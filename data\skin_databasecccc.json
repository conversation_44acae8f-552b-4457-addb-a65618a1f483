{"1000": {"name": "<PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "1001": {"name": "<PERSON><PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "1002": {"name": "Red Riding Annie", "champion": "<PERSON>", "rarity": "kNoRarity"}, "1003": {"name": "<PERSON> in Wonderland", "champion": "<PERSON>", "rarity": "kLegendary"}, "1004": {"name": "Prom Queen <PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "1005": {"name": "Frostfire Annie", "champion": "<PERSON>", "rarity": "kNoRarity"}, "1006": {"name": "Reverse Annie", "champion": "<PERSON>", "rarity": "kNoRarity"}, "1007": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "1008": {"name": "Panda <PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "1009": {"name": "Sweetheart Annie", "champion": "<PERSON>", "rarity": "kNoRarity"}, "1010": {"name": "Hextech Annie", "champion": "<PERSON>", "rarity": "kMythic"}, "1011": {"name": "Super Galaxy Annie", "champion": "<PERSON>", "rarity": "kEpic"}, "1012": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON>", "rarity": "kMythic"}, "1013": {"name": "Lunar Beast Annie", "champion": "<PERSON>", "rarity": "kEpic"}, "1022": {"name": "Cafe Cuties Annie", "champion": "<PERSON>", "rarity": "kEpic"}, "1031": {"name": "Fright Night Annie", "champion": "<PERSON>", "rarity": "kEpic"}, "1040": {"name": "Winter<PERSON>sed Annie", "champion": "<PERSON>", "rarity": "kEpic"}, "1050": {"name": "Battle Princess Annie", "champion": "<PERSON>", "rarity": "kEpic"}, "2000": {"name": "<PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "2001": {"name": "Forsaken Olaf", "champion": "<PERSON>", "rarity": "kNoRarity"}, "2002": {"name": "Glacial Olaf", "champion": "<PERSON>", "rarity": "kNoRarity"}, "2003": {"name": "Brolaf", "champion": "<PERSON>", "rarity": "kLegendary"}, "2004": {"name": "Pentakill Olaf", "champion": "<PERSON>", "rarity": "kNoRarity"}, "2005": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "2006": {"name": "<PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "2015": {"name": "SKT T1 Olaf", "champion": "<PERSON>", "rarity": "kEpic"}, "2016": {"name": "Dragonslayer <PERSON>", "champion": "<PERSON>", "rarity": "kEpic"}, "2025": {"name": "Sentinel Olaf", "champion": "<PERSON>", "rarity": "kEpic"}, "2035": {"name": "Pentakill III: Lost Chapter Olaf", "champion": "<PERSON>", "rarity": "kEpic"}, "2044": {"name": "Infernal Olaf", "champion": "<PERSON>", "rarity": "kEpic"}, "3000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "3001": {"name": "Enchanted <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "3002": {"name": "Hextech Galio", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "3003": {"name": "Commando Galio", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "3004": {"name": "Gatekeeper <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "3005": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "3006": {"name": "Bird<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "3013": {"name": "Infernal Galio", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "3019": {"name": "Dragon Guardian Galio", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "3028": {"name": "Myth<PERSON> <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "4000": {"name": "Twisted Fate", "champion": "Twisted Fate", "rarity": "kNoRarity"}, "4001": {"name": "PAX Twisted Fate", "champion": "TwistedFate", "rarity": "kNoRarity"}, "4002": {"name": "Jack of Hearts Twisted Fate", "champion": "TwistedFate", "rarity": "kNoRarity"}, "4003": {"name": "The Magnificent Twisted Fate", "champion": "TwistedFate", "rarity": "kLegendary"}, "4004": {"name": "Tango Twisted Fate", "champion": "TwistedFate", "rarity": "kNoRarity"}, "4005": {"name": "High Noon Twisted Fate", "champion": "TwistedFate", "rarity": "kNoRarity"}, "4006": {"name": "Musketeer T<PERSON><PERSON>", "champion": "TwistedFate", "rarity": "kNoRarity"}, "4007": {"name": "Underworld Twisted Fate", "champion": "TwistedFate", "rarity": "kNoRarity"}, "4008": {"name": "Red Card Twisted Fate", "champion": "TwistedFate", "rarity": "kNoRarity"}, "4009": {"name": "Cutpurse Twisted Fate", "champion": "TwistedFate", "rarity": "kNoRarity"}, "4010": {"name": "<PERSON> Moon Twisted Fate", "champion": "TwistedFate", "rarity": "kEpic"}, "4011": {"name": "Pulsefire Twisted Fate", "champion": "TwistedFate", "rarity": "kEpic"}, "4013": {"name": "Odyssey Twisted Fate", "champion": "TwistedFate", "rarity": "kEpic"}, "4023": {"name": "DWG Twisted Fate", "champion": "TwistedFate", "rarity": "kEpic"}, "4025": {"name": "Crime City Nightmare Twisted Fate", "champion": "TwistedFate", "rarity": "kEpic"}, "4036": {"name": "Space Groove Twisted Fate", "champion": "TwistedFate", "rarity": "kEpic"}, "5000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "5001": {"name": "Commando <PERSON><PERSON>", "champion": "XinZhao", "rarity": "kNoRarity"}, "5002": {"name": "Imperial <PERSON><PERSON>", "champion": "XinZhao", "rarity": "kNoRarity"}, "5003": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "XinZhao", "rarity": "kNoRarity"}, "5004": {"name": "Winged <PERSON><PERSON><PERSON>", "champion": "XinZhao", "rarity": "kNoRarity"}, "5005": {"name": "Warring Kingdoms Xin Zhao", "champion": "XinZhao", "rarity": "kEpic"}, "5006": {"name": "Secret Agent <PERSON><PERSON>", "champion": "XinZhao", "rarity": "kNoRarity"}, "5013": {"name": "Dragonslayer <PERSON><PERSON>", "champion": "XinZhao", "rarity": "kEpic"}, "5020": {"name": "Cosmic Defender <PERSON><PERSON>", "champion": "XinZhao", "rarity": "kEpic"}, "5027": {"name": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "champion": "XinZhao", "rarity": "kEpic"}, "5036": {"name": "Firecracker <PERSON><PERSON>", "champion": "XinZhao", "rarity": "kEpic"}, "6000": {"name": "Urgot", "champion": "Urgot", "rarity": "kNoRarity"}, "6001": {"name": "Giant Enemy Crabgot", "champion": "Urgot", "rarity": "kNoRarity"}, "6002": {"name": "<PERSON>", "champion": "Urgot", "rarity": "kNoRarity"}, "6003": {"name": "Battlecast Urgot", "champion": "Urgot", "rarity": "kEpic"}, "6009": {"name": "High Noon Urgot", "champion": "Urgot", "rarity": "kEpic"}, "6015": {"name": "Pajama Guardian Cosplay Urgot", "champion": "Urgot", "rarity": "kEpic"}, "6023": {"name": "Fright Night Urgot", "champion": "Urgot", "rarity": "kEpic"}, "7000": {"name": "LeBlanc", "champion": "LeBlanc", "rarity": "kNoRarity"}, "7001": {"name": "<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "7002": {"name": "Prestigious LeBlanc", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "7003": {"name": "Mistletoe LeBlanc", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "7004": {"name": "Ravenborn LeBlanc", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "7005": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "7012": {"name": "Program LeBlanc", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "7019": {"name": "iG <PERSON>c", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "7020": {"name": "Coven <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "7029": {"name": "Worlds 2020 LeBlanc", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "7033": {"name": "Prestige Coven LeBlanc", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "7035": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "7045": {"name": "Bewitching LeBlanc", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "7055": {"name": "Risen Legend <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "8000": {"name": "Vladimir", "champion": "Vladimir", "rarity": "kNoRarity"}, "8001": {"name": "Count <PERSON>", "champion": "Vladimir", "rarity": "kNoRarity"}, "8002": {"name": "Marquis <PERSON>", "champion": "Vladimir", "rarity": "kNoRarity"}, "8003": {"name": "Nosferatu Vladimir", "champion": "Vladimir", "rarity": "kNoRarity"}, "8004": {"name": "<PERSON><PERSON>", "champion": "Vladimir", "rarity": "kNoRarity"}, "8005": {"name": "Blood Lord Vladimir", "champion": "Vladimir", "rarity": "kLegendary"}, "8006": {"name": "Souls<PERSON><PERSON><PERSON>", "champion": "Vladimir", "rarity": "kEpic"}, "8007": {"name": "Academy Vladimir", "champion": "Vladimir", "rarity": "kNoRarity"}, "8008": {"name": "Dark Waters Vladimir", "champion": "Vladimir", "rarity": "kEpic"}, "8014": {"name": "Nightbringer Vladimir", "champion": "Vladimir", "rarity": "kEpic"}, "8021": {"name": "Cosmic Devourer Vladimir", "champion": "Vladimir", "rarity": "kEpic"}, "8030": {"name": "Cafe Cuties Vladimir", "champion": "Vladimir", "rarity": "kEpic"}, "8039": {"name": "Broken Covenant Vladimir", "champion": "Vladimir", "rarity": "kEpic"}, "8048": {"name": "Masque of the Black Rose Vladimir", "champion": "Vladimir", "rarity": "kNoRarity"}, "9000": {"name": "Fiddlesticks", "champion": "Fiddlesticks", "rarity": "kNoRarity"}, "9001": {"name": "Spectral Fiddlesticks", "champion": "Fiddlesticks", "rarity": "kNoRarity"}, "9002": {"name": "Union Jack Fiddlesticks", "champion": "Fiddlesticks", "rarity": "kNoRarity"}, "9003": {"name": "Bandito Fiddlesticks", "champion": "Fiddlesticks", "rarity": "kNoRarity"}, "9004": {"name": "Pumpkinhead Fiddlesticks", "champion": "Fiddlesticks", "rarity": "kNoRarity"}, "9005": {"name": "Fiddle Me Timbers", "champion": "Fiddlesticks", "rarity": "kNoRarity"}, "9006": {"name": "Surprise Party Fiddlesticks", "champion": "Fiddlesticks", "rarity": "kLegendary"}, "9007": {"name": "Dark Candy Fiddlesticks", "champion": "Fiddlesticks", "rarity": "kNoRarity"}, "9008": {"name": "Risen Fiddlesticks", "champion": "Fiddlesticks", "rarity": "kEpic"}, "9009": {"name": "Praetorian Fiddlesticks", "champion": "Fiddlesticks", "rarity": "kEpic"}, "9027": {"name": "Star Nemesis Fiddlesticks", "champion": "Fiddlesticks", "rarity": "kEpic"}, "9037": {"name": "Blood Moon Fiddlesticks", "champion": "Fiddlesticks", "rarity": "kEpic"}, "10000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "10001": {"name": "<PERSON>le", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "10002": {"name": "Viridian <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "10003": {"name": "Transcended <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "10004": {"name": "<PERSON><PERSON> Kayle", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "10005": {"name": "Judgment <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "10006": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "10007": {"name": "Riot Kayle", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "10008": {"name": "Iron Inquisitor <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "10009": {"name": "Pentakill Kayle", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "10015": {"name": "PsyO<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "10024": {"name": "Dragonslayer <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "10033": {"name": "Pentakill III: Lost Chapter Kayle", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "10042": {"name": "Sun-Eat<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "10057": {"name": "Immortal <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "10066": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "10067": {"name": "Prestige Empyrea<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "11000": {"name": "Master <PERSON>", "champion": "Master <PERSON>", "rarity": "kNoRarity"}, "11001": {"name": "Assassin Master <PERSON>", "champion": "MasterYi", "rarity": "kNoRarity"}, "11002": {"name": "<PERSON><PERSON> Master <PERSON>", "champion": "MasterYi", "rarity": "kNoRarity"}, "11003": {"name": "Ionia <PERSON>", "champion": "MasterYi", "rarity": "kNoRarity"}, "11004": {"name": "Samurai Yi", "champion": "MasterYi", "rarity": "kNoRarity"}, "11005": {"name": "Headhunter Master <PERSON>", "champion": "MasterYi", "rarity": "kNoRarity"}, "11009": {"name": "PROJECT: Yi", "champion": "MasterYi", "rarity": "kLegendary"}, "11010": {"name": "Cosmic Blade Master Yi", "champion": "MasterYi", "rarity": "kEpic"}, "11011": {"name": "Eternal Sword Yi", "champion": "MasterYi", "rarity": "kEpic"}, "11017": {"name": "<PERSON>", "champion": "MasterYi", "rarity": "kEpic"}, "11024": {"name": "Blood Moon Master Yi", "champion": "MasterYi", "rarity": "kEpic"}, "11033": {"name": "PsyOps Master Yi", "champion": "MasterYi", "rarity": "kEpic"}, "11042": {"name": "<PERSON><PERSON><PERSON>", "champion": "MasterYi", "rarity": "kEpic"}, "11052": {"name": "Spirit Blossom Master Yi", "champion": "MasterYi", "rarity": "kEpic"}, "11053": {"name": "Prestige Spirit Blossom Master Yi", "champion": "MasterYi", "rarity": "kMythic"}, "11089": {"name": "Inkshadow Master Yi", "champion": "MasterYi", "rarity": "kLegendary"}, "11096": {"name": "Heavenscale Master Yi", "champion": "MasterYi", "rarity": "kEpic"}, "11106": {"name": "Victorious Master <PERSON>", "champion": "MasterYi", "rarity": "kNoRarity"}, "12000": {"name": "Alistar", "champion": "Alistar", "rarity": "kNoRarity"}, "12001": {"name": "Black Alistar", "champion": "Alistar", "rarity": "kNoRarity"}, "12002": {"name": "Golden Alistar", "champion": "Alistar", "rarity": "kNoRarity"}, "12003": {"name": "<PERSON><PERSON>", "champion": "Alistar", "rarity": "kNoRarity"}, "12004": {"name": "Longhorn Alistar", "champion": "Alistar", "rarity": "kNoRarity"}, "12005": {"name": "Unchained <PERSON><PERSON>", "champion": "Alistar", "rarity": "kNoRarity"}, "12006": {"name": "Infernal Alistar", "champion": "Alistar", "rarity": "kNoRarity"}, "12007": {"name": "<PERSON>weeper <PERSON>", "champion": "Alistar", "rarity": "kNoRarity"}, "12008": {"name": "<PERSON><PERSON><PERSON>", "champion": "Alistar", "rarity": "kNoRarity"}, "12009": {"name": "SKT T1 Alistar", "champion": "Alistar", "rarity": "kNoRarity"}, "12010": {"name": "<PERSON><PERSON> Cow Alistar", "champion": "Alistar", "rarity": "kNoRarity"}, "12019": {"name": "Hextech Alistar", "champion": "Alistar", "rarity": "kMythic"}, "12020": {"name": "<PERSON><PERSON><PERSON>", "champion": "Alistar", "rarity": "kRare"}, "12022": {"name": "Blackfrost Alistar", "champion": "Alistar", "rarity": "kEpic"}, "12029": {"name": "Lunar Beast Alistar", "champion": "Alistar", "rarity": "kEpic"}, "13000": {"name": "Ryze", "champion": "Ryze", "rarity": "kNoRarity"}, "13001": {"name": "Young Ryze", "champion": "Ryze", "rarity": "kNoRarity"}, "13002": {"name": "Tribal Ryze", "champion": "Ryze", "rarity": "kNoRarity"}, "13003": {"name": "Uncle <PERSON><PERSON><PERSON>", "champion": "Ryze", "rarity": "kNoRarity"}, "13004": {"name": "Triumphant <PERSON>", "champion": "Ryze", "rarity": "kNoRarity"}, "13005": {"name": "Professor <PERSON><PERSON><PERSON>", "champion": "Ryze", "rarity": "kNoRarity"}, "13006": {"name": "Zombie Ryze", "champion": "Ryze", "rarity": "kNoRarity"}, "13007": {"name": "Dark Crystal Ryze", "champion": "Ryze", "rarity": "kNoRarity"}, "13008": {"name": "Pirate Ryze", "champion": "Ryze", "rarity": "kNoRarity"}, "13009": {"name": "<PERSON><PERSON><PERSON>beard", "champion": "Ryze", "rarity": "kNoRarity"}, "13010": {"name": "SKT T1 Ryze", "champion": "Ryze", "rarity": "kNoRarity"}, "13011": {"name": "Worlds 2019 Ryze", "champion": "Ryze", "rarity": "kEpic"}, "13013": {"name": "Guardian of the Sands Ryze", "champion": "Ryze", "rarity": "kEpic"}, "13020": {"name": "<PERSON><PERSON>", "champion": "Ryze", "rarity": "kEpic"}, "14000": {"name": "Sion", "champion": "Sion", "rarity": "kNoRarity"}, "14001": {"name": "Hextech Sion", "champion": "Sion", "rarity": "kNoRarity"}, "14002": {"name": "Barbarian Sion", "champion": "Sion", "rarity": "kNoRarity"}, "14003": {"name": "Lumber<PERSON>", "champion": "Sion", "rarity": "kNoRarity"}, "14004": {"name": "<PERSON><PERSON><PERSON>", "champion": "Sion", "rarity": "kNoRarity"}, "14005": {"name": "<PERSON><PERSON> <PERSON>", "champion": "Sion", "rarity": "kLegendary"}, "14014": {"name": "Worldbreaker <PERSON><PERSON>", "champion": "Sion", "rarity": "kEpic"}, "14022": {"name": "Blackfrost Sion", "champion": "Sion", "rarity": "kEpic"}, "14030": {"name": "High Noon Sion", "champion": "Sion", "rarity": "kEpic"}, "14040": {"name": "Cosmic Pa<PERSON><PERSON>", "champion": "Sion", "rarity": "kEpic"}, "14049": {"name": "Grand Reckoning Sion", "champion": "Sion", "rarity": "kNoRarity"}, "15000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "15001": {"name": "Warrior Princess <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "15002": {"name": "Spectacular Sivir", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "15003": {"name": "Hunt<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "15004": {"name": "Bandit Sivir", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "15005": {"name": "PAX Sivir", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "15006": {"name": "Snowstorm Sivir", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "15007": {"name": "Warden <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "15008": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "15009": {"name": "Neo PAX Sivir", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "15010": {"name": "Pizza Delivery Sivir", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "15016": {"name": "Blood Moon Sivir", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "15025": {"name": "Odyssey Sivir", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "15034": {"name": "Cafe Cuties Sivir", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "15043": {"name": "Solar Eclipse Sivir", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "15050": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "15051": {"name": "Prestige Mythmaker Sivir", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "15061": {"name": "Primal Ambush <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "15070": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "16000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "16001": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "16002": {"name": "Divine Soraka", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "16003": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "16004": {"name": "Reaper Soraka", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "16005": {"name": "Order of the Banana Soraka", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "16006": {"name": "Program Soraka", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "16007": {"name": "Star Guardian Soraka", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "16008": {"name": "Pajama Guardian <PERSON>rak<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "16009": {"name": "Winter Wonder Soraka", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "16015": {"name": "<PERSON><PERSON><PERSON> Soraka", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "16016": {"name": "Nightbringer Soraka", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "16017": {"name": "Prestige Star Guardian Soraka", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "16018": {"name": "Cafe Cuties Soraka", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "16027": {"name": "Spirit Blossom <PERSON><PERSON>a", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "16037": {"name": "Immortal Journey <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "16044": {"name": "Faerie Court Soraka", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "17000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "17001": {"name": "<PERSON> Elf <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "17002": {"name": "Recon Teemo", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "17003": {"name": "Badger <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "17004": {"name": "Astronaut <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "17005": {"name": "Cottontail Teemo", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "17006": {"name": "Super Teemo", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "17007": {"name": "Panda Teemo", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "17008": {"name": "Omega Squad Teemo", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "17014": {"name": "Little Devil Teemo", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "17018": {"name": "Beemo", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "17025": {"name": "Spirit Blossom Te<PERSON>o", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "17027": {"name": "Prestige Spirit Blossom Teemo", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "17037": {"name": "Firecracker Teemo", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "17047": {"name": "Space Groove Teemo", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "18000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "18001": {"name": "Riot Girl <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "18002": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "18003": {"name": "Firefighter <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "18004": {"name": "Guerilla Tristana", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "18005": {"name": "Buccane<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "18006": {"name": "Rocket Girl Tristan<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "18010": {"name": "Dragon Trainer <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "18011": {"name": "Bewitching <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "18012": {"name": "Omega Squad Tristana", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "18024": {"name": "Little <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "18033": {"name": "<PERSON><PERSON> Cosplay <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "18040": {"name": "Hextech Tristana", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "18041": {"name": "Firecracker Tristana", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "18051": {"name": "Spirit Blossom <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "18061": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "19000": {"name": "Warwick", "champion": "Warwick", "rarity": "kNoRarity"}, "19001": {"name": "Grey Warwick", "champion": "Warwick", "rarity": "kNoRarity"}, "19002": {"name": "Urf the Manatee", "champion": "Warwick", "rarity": "kNoRarity"}, "19003": {"name": "Big Bad Warwick", "champion": "Warwick", "rarity": "kNoRarity"}, "19004": {"name": "<PERSON><PERSON>", "champion": "Warwick", "rarity": "kNoRarity"}, "19005": {"name": "Feral Warwick", "champion": "Warwick", "rarity": "kNoRarity"}, "19006": {"name": "Firefang Warwick", "champion": "Warwick", "rarity": "kNoRarity"}, "19007": {"name": "<PERSON><PERSON>na <PERSON>", "champion": "Warwick", "rarity": "kNoRarity"}, "19008": {"name": "<PERSON><PERSON><PERSON>", "champion": "Warwick", "rarity": "kNoRarity"}, "19009": {"name": "Urfwick", "champion": "Warwick", "rarity": "kNoRarity"}, "19010": {"name": "Lunar Guardian Warwick", "champion": "Warwick", "rarity": "kEpic"}, "19016": {"name": "PROJECT: Warwick", "champion": "Warwick", "rarity": "kEpic"}, "19035": {"name": "Old God Warwick", "champion": "Warwick", "rarity": "kEpic"}, "19045": {"name": "Winterblessed Warwick", "champion": "Warwick", "rarity": "kEpic"}, "19046": {"name": "Prestige Winterblessed Warwick", "champion": "Warwick", "rarity": "kMythic"}, "19056": {"name": "<PERSON><PERSON>", "champion": "Warwick", "rarity": "kEpic"}, "20000": {"name": "Nunu & Willump", "champion": "Nunu & Willump", "rarity": "kNoRarity"}, "20001": {"name": "Sasquatch Nunu & Willump", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "20002": {"name": "Workshop Nunu & Willump", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "20003": {"name": "Grungy Nunu & Willump", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "20004": {"name": "Nunu & Willump Bot", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "20005": {"name": "Demolisher Nunu & Willump", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "20006": {"name": "TPA Nunu & Willump", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "20007": {"name": "Zombie Nunu & Willump", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "20008": {"name": "Papercraft Nunu & Willump", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "20016": {"name": "Space Groove Nunu & Willump", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "20026": {"name": "Nunu & Beelump", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "20035": {"name": "Cosmic Paladins Nunu & Willump", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "20044": {"name": "Fright Night Nunu & Willump", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "21000": {"name": "Miss Fortune", "champion": "Miss Fortune", "rarity": "kNoRarity"}, "21001": {"name": "Cowgirl Miss <PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "21002": {"name": "Waterloo Miss Fortune", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "21003": {"name": "Secret Agent <PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "21004": {"name": "<PERSON> Cane <PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "21005": {"name": "Road Warrior Miss <PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "21006": {"name": "Crime City Miss Fortune", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "21007": {"name": "Arcade Miss Fortune", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "21008": {"name": "Captain <PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "21009": {"name": "Pool Party Miss Fortune", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "21015": {"name": "Star Guardian Miss Fortune", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "21016": {"name": "Gun Goddess Miss Fortune", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kUltimate"}, "21017": {"name": "Pajama Guardian Miss Fortune", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "21018": {"name": "Bewitching Miss <PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "21020": {"name": "Prestige Bewitching Miss Fortune", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "21021": {"name": "Ruined <PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "21031": {"name": "Battle Bunny Miss <PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "21032": {"name": "Admiral <PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "21033": {"name": "Prestige Bewitching Miss Fortune (2022)", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "21040": {"name": "Broken Covenant Miss Fortune", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "21041": {"name": "Prestige Broken Covenant Miss Fortune", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "21050": {"name": "Porcelain Miss Fortune", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "21060": {"name": "Battle Queen Miss Fortune", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "22000": {"name": "Ashe", "champion": "Ashe", "rarity": "kNoRarity"}, "22001": {"name": "Frel<PERSON> Ashe", "champion": "Ashe", "rarity": "kNoRarity"}, "22002": {"name": "Sherwood Forest Ashe", "champion": "Ashe", "rarity": "kNoRarity"}, "22003": {"name": "Woad <PERSON>", "champion": "Ashe", "rarity": "kNoRarity"}, "22004": {"name": "<PERSON>", "champion": "Ashe", "rarity": "kNoRarity"}, "22005": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "Ashe", "rarity": "kNoRarity"}, "22006": {"name": "<PERSON><PERSON><PERSON>", "champion": "Ashe", "rarity": "kNoRarity"}, "22007": {"name": "<PERSON><PERSON><PERSON>", "champion": "Ashe", "rarity": "kNoRarity"}, "22008": {"name": "PROJECT: Ashe", "champion": "Ashe", "rarity": "kLegendary"}, "22009": {"name": "Worlds 2017 Ashe", "champion": "Ashe", "rarity": "kEpic"}, "22011": {"name": "Cosmic Queen Ashe", "champion": "Ashe", "rarity": "kEpic"}, "22017": {"name": "High Noon Ashe", "champion": "Ashe", "rarity": "kLegendary"}, "22023": {"name": "Fae <PERSON> Ashe", "champion": "Ashe", "rarity": "kEpic"}, "22032": {"name": "Coven <PERSON>", "champion": "Ashe", "rarity": "kEpic"}, "22043": {"name": "<PERSON>", "champion": "Ashe", "rarity": "kEpic"}, "22052": {"name": "Lunar Empress <PERSON>", "champion": "Ashe", "rarity": "kEpic"}, "22063": {"name": "DRX Ashe", "champion": "Ashe", "rarity": "kEpic"}, "22065": {"name": "<PERSON><PERSON>", "champion": "Ashe", "rarity": "kMythic"}, "22067": {"name": "Infernal Ashe", "champion": "Ashe", "rarity": "kEpic"}, "23000": {"name": "Tryndamere", "champion": "Tryndamere", "rarity": "kNoRarity"}, "23001": {"name": "Highland Tryndamere", "champion": "Tryndamere", "rarity": "kNoRarity"}, "23002": {"name": "King <PERSON><PERSON>", "champion": "Tryndamere", "rarity": "kNoRarity"}, "23003": {"name": "Viking Tryndamere", "champion": "Tryndamere", "rarity": "kNoRarity"}, "23004": {"name": "Demonblade Tryndamere", "champion": "Tryndamere", "rarity": "kLegendary"}, "23005": {"name": "Sultan Tryndamere", "champion": "Tryndamere", "rarity": "kNoRarity"}, "23006": {"name": "Warring Kingdoms Tryndamere", "champion": "Tryndamere", "rarity": "kNoRarity"}, "23007": {"name": "Nightmare Tryndamere", "champion": "Tryndamere", "rarity": "kNoRarity"}, "23008": {"name": "Beast Hunter Tryndamere", "champion": "Tryndamere", "rarity": "kNoRarity"}, "23009": {"name": "Chemtech Tryndamere", "champion": "Tryndamere", "rarity": "kNoRarity"}, "23010": {"name": "Blood Moon Tryndamere", "champion": "Tryndamere", "rarity": "kEpic"}, "23018": {"name": "Nightbringer Tryndamere", "champion": "Tryndamere", "rarity": "kEpic"}, "23027": {"name": "<PERSON><PERSON>", "champion": "Tryndamere", "rarity": "kNoRarity"}, "24000": {"name": "Jax", "champion": "Jax", "rarity": "kNoRarity"}, "24001": {"name": "The Mighty Jax", "champion": "Jax", "rarity": "kNoRarity"}, "24002": {"name": "<PERSON><PERSON>", "champion": "Jax", "rarity": "kNoRarity"}, "24003": {"name": "<PERSON><PERSON>", "champion": "Jax", "rarity": "kNoRarity"}, "24004": {"name": "PAX Jax", "champion": "Jax", "rarity": "kNoRarity"}, "24005": {"name": "<PERSON><PERSON>", "champion": "Jax", "rarity": "kNoRarity"}, "24006": {"name": "Temple Jax", "champion": "Jax", "rarity": "kNoRarity"}, "24007": {"name": "Nemesis Jax", "champion": "Jax", "rarity": "kNoRarity"}, "24008": {"name": "SKT T1 Jax", "champion": "Jax", "rarity": "kNoRarity"}, "24012": {"name": "<PERSON>", "champion": "Jax", "rarity": "kNoRarity"}, "24013": {"name": "God Staff Jax", "champion": "Jax", "rarity": "kEpic"}, "24014": {"name": "Mecha Kingdoms Jax", "champion": "Jax", "rarity": "kLegendary"}, "24020": {"name": "Conquer<PERSON>", "champion": "Jax", "rarity": "kEpic"}, "24021": {"name": "Prestige Conqueror Jax", "champion": "Jax", "rarity": "kMythic"}, "24022": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "Jax", "rarity": "kEpic"}, "24032": {"name": "Neo PAX Jax", "champion": "Jax", "rarity": "kMythic"}, "24033": {"name": "PROJECT: Jax", "champion": "Jax", "rarity": "kEpic"}, "25000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "25001": {"name": "Exiled <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "25002": {"name": "Sinful Succulence Morgana", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "25003": {"name": "Blade Mistress <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "25004": {"name": "Blackthorn Morgan<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "25005": {"name": "Ghost Bride <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "25006": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "25010": {"name": "Lunar Wraith Morgan<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "25011": {"name": "Bewitching <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "25017": {"name": "Majestic Empress <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "25026": {"name": "Coven Morgana", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "25039": {"name": "Dawnbringer Morgana", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "25041": {"name": "Prestige Bewitching Morgana", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "25050": {"name": "Star Nemesis Morgana", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "25060": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "25070": {"name": "Porcelain Morgana", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "26000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "26001": {"name": "Old Saint Z<PERSON>an", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "26002": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "26003": {"name": "Shurima Desert Zilean", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "26004": {"name": "Time Machine Zilean", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "26005": {"name": "Blood Moon Zilean", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "26006": {"name": "<PERSON> Rush Z<PERSON>an", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "26014": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "27000": {"name": "Singed", "champion": "Singed", "rarity": "kNoRarity"}, "27001": {"name": "Riot Squad Singed", "champion": "Singed", "rarity": "kNoRarity"}, "27002": {"name": "Hextech Singed", "champion": "Singed", "rarity": "kNoRarity"}, "27003": {"name": "<PERSON><PERSON> Singed", "champion": "Singed", "rarity": "kNoRarity"}, "27004": {"name": "Mad Scientist Singed", "champion": "Singed", "rarity": "kNoRarity"}, "27005": {"name": "Augmented Singed", "champion": "Singed", "rarity": "kNoRarity"}, "27006": {"name": "Snow Day Singed", "champion": "Singed", "rarity": "kNoRarity"}, "27007": {"name": "SSW Singed", "champion": "Singed", "rarity": "kNoRarity"}, "27008": {"name": "Black Scourge Singed", "champion": "Singed", "rarity": "kNoRarity"}, "27009": {"name": "Beekeeper Singed", "champion": "Singed", "rarity": "kEpic"}, "27010": {"name": "Resistance Singed", "champion": "Singed", "rarity": "kEpic"}, "27019": {"name": "Astronaut Singed", "champion": "Singed", "rarity": "kEpic"}, "27028": {"name": "<PERSON><PERSON> Shimmer Lab Singed", "champion": "Singed", "rarity": "kNoRarity"}, "28000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "28001": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "28002": {"name": "Masquerade Evelynn", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "28003": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "28004": {"name": "Safecracker <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "28005": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "28006": {"name": "K/DA Evelynn", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "28007": {"name": "Prestige K/DA Evelynn", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "28008": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "28015": {"name": "K/DA ALL OUT Evelynn", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "28024": {"name": "Coven <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "28031": {"name": "Prestige K/DA Evelynn (2022)", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "28032": {"name": "Spirit Blossom <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "28042": {"name": "Soul Fighter Evelyn<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "28052": {"name": "High Noon Evelynn", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "28053": {"name": "Prestige High Noon Evelynn", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "29000": {"name": "Twitch", "champion": "Twitch", "rarity": "kNoRarity"}, "29001": {"name": "Kingpin Twitch", "champion": "Twitch", "rarity": "kNoRarity"}, "29002": {"name": "Whistler Village Twitch", "champion": "Twitch", "rarity": "kNoRarity"}, "29003": {"name": "Medieval Twitch", "champion": "Twitch", "rarity": "kNoRarity"}, "29004": {"name": "Crime City Twitch", "champion": "Twitch", "rarity": "kNoRarity"}, "29005": {"name": "<PERSON><PERSON>", "champion": "Twitch", "rarity": "kNoRarity"}, "29006": {"name": "Pickpocket Twitch", "champion": "Twitch", "rarity": "kNoRarity"}, "29007": {"name": "SSW Twitch", "champion": "Twitch", "rarity": "kNoRarity"}, "29008": {"name": "Omega Squad Twitch", "champion": "Twitch", "rarity": "kEpic"}, "29012": {"name": "Ice King Twitch", "champion": "Twitch", "rarity": "kEpic"}, "29027": {"name": "Twitch Shadowfoot", "champion": "Twitch", "rarity": "kEpic"}, "29036": {"name": "Dragonslayer Twitch", "champion": "Twitch", "rarity": "kEpic"}, "29045": {"name": "High Noon Twitch", "champion": "Twitch", "rarity": "kEpic"}, "29055": {"name": "Cheddar Chief <PERSON>", "champion": "Twitch", "rarity": "kEpic"}, "30000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "30001": {"name": "<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "30002": {"name": "Statue of Karthus", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "30003": {"name": "<PERSON><PERSON> <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "30004": {"name": "Penta<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "30005": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "30009": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "30010": {"name": "Infernal Karthus", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "30017": {"name": "Pentakill III: Lost Chapter Karthus", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "30026": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "31000": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "31001": {"name": "<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "31002": {"name": "Gentleman <PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "31003": {"name": "Loch Ness <PERSON>ath", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "31004": {"name": "Jurassic Cho'Gath", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "31005": {"name": "Battlecast Prime <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "31006": {"name": "Prehistoric <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "31007": {"name": "Dark Star Cho'Gath", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "31014": {"name": "Shan <PERSON> Scrolls Cho'Gath", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "31023": {"name": "Broken Covenant Cho'Gath", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "31032": {"name": "Toy Terror Cho'Gath", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "32000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "32001": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "32002": {"name": "Vancouver Amumu", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "32003": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "32004": {"name": "Re-Gifted <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "32005": {"name": "Almost-Prom King <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "32006": {"name": "Little Knight Amumu", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "32007": {"name": "Sad Robot <PERSON>u", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "32008": {"name": "Surprise Party Amumu", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "32017": {"name": "Infernal Amumu", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "32023": {"name": "Hextech Amumu", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "32024": {"name": "Pumpkin Prince <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "32034": {"name": "Po<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "32044": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "32053": {"name": "Dumpling <PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "33000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "33001": {"name": "King <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "33002": {"name": "Chrome Rammus", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "33003": {"name": "Molten Rammus", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "33004": {"name": "Freljord Rammus", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "33005": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "33006": {"name": "Full Metal Rammus", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "33007": {"name": "Guardian of the Sands Rammus", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "33008": {"name": "<PERSON>we<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "33016": {"name": "Hextech Rammus", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "33017": {"name": "Astronaut <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "33026": {"name": "Durian Defender <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "34000": {"name": "An<PERSON><PERSON>", "champion": "An<PERSON><PERSON>", "rarity": "kNoRarity"}, "34001": {"name": "Team Spirit Anivia", "champion": "An<PERSON><PERSON>", "rarity": "kNoRarity"}, "34002": {"name": "Bird of Prey Anivia", "champion": "An<PERSON><PERSON>", "rarity": "kNoRarity"}, "34003": {"name": "Noxus Hunter Anivia", "champion": "An<PERSON><PERSON>", "rarity": "kNoRarity"}, "34004": {"name": "Hextech Anivia", "champion": "An<PERSON><PERSON>", "rarity": "kNoRarity"}, "34005": {"name": "Blackfrost Anivia", "champion": "An<PERSON><PERSON>", "rarity": "kLegendary"}, "34006": {"name": "Prehistoric Anivia", "champion": "An<PERSON><PERSON>", "rarity": "kNoRarity"}, "34007": {"name": "Festival Queen <PERSON><PERSON><PERSON>", "champion": "An<PERSON><PERSON>", "rarity": "kNoRarity"}, "34008": {"name": "Papercraft Anivia", "champion": "An<PERSON><PERSON>", "rarity": "kEpic"}, "34017": {"name": "Cosmic Flight Anivia", "champion": "An<PERSON><PERSON>", "rarity": "kEpic"}, "34027": {"name": "Divine Phoenix Anivia", "champion": "An<PERSON><PERSON>", "rarity": "kEpic"}, "34037": {"name": "Bewitching Batnivia", "champion": "An<PERSON><PERSON>", "rarity": "kEpic"}, "34046": {"name": "<PERSON><PERSON>", "champion": "An<PERSON><PERSON>", "rarity": "kNoRarity"}, "35000": {"name": "Shaco", "champion": "Shaco", "rarity": "kNoRarity"}, "35001": {"name": "<PERSON>", "champion": "Shaco", "rarity": "kNoRarity"}, "35002": {"name": "Royal Shaco", "champion": "Shaco", "rarity": "kNoRarity"}, "35003": {"name": "Nutcracko", "champion": "Shaco", "rarity": "kNoRarity"}, "35004": {"name": "Workshop Shaco", "champion": "Shaco", "rarity": "kNoRarity"}, "35005": {"name": "Asylum Shaco", "champion": "Shaco", "rarity": "kNoRarity"}, "35006": {"name": "Masked <PERSON>", "champion": "Shaco", "rarity": "kNoRarity"}, "35007": {"name": "Wild Card Shaco", "champion": "Shaco", "rarity": "kNoRarity"}, "35008": {"name": "Dark Star Shaco", "champion": "Shaco", "rarity": "kEpic"}, "35015": {"name": "Arcanist <PERSON><PERSON><PERSON>", "champion": "Shaco", "rarity": "kEpic"}, "35023": {"name": "Crime City Nightmare Shaco", "champion": "Shaco", "rarity": "kEpic"}, "35033": {"name": "Winterblessed Shaco", "champion": "Shaco", "rarity": "kEpic"}, "35043": {"name": "Soul Fighter Shaco", "champion": "Shaco", "rarity": "kEpic"}, "35044": {"name": "Prestige Soul Fighter Shaco", "champion": "Shaco", "rarity": "kMythic"}, "35054": {"name": "Fright Night Shaco", "champion": "Shaco", "rarity": "kEpic"}, "36000": {"name": "Dr. <PERSON>", "champion": "Dr. <PERSON>", "rarity": "kNoRarity"}, "36001": {"name": "Toxic Dr<PERSON>", "champion": "DrMundo", "rarity": "kNoRarity"}, "36002": {"name": "Mr. <PERSON>", "champion": "DrMundo", "rarity": "kNoRarity"}, "36003": {"name": "Corporate Mundo", "champion": "DrMundo", "rarity": "kLegendary"}, "36004": {"name": "Mundo Mundo", "champion": "DrMundo", "rarity": "kNoRarity"}, "36005": {"name": "Executioner <PERSON><PERSON>", "champion": "DrMundo", "rarity": "kNoRarity"}, "36006": {"name": "Rageborn Mundo", "champion": "DrMundo", "rarity": "kRare"}, "36007": {"name": "TPA Mundo", "champion": "DrMundo", "rarity": "kNoRarity"}, "36008": {"name": "Pool Party Mundo", "champion": "DrMundo", "rarity": "kRare"}, "36009": {"name": "El Macho Mundo", "champion": "DrMundo", "rarity": "kEpic"}, "36010": {"name": "Frozen Prince <PERSON>", "champion": "DrMundo", "rarity": "kEpic"}, "36021": {"name": "Street Demons Dr. <PERSON>", "champion": "DrMundo", "rarity": "kEpic"}, "37000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "37001": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "37002": {"name": "Pentakill Sona", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "37003": {"name": "Silent Night Sona", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "37004": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "37005": {"name": "Arcade Sona", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "37006": {"name": "DJ <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kUltimate"}, "37007": {"name": "Sweetheart Sona", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "37009": {"name": "Odyssey Sona", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "37017": {"name": "PsyOps Sona", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "37026": {"name": "Pentakill III: Lost Chapter Sona", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "37035": {"name": "Star Guardian Sona", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "37045": {"name": "Immortal Journey Sona", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "37046": {"name": "Prestige Immortal Journey Sona", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "37056": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "38000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "38001": {"name": "Festival Kassadin", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "38002": {"name": "Deep One Kassadin", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "38003": {"name": "Pre-Void Ka<PERSON>din", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "38004": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "38005": {"name": "Cosmic Reaver <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "38006": {"name": "Count <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "38014": {"name": "Hextech <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "38015": {"name": "Shockblade <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "38024": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "39000": {"name": "Irelia", "champion": "Irelia", "rarity": "kNoRarity"}, "39001": {"name": "Nightblade Irelia", "champion": "Irelia", "rarity": "kNoRarity"}, "39002": {"name": "Aviator <PERSON><PERSON><PERSON>", "champion": "Irelia", "rarity": "kNoRarity"}, "39003": {"name": "Infiltrator Irelia", "champion": "Irelia", "rarity": "kNoRarity"}, "39004": {"name": "Frostblade Irelia", "champion": "Irelia", "rarity": "kNoRarity"}, "39005": {"name": "Order of the Lotus Irelia", "champion": "Irelia", "rarity": "kNoRarity"}, "39006": {"name": "Divine Sword Irelia", "champion": "Irelia", "rarity": "kEpic"}, "39015": {"name": "iG Irelia", "champion": "Irelia", "rarity": "kEpic"}, "39016": {"name": "PROJECT: Irelia", "champion": "Irelia", "rarity": "kEpic"}, "39017": {"name": "Prestige PROJECT: Irelia", "champion": "Irelia", "rarity": "kMythic"}, "39018": {"name": "High Noon Irelia", "champion": "Irelia", "rarity": "kEpic"}, "39026": {"name": "Sentinel Irelia", "champion": "Irelia", "rarity": "kEpic"}, "39036": {"name": "Prestige PROJECT: Irelia (2022)", "champion": "Irelia", "rarity": "kMythic"}, "39037": {"name": "Mythmaker <PERSON><PERSON><PERSON>", "champion": "Irelia", "rarity": "kLegendary"}, "39045": {"name": "Porcelain Irelia", "champion": "Irelia", "rarity": "kEpic"}, "40000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "40001": {"name": "Tempest Janna", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "40002": {"name": "Hextech Janna", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "40003": {"name": "Frost Queen <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "40004": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "40005": {"name": "Forecast <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "40006": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "40007": {"name": "Star Guardian Janna", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "40008": {"name": "Sacred Sword Janna", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "40013": {"name": "Bewitching <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "40020": {"name": "Guardian of the Sands Janna", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "40027": {"name": "Battle Queen <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "40036": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "40045": {"name": "Cyber Hal<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "40046": {"name": "Prestige Cyber Halo Janna", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "40056": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "41000": {"name": "Gangplank", "champion": "Gangplank", "rarity": "kNoRarity"}, "41001": {"name": "Spooky Gangplank", "champion": "Gangplank", "rarity": "kNoRarity"}, "41002": {"name": "Minuteman Gangplank", "champion": "Gangplank", "rarity": "kNoRarity"}, "41003": {"name": "<PERSON>", "champion": "Gangplank", "rarity": "kNoRarity"}, "41004": {"name": "Toy Soldier Gangplank", "champion": "Gangplank", "rarity": "kNoRarity"}, "41005": {"name": "Special Forces Gangplank", "champion": "Gangplank", "rarity": "kNoRarity"}, "41006": {"name": "Sultan <PERSON>", "champion": "Gangplank", "rarity": "kNoRarity"}, "41007": {"name": "Captain <PERSON><PERSON>", "champion": "Gangplank", "rarity": "kNoRarity"}, "41008": {"name": "Dreadnova Gangplank", "champion": "Gangplank", "rarity": "kEpic"}, "41014": {"name": "Pool Party Gangplank", "champion": "Gangplank", "rarity": "kEpic"}, "41021": {"name": "FPX Gangplank", "champion": "Gangplank", "rarity": "kEpic"}, "41023": {"name": "Gangplank the Betrayer", "champion": "Gangplank", "rarity": "kEpic"}, "41033": {"name": "PROJECT: Gangplank", "champion": "Gangplank", "rarity": "kEpic"}, "42000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "42001": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "42002": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "42003": {"name": "Red <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "42004": {"name": "<PERSON> <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "42005": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "42006": {"name": "Dragon<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "42007": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "42008": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "42018": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "42026": {"name": "Astronaut <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "43000": {"name": "<PERSON>rma", "champion": "<PERSON>rma", "rarity": "kNoRarity"}, "43001": {"name": "Sun Goddess Karma", "champion": "<PERSON>rma", "rarity": "kNoRarity"}, "43002": {"name": "Sakura Karma", "champion": "<PERSON>rma", "rarity": "kNoRarity"}, "43003": {"name": "Traditional Karma", "champion": "<PERSON>rma", "rarity": "kNoRarity"}, "43004": {"name": "Order of the Lotus Karma", "champion": "<PERSON>rma", "rarity": "kNoRarity"}, "43005": {"name": "Warden <PERSON>", "champion": "<PERSON>rma", "rarity": "kNoRarity"}, "43006": {"name": "Winter Wonder Karma", "champion": "<PERSON>rma", "rarity": "kEpic"}, "43007": {"name": "Con<PERSON><PERSON>", "champion": "<PERSON>rma", "rarity": "kNoRarity"}, "43008": {"name": "Dark Star Karma", "champion": "<PERSON>rma", "rarity": "kEpic"}, "43019": {"name": "Dawnbringer Karma", "champion": "<PERSON>rma", "rarity": "kMythic"}, "43026": {"name": "Odyssey Karma", "champion": "<PERSON>rma", "rarity": "kEpic"}, "43027": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON>rma", "rarity": "kEpic"}, "43044": {"name": "Tranquility Dragon Karma", "champion": "<PERSON>rma", "rarity": "kEpic"}, "43054": {"name": "Faerie Queen <PERSON>rma", "champion": "<PERSON>rma", "rarity": "kLegendary"}, "43061": {"name": "Infernal Karma", "champion": "<PERSON>rma", "rarity": "kEpic"}, "44000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "44001": {"name": "Emerald Taric", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "44002": {"name": "Armor of the Fifth Age Taric", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "44003": {"name": "Bloodstone Taric", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "44004": {"name": "Pool Party Taric", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "44009": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "44018": {"name": "Space Groove Taric", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "45000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "45001": {"name": "White Mage Veigar", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "45002": {"name": "Curling Veigar", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "45003": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "45004": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "45005": {"name": "<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "45006": {"name": "Superb <PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "45007": {"name": "Bad Santa Veigar", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "45008": {"name": "Final Boss <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "45009": {"name": "Omega Squad Veigar", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "45013": {"name": "<PERSON><PERSON> Veigar", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "45023": {"name": "Furyhorn Cosplay Veigar", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "45032": {"name": "Astronaut <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "45041": {"name": "<PERSON> Tam<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "45051": {"name": "<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "45060": {"name": "Fright Night Veigar", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "48000": {"name": "Trundle", "champion": "Trundle", "rarity": "kNoRarity"}, "48001": {"name": "<PERSON>' Slugger Trundle", "champion": "Trundle", "rarity": "kNoRarity"}, "48002": {"name": "<PERSON><PERSON><PERSON>", "champion": "Trundle", "rarity": "kNoRarity"}, "48003": {"name": "Traditional Trundle", "champion": "Trundle", "rarity": "kNoRarity"}, "48004": {"name": "<PERSON>", "champion": "Trundle", "rarity": "kNoRarity"}, "48005": {"name": "Worldbreaker Trundle", "champion": "Trundle", "rarity": "kNoRarity"}, "48006": {"name": "Dragonslayer Trundle", "champion": "Trundle", "rarity": "kEpic"}, "48012": {"name": "Fright Night Trundle", "champion": "Trundle", "rarity": "kEpic"}, "48021": {"name": "Esports Fan Trundle", "champion": "Trundle", "rarity": "kEpic"}, "50000": {"name": "Swain", "champion": "Swain", "rarity": "kNoRarity"}, "50001": {"name": "Northern Front Swain", "champion": "Swain", "rarity": "kNoRarity"}, "50002": {"name": "<PERSON><PERSON><PERSON><PERSON> Swain", "champion": "Swain", "rarity": "kNoRarity"}, "50003": {"name": "<PERSON><PERSON>", "champion": "Swain", "rarity": "kEpic"}, "50004": {"name": "Dragon Master Swain", "champion": "Swain", "rarity": "kEpic"}, "50011": {"name": "<PERSON>xtech Swain", "champion": "Swain", "rarity": "kMythic"}, "50012": {"name": "<PERSON>", "champion": "Swain", "rarity": "kEpic"}, "50021": {"name": "<PERSON><PERSON><PERSON>", "champion": "Swain", "rarity": "kEpic"}, "50032": {"name": "<PERSON><PERSON> of the Wolf Swain", "champion": "Swain", "rarity": "kEpic"}, "50033": {"name": "<PERSON><PERSON><PERSON> of the Wolf Swain", "champion": "Swain", "rarity": "kMythic"}, "51000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "51001": {"name": "<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "51002": {"name": "Sheriff <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "51003": {"name": "Safari Caitlyn", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "51004": {"name": "Arctic Warfare Caitlyn", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "51005": {"name": "Officer <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "51006": {"name": "Headhunter Caitlyn", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "51010": {"name": "Luna<PERSON> <PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "51011": {"name": "Pulsefire Caitlyn", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "51013": {"name": "Pool Party Caitlyn", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "51019": {"name": "Arcade Caitlyn", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "51020": {"name": "Prestige Arcade Caitlyn", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "51022": {"name": "Battle Academia Caitlyn", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "51028": {"name": "<PERSON>ane Enforcer <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kRare"}, "51029": {"name": "Prestige Arcade Caitlyn (2022)", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "51030": {"name": "<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "51039": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "51048": {"name": "DRX Caitlyn", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "51050": {"name": "Arcane Commander <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "51051": {"name": "Prestige Arcane Commander <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "53000": {"name": "Blitzcrank", "champion": "Blitzcrank", "rarity": "kNoRarity"}, "53001": {"name": "<PERSON>", "champion": "Blitzcrank", "rarity": "kNoRarity"}, "53002": {"name": "Goalkeeper <PERSON><PERSON><PERSON><PERSON>", "champion": "Blitzcrank", "rarity": "kNoRarity"}, "53003": {"name": "Boom Boom Blitzcrank", "champion": "Blitzcrank", "rarity": "kNoRarity"}, "53004": {"name": "Piltover Customs Blitzcrank", "champion": "Blitzcrank", "rarity": "kEpic"}, "53005": {"name": "Definitely Not Blitzcrank", "champion": "Blitzcrank", "rarity": "kNoRarity"}, "53006": {"name": "iBlitzcrank", "champion": "Blitzcrank", "rarity": "kEpic"}, "53007": {"name": "Riot Blitzcrank", "champion": "Blitzcrank", "rarity": "kNoRarity"}, "53011": {"name": "Battle Boss Blitzcrank", "champion": "Blitzcrank", "rarity": "kEpic"}, "53020": {"name": "Lancer <PERSON>crank", "champion": "Blitzcrank", "rarity": "kEpic"}, "53021": {"name": "Lancer Paragon Blitzcrank", "champion": "Blitzcrank", "rarity": "kEpic"}, "53022": {"name": "Witch's <PERSON><PERSON>", "champion": "Blitzcrank", "rarity": "kEpic"}, "53029": {"name": "Space Groove Blitz & Crank", "champion": "Blitzcrank", "rarity": "kLegendary"}, "53036": {"name": "<PERSON><PERSON>", "champion": "Blitzcrank", "rarity": "kNoRarity"}, "53047": {"name": "Zenith Games Blitzcrank", "champion": "Blitzcrank", "rarity": "kEpic"}, "53056": {"name": "Bee<PERSON><PERSON>rank", "champion": "Blitzcrank", "rarity": "kEpic"}, "54000": {"name": "Malphite", "champion": "Malphite", "rarity": "kNoRarity"}, "54001": {"name": "Shamrock Malphite", "champion": "Malphite", "rarity": "kNoRarity"}, "54002": {"name": "Coral Reef Malphite", "champion": "Malphite", "rarity": "kNoRarity"}, "54003": {"name": "Marble Malphite", "champion": "Malphite", "rarity": "kNoRarity"}, "54004": {"name": "Obsidian Malphite", "champion": "Malphite", "rarity": "kNoRarity"}, "54005": {"name": "Glacial Malphite", "champion": "Malphite", "rarity": "kEpic"}, "54006": {"name": "Mecha Malphite", "champion": "Malphite", "rarity": "kEpic"}, "54007": {"name": "Ironside Malphite", "champion": "Malphite", "rarity": "kNoRarity"}, "54016": {"name": "Odyssey Malphite", "champion": "Malphite", "rarity": "kEpic"}, "54023": {"name": "Dark Star Malphite", "champion": "Malphite", "rarity": "kEpic"}, "54024": {"name": "Prestige Dark Star Malphite", "champion": "Malphite", "rarity": "kMythic"}, "54025": {"name": "FPX Malphite", "champion": "Malphite", "rarity": "kEpic"}, "54027": {"name": "Old God Malphite", "champion": "Malphite", "rarity": "kEpic"}, "54037": {"name": "Lunar Guardian Malphite", "champion": "Malphite", "rarity": "kEpic"}, "55000": {"name": "Katarina", "champion": "Katarina", "rarity": "kNoRarity"}, "55001": {"name": "Mercenary Katarina", "champion": "Katarina", "rarity": "kNoRarity"}, "55002": {"name": "Red Card Katarina", "champion": "Katarina", "rarity": "kNoRarity"}, "55003": {"name": "Bilgewater Katarina", "champion": "Katarina", "rarity": "kNoRarity"}, "55004": {"name": "<PERSON>", "champion": "Katarina", "rarity": "kNoRarity"}, "55005": {"name": "High Command Katarina", "champion": "Katarina", "rarity": "kNoRarity"}, "55006": {"name": "Sandstorm Katarina", "champion": "Katarina", "rarity": "kNoRarity"}, "55007": {"name": "<PERSON>lay <PERSON>", "champion": "Katarina", "rarity": "kNoRarity"}, "55008": {"name": "Warring Kingdoms Katarina", "champion": "Katarina", "rarity": "kNoRarity"}, "55009": {"name": "PROJECT: Katarina", "champion": "Katarina", "rarity": "kEpic"}, "55010": {"name": "Death Sworn Katarina", "champion": "Katarina", "rarity": "kEpic"}, "55012": {"name": "Battle Academia Katarina", "champion": "Katarina", "rarity": "kEpic"}, "55021": {"name": "Blood Moon Katarina", "champion": "Katarina", "rarity": "kEpic"}, "55029": {"name": "Battle Queen <PERSON>", "champion": "Katarina", "rarity": "kLegendary"}, "55037": {"name": "High Noon <PERSON>arina", "champion": "Katarina", "rarity": "kEpic"}, "55047": {"name": "Faerie Court Katarina", "champion": "Katarina", "rarity": "kEpic"}, "55048": {"name": "Prestige Faerie Court Katarina", "champion": "Katarina", "rarity": "kMythic"}, "55059": {"name": "<PERSON><PERSON> of the Wolf Katarina", "champion": "Katarina", "rarity": "kEpic"}, "55060": {"name": "Prestige Masque of the Black Rose Katarina", "champion": "Katarina", "rarity": "kMythic"}, "56000": {"name": "Nocturne", "champion": "Nocturne", "rarity": "kNoRarity"}, "56001": {"name": "Frozen Terror Nocturne", "champion": "Nocturne", "rarity": "kNoRarity"}, "56002": {"name": "Void Nocturne", "champion": "Nocturne", "rarity": "kNoRarity"}, "56003": {"name": "<PERSON><PERSON><PERSON> Nocturne", "champion": "Nocturne", "rarity": "kNoRarity"}, "56004": {"name": "Haunting Nocturne", "champion": "Nocturne", "rarity": "kNoRarity"}, "56005": {"name": "Eternum Nocturne", "champion": "Nocturne", "rarity": "kLegendary"}, "56006": {"name": "Cursed <PERSON><PERSON>t <PERSON>urne", "champion": "Nocturne", "rarity": "kNoRarity"}, "56007": {"name": "Old God Nocturne", "champion": "Nocturne", "rarity": "kEpic"}, "56016": {"name": "Hextech Nocturne", "champion": "Nocturne", "rarity": "kMythic"}, "56017": {"name": "Broken Covenant Nocturne", "champion": "Nocturne", "rarity": "kEpic"}, "56026": {"name": "Empyrean Nocturne", "champion": "Nocturne", "rarity": "kEpic"}, "57000": {"name": "Maokai", "champion": "Maokai", "rarity": "kNoRarity"}, "57001": {"name": "Charred <PERSON>", "champion": "Maokai", "rarity": "kNoRarity"}, "57002": {"name": "Totemic <PERSON>", "champion": "Maokai", "rarity": "kNoRarity"}, "57003": {"name": "Festive Maokai", "champion": "Maokai", "rarity": "kNoRarity"}, "57004": {"name": "Haunted <PERSON>", "champion": "Maokai", "rarity": "kNoRarity"}, "57005": {"name": "Goalkeeper <PERSON><PERSON>", "champion": "Maokai", "rarity": "kNoRarity"}, "57006": {"name": "Meowkai", "champion": "Maokai", "rarity": "kEpic"}, "57007": {"name": "<PERSON><PERSON>", "champion": "Maokai", "rarity": "kNoRarity"}, "57016": {"name": "Worldbreaker <PERSON><PERSON>", "champion": "Maokai", "rarity": "kEpic"}, "57024": {"name": "Astronaut <PERSON>", "champion": "Maokai", "rarity": "kEpic"}, "57033": {"name": "DRX Maokai", "champion": "Maokai", "rarity": "kEpic"}, "58000": {"name": "Renekton", "champion": "Renekton", "rarity": "kNoRarity"}, "58001": {"name": "Galactic Renekton", "champion": "Renekton", "rarity": "kNoRarity"}, "58002": {"name": "Outback Renekton", "champion": "Renekton", "rarity": "kNoRarity"}, "58003": {"name": "Bloodfury Renekton", "champion": "Renekton", "rarity": "kNoRarity"}, "58004": {"name": "Rune Wars Renekton", "champion": "Renekton", "rarity": "kNoRarity"}, "58005": {"name": "Scorched Earth Renekton", "champion": "Renekton", "rarity": "kNoRarity"}, "58006": {"name": "Pool Party Renekton", "champion": "Renekton", "rarity": "kNoRarity"}, "58007": {"name": "Prehistoric Renekton", "champion": "Renekton", "rarity": "kNoRarity"}, "58008": {"name": "SKT T1 Renekton", "champion": "Renekton", "rarity": "kNoRarity"}, "58009": {"name": "<PERSON><PERSON><PERSON>", "champion": "Renekton", "rarity": "kNoRarity"}, "58017": {"name": "Hextech Renekton", "champion": "Renekton", "rarity": "kMythic"}, "58018": {"name": "Blackfrost Renekton", "champion": "Renekton", "rarity": "kEpic"}, "58026": {"name": "PROJECT: Renekton", "champion": "Renekton", "rarity": "kLegendary"}, "58033": {"name": "Dawnbringer Renekton", "champion": "Renekton", "rarity": "kEpic"}, "58042": {"name": "Worlds 2023 Renekton", "champion": "Renekton", "rarity": "kEpic"}, "59000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "59001": {"name": "Commando Jarvan IV", "champion": "JarvanIV", "rarity": "kNoRarity"}, "59002": {"name": "Dragonslayer <PERSON><PERSON><PERSON>", "champion": "JarvanIV", "rarity": "kNoRarity"}, "59003": {"name": "<PERSON><PERSON><PERSON>", "champion": "JarvanIV", "rarity": "kNoRarity"}, "59004": {"name": "<PERSON><PERSON>", "champion": "JarvanIV", "rarity": "kNoRarity"}, "59005": {"name": "Warring Kingdoms Jarvan IV", "champion": "JarvanIV", "rarity": "kEpic"}, "59006": {"name": "<PERSON>nat<PERSON>", "champion": "JarvanIV", "rarity": "kNoRarity"}, "59007": {"name": "Dark Star Jarvan IV", "champion": "JarvanIV", "rarity": "kEpic"}, "59008": {"name": "SSG Jarvan IV", "champion": "JarvanIV", "rarity": "kEpic"}, "59009": {"name": "Hextech Jarvan IV", "champion": "JarvanIV", "rarity": "kMythic"}, "59011": {"name": "Pool Party Jarvan IV", "champion": "JarvanIV", "rarity": "kEpic"}, "59021": {"name": "Lunar Beast Jarvan IV", "champion": "JarvanIV", "rarity": "kEpic"}, "59030": {"name": "Worlds 2021 <PERSON><PERSON><PERSON>", "champion": "JarvanIV", "rarity": "kEpic"}, "59035": {"name": "Nightbringer J<PERSON><PERSON>", "champion": "JarvanIV", "rarity": "kEpic"}, "59044": {"name": "Myth<PERSON> <PERSON><PERSON><PERSON>", "champion": "JarvanIV", "rarity": "kEpic"}, "60000": {"name": "<PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "60001": {"name": "Death Blossom Elise", "champion": "<PERSON>", "rarity": "kNoRarity"}, "60002": {"name": "<PERSON><PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "60003": {"name": "Blood Moon Elise", "champion": "<PERSON>", "rarity": "kNoRarity"}, "60004": {"name": "SKT T1 Elise", "champion": "<PERSON>", "rarity": "kNoRarity"}, "60005": {"name": "Super Galaxy Elise", "champion": "<PERSON>", "rarity": "kEpic"}, "60006": {"name": "Bewitching Elise", "champion": "<PERSON>", "rarity": "kEpic"}, "60015": {"name": "Withered <PERSON>", "champion": "<PERSON>", "rarity": "kEpic"}, "60024": {"name": "Coven Elise", "champion": "<PERSON>", "rarity": "kEpic"}, "60034": {"name": "Masque of the Black Rose Elise", "champion": "<PERSON>", "rarity": "kNoRarity"}, "61000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "61001": {"name": "Gothic Orianna", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "61002": {"name": "Sewn Chaos Orianna", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "61003": {"name": "Bladecraft Orianna", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "61004": {"name": "TPA Orianna", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "61005": {"name": "Winter Wonder Orianna", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "61006": {"name": "<PERSON>eeker <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "61007": {"name": "Dark Star Orianna", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "61008": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "61011": {"name": "Pool Party Orianna", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "61020": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "61029": {"name": "Star Guardian Orianna", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "61038": {"name": "T1 Orianna", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "62000": {"name": "Wukong", "champion": "Wukong", "rarity": "kNoRarity"}, "62001": {"name": "Volcanic Wu<PERSON>g", "champion": "MonkeyKing", "rarity": "kNoRarity"}, "62002": {"name": "General <PERSON>", "champion": "MonkeyKing", "rarity": "kNoRarity"}, "62003": {"name": "Jade Dragon Wukong", "champion": "MonkeyKing", "rarity": "kNoRarity"}, "62004": {"name": "Underworld Wukong", "champion": "MonkeyKing", "rarity": "kEpic"}, "62005": {"name": "<PERSON><PERSON><PERSON>", "champion": "MonkeyKing", "rarity": "kEpic"}, "62006": {"name": "Lancer <PERSON><PERSON><PERSON>", "champion": "MonkeyKing", "rarity": "kEpic"}, "62007": {"name": "Battle Academia Wukong", "champion": "MonkeyKing", "rarity": "kEpic"}, "62016": {"name": "<PERSON><PERSON>", "champion": "MonkeyKing", "rarity": "kEpic"}, "63000": {"name": "Brand", "champion": "Brand", "rarity": "kNoRarity"}, "63001": {"name": "Apocalyptic Brand", "champion": "Brand", "rarity": "kNoRarity"}, "63002": {"name": "<PERSON><PERSON>", "champion": "Brand", "rarity": "kNoRarity"}, "63003": {"name": "Cryocore Brand", "champion": "Brand", "rarity": "kNoRarity"}, "63004": {"name": "Zombie Brand", "champion": "Brand", "rarity": "kLegendary"}, "63005": {"name": "Spirit Fire Brand", "champion": "Brand", "rarity": "kEpic"}, "63006": {"name": "Battle Boss Brand", "champion": "Brand", "rarity": "kEpic"}, "63007": {"name": "Arclight Brand", "champion": "Brand", "rarity": "kEpic"}, "63008": {"name": "Eternal Dragon Brand", "champion": "Brand", "rarity": "kEpic"}, "63021": {"name": "Debonair Brand", "champion": "Brand", "rarity": "kEpic"}, "63022": {"name": "Prestige Debonair Brand", "champion": "Brand", "rarity": "kMythic"}, "63033": {"name": "Street Demons Brand", "champion": "Brand", "rarity": "kEpic"}, "63042": {"name": "Empyrean Brand", "champion": "Brand", "rarity": "kEpic"}, "64000": {"name": "<PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "64001": {"name": "Traditional Lee Sin", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "64002": {"name": "Acoly<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "64003": {"name": "Dragon Fist <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "64004": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "64005": {"name": "Pool Party Lee <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "64006": {"name": "SKT T1 Lee <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "64010": {"name": "Knockout <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "64011": {"name": "God Fist Lee <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "64012": {"name": "Playmaker <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "64027": {"name": "Nightbri<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "64028": {"name": "Prestige Nightbringer Lee Sin", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "64029": {"name": "FPX Lee <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "64031": {"name": "Storm Dragon Lee Sin", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "64039": {"name": "Prestige Nightbringer Lee Sin (2022)", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "64041": {"name": "Zenith Games Lee Sin", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "64051": {"name": "Heavensca<PERSON> Lee <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "64052": {"name": "Divine Heavenscale Lee <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "64068": {"name": "T1 <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "67000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "67001": {"name": "Vindicator <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "67002": {"name": "Aristo<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "67003": {"name": "Dragonslayer <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "67004": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "67005": {"name": "SKT T1 Vayne", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "67006": {"name": "Arclight Vayne", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "67010": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "67011": {"name": "PROJECT: <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "67012": {"name": "Firecracker Vayne", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "67013": {"name": "Prestige Firecracker Vayne", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "67014": {"name": "Spirit Blossom Vayne", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "67015": {"name": "FPX Vayne", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "67025": {"name": "Sentinel Vayne", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "67032": {"name": "Battle Bat Vayne", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "67033": {"name": "Prestige Firecracker Vayne (2022)", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "67044": {"name": "Dawnbringer Vayne", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "67055": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "68000": {"name": "Rumble", "champion": "Rumble", "rarity": "kNoRarity"}, "68001": {"name": "Rumble in the Jungle", "champion": "Rumble", "rarity": "kNoRarity"}, "68002": {"name": "Bilgerat Rumble", "champion": "Rumble", "rarity": "kNoRarity"}, "68003": {"name": "Super Galaxy Rumble", "champion": "Rumble", "rarity": "kLegendary"}, "68004": {"name": "Badlands Baron <PERSON>", "champion": "Rumble", "rarity": "kEpic"}, "68013": {"name": "Space Groove Rumble", "champion": "Rumble", "rarity": "kEpic"}, "68023": {"name": "Cafe Cuties Rumble", "champion": "Rumble", "rarity": "kEpic"}, "69000": {"name": "Cassiopeia", "champion": "Cassiopeia", "rarity": "kNoRarity"}, "69001": {"name": "Desperada Cassiopeia", "champion": "Cassiopeia", "rarity": "kNoRarity"}, "69002": {"name": "<PERSON><PERSON>", "champion": "Cassiopeia", "rarity": "kNoRarity"}, "69003": {"name": "Mythic Cassiopeia", "champion": "Cassiopeia", "rarity": "kNoRarity"}, "69004": {"name": "<PERSON>", "champion": "Cassiopeia", "rarity": "kNoRarity"}, "69008": {"name": "Eternum Cassiopeia", "champion": "Cassiopeia", "rarity": "kEpic"}, "69009": {"name": "Spirit Blossom Cassiopeia", "champion": "Cassiopeia", "rarity": "kEpic"}, "69018": {"name": "Coven Cassiopeia", "champion": "Cassiopeia", "rarity": "kEpic"}, "69028": {"name": "Bewitching Cassiopeia", "champion": "Cassiopeia", "rarity": "kEpic"}, "69038": {"name": "Prestige Mythmaker Cassiopeia", "champion": "Cassiopeia", "rarity": "kMythic"}, "72000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "72001": {"name": "Sandscourge Skarner", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "72002": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "72003": {"name": "Battlecast Alpha Skarner", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "72004": {"name": "Guardian of the Sands Skarner", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "72005": {"name": "Cosmic Sting Skarner", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "74000": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "74001": {"name": "Alien Invader <PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "74002": {"name": "Blast Zone Heimerdinger", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "74003": {"name": "Piltover Customs Heimerdinger", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "74004": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "74005": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "74006": {"name": "Dragon Trainer <PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "74015": {"name": "Pool <PERSON> Heimerdinger", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "74024": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "74033": {"name": "Arcane Professor <PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "75000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "75001": {"name": "Galactic Nasus", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "75002": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "75003": {"name": "Dreadknight Nasus", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "75004": {"name": "Riot K-9 Nasus", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "75005": {"name": "Infernal Nasus", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "75006": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "75010": {"name": "Worldbreaker <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "75011": {"name": "Lunar Guardian Nasus", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "75016": {"name": "Battlecast Nasus", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "75025": {"name": "Space Groove Nasus", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "75035": {"name": "Armored Titan Nasus", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "75045": {"name": "Nightbringer Nasus", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "76000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "76001": {"name": "<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "76002": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "76003": {"name": "French Maid <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "76004": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "76005": {"name": "Bewitching <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "76006": {"name": "Headhunter <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "76007": {"name": "Warring Kingdoms Nidalee", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "76008": {"name": "<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "76009": {"name": "Super Galaxy Nidalee", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "76011": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "76018": {"name": "Cosmic Huntress <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "76027": {"name": "DWG <PERSON>e", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "76029": {"name": "<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "76039": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "76048": {"name": "La Ilusión Nidalee", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "77000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "77001": {"name": "Black Belt Udyr", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "77002": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "77003": {"name": "Spirit Guard Udyr", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kUltimate"}, "77004": {"name": "Definitely Not Udyr", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "77005": {"name": "Dragon Oracle Udyr", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "77006": {"name": "Inkshadow Udyr", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "78000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "78001": {"name": "Noxus Poppy", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "78002": {"name": "Lollipoppy", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "78003": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "78004": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "78005": {"name": "Battle Regalia Poppy", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "78006": {"name": "<PERSON> Hammer Poppy", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "78007": {"name": "Star Guardian Poppy", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "78014": {"name": "Snow Fawn <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "78015": {"name": "Hextech Poppy", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "78016": {"name": "Astronaut Poppy", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "78024": {"name": "Bewitching <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "78033": {"name": "Cafe Cuties Poppy", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "79000": {"name": "Gragas", "champion": "Gragas", "rarity": "kNoRarity"}, "79001": {"name": "Scuba Gragas", "champion": "Gragas", "rarity": "kNoRarity"}, "79002": {"name": "Hillbilly Gragas", "champion": "Gragas", "rarity": "kNoRarity"}, "79003": {"name": "Santa Gragas", "champion": "Gragas", "rarity": "kNoRarity"}, "79004": {"name": "Gragas, Esq.", "champion": "Gragas", "rarity": "kNoRarity"}, "79005": {"name": "<PERSON><PERSON> Gragas", "champion": "Gragas", "rarity": "kNoRarity"}, "79006": {"name": "Oktoberfest Gragas", "champion": "Gragas", "rarity": "kNoRarity"}, "79007": {"name": "Superfan <PERSON>", "champion": "Gragas", "rarity": "kNoRarity"}, "79008": {"name": "Fnatic Gragas", "champion": "Gragas", "rarity": "kNoRarity"}, "79009": {"name": "Gragas Caskbreaker", "champion": "Gragas", "rarity": "kNoRarity"}, "79010": {"name": "Arctic Ops Gragas", "champion": "Gragas", "rarity": "kEpic"}, "79011": {"name": "<PERSON>", "champion": "Gragas", "rarity": "kEpic"}, "79020": {"name": "Space Groove Gragas", "champion": "Gragas", "rarity": "kEpic"}, "79029": {"name": "High Noon Gragas", "champion": "Gragas", "rarity": "kEpic"}, "79039": {"name": "Music Fan Gragas", "champion": "Gragas", "rarity": "kEpic"}, "80000": {"name": "Pantheon", "champion": "Pantheon", "rarity": "kNoRarity"}, "80001": {"name": "Myrmidon Pantheon", "champion": "Pantheon", "rarity": "kNoRarity"}, "80002": {"name": "Ruthless Pantheon", "champion": "Pantheon", "rarity": "kNoRarity"}, "80003": {"name": "Perseus Pantheon", "champion": "Pantheon", "rarity": "kNoRarity"}, "80004": {"name": "Full Metal Pantheon", "champion": "Pantheon", "rarity": "kNoRarity"}, "80005": {"name": "Glaive Warrior Pantheon", "champion": "Pantheon", "rarity": "kNoRarity"}, "80006": {"name": "Dragonslayer Pantheon", "champion": "Pantheon", "rarity": "kEpic"}, "80007": {"name": "Zombie Slayer Pantheon", "champion": "Pantheon", "rarity": "kNoRarity"}, "80008": {"name": "Baker Pantheon", "champion": "Pantheon", "rarity": "kNoRarity"}, "80016": {"name": "Pulsefire Pantheon", "champion": "Pantheon", "rarity": "kEpic"}, "80025": {"name": "Ruined Pantheon", "champion": "Pantheon", "rarity": "kEpic"}, "80026": {"name": "Pre<PERSON><PERSON> Ascended Pantheon", "champion": "Pantheon", "rarity": "kMythic"}, "80036": {"name": "Ashen Conqueror Pantheon", "champion": "Pantheon", "rarity": "kMythic"}, "80038": {"name": "<PERSON><PERSON> of the Wolf Pantheon", "champion": "Pantheon", "rarity": "kEpic"}, "81000": {"name": "Ezreal", "champion": "Ezreal", "rarity": "kNoRarity"}, "81001": {"name": "Nottingham Ezreal", "champion": "Ezreal", "rarity": "kNoRarity"}, "81002": {"name": "Striker <PERSON>", "champion": "Ezreal", "rarity": "kNoRarity"}, "81003": {"name": "<PERSON><PERSON>", "champion": "Ezreal", "rarity": "kNoRarity"}, "81004": {"name": "Explorer <PERSON>", "champion": "Ezreal", "rarity": "kNoRarity"}, "81005": {"name": "Pulsefire Ezreal", "champion": "Ezreal", "rarity": "kUltimate"}, "81006": {"name": "TPA Ezreal", "champion": "Ezreal", "rarity": "kNoRarity"}, "81007": {"name": "<PERSON><PERSON><PERSON>", "champion": "Ezreal", "rarity": "kNoRarity"}, "81008": {"name": "Ace of Spades Ezreal", "champion": "Ezreal", "rarity": "kNoRarity"}, "81009": {"name": "Arcade Ezreal", "champion": "Ezreal", "rarity": "kEpic"}, "81018": {"name": "Star Guardian Ezreal", "champion": "Ezreal", "rarity": "kEpic"}, "81019": {"name": "SSG Ezreal", "champion": "Ezreal", "rarity": "kEpic"}, "81020": {"name": "Pajama Guardian Ezreal", "champion": "Ezreal", "rarity": "kEpic"}, "81021": {"name": "Battle Academia Ezreal", "champion": "Ezreal", "rarity": "kLegendary"}, "81022": {"name": "PsyOps Ezreal", "champion": "Ezreal", "rarity": "kEpic"}, "81023": {"name": "Prestige PsyOps Ezreal", "champion": "Ezreal", "rarity": "kMythic"}, "81025": {"name": "Porcelain Protector Ezreal", "champion": "Ezreal", "rarity": "kLegendary"}, "81033": {"name": "Faerie Court Ezreal", "champion": "Ezreal", "rarity": "kEpic"}, "81043": {"name": "HEARTSTEEL Ezreal", "champion": "Ezreal", "rarity": "kEpic"}, "81044": {"name": "Heavenscale Ezreal", "champion": "Ezreal", "rarity": "kEpic"}, "81054": {"name": "Prestige Heavenscale Ezreal", "champion": "Ezreal", "rarity": "kMythic"}, "81065": {"name": "Masque of the Black Rose Ezreal", "champion": "Ezreal", "rarity": "kEpic"}, "82000": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "82001": {"name": "Dragon Knight Mordekaiser", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "82002": {"name": "Infernal Mordekaiser", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "82003": {"name": "Pentakill Mordekaiser", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "82004": {"name": "Lord <PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "82005": {"name": "King of Clubs Mordekaiser", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "82006": {"name": "Dark Star Mordekaiser", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "82013": {"name": "PROJECT: <PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "82023": {"name": "Pentakill III: Lost Chapter Mordekaiser", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "82032": {"name": "High Noon Mordekaiser", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "82042": {"name": "<PERSON>n Graveknight <PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "82044": {"name": "Old God Mordekaiser", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "83000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "83001": {"name": "Undertaker <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "83002": {"name": "Pentakill Yorick", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "83003": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "83004": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "83012": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "83021": {"name": "Pentakill III: Lost Chapter Yorick", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "83030": {"name": "Spirit Blossom <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "83040": {"name": "Dark Star Yorick", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "84000": {"name": "Akali", "champion": "Akali", "rarity": "kNoRarity"}, "84001": {"name": "<PERSON><PERSON>", "champion": "Akali", "rarity": "kNoRarity"}, "84002": {"name": "Infernal Akali", "champion": "Akali", "rarity": "kNoRarity"}, "84003": {"name": "All-star <PERSON><PERSON><PERSON>", "champion": "Akali", "rarity": "kNoRarity"}, "84004": {"name": "Nurse <PERSON><PERSON><PERSON>", "champion": "Akali", "rarity": "kNoRarity"}, "84005": {"name": "Blood Moon Akali", "champion": "Akali", "rarity": "kNoRarity"}, "84006": {"name": "Silverfang Akali", "champion": "Akali", "rarity": "kNoRarity"}, "84007": {"name": "Headhunter Akali", "champion": "Akali", "rarity": "kEpic"}, "84008": {"name": "<PERSON><PERSON><PERSON>", "champion": "Akali", "rarity": "kNoRarity"}, "84009": {"name": "K/DA Akali", "champion": "Akali", "rarity": "kEpic"}, "84013": {"name": "Prestige K/DA Akali", "champion": "Akali", "rarity": "kMythic"}, "84014": {"name": "PROJECT: <PERSON><PERSON><PERSON>", "champion": "Akali", "rarity": "kEpic"}, "84015": {"name": "True Damage Akali", "champion": "Akali", "rarity": "kEpic"}, "84032": {"name": "K/DA ALL OUT Akali", "champion": "Akali", "rarity": "kEpic"}, "84050": {"name": "Crime City Nightmare Akali", "champion": "Akali", "rarity": "kEpic"}, "84060": {"name": "Prestige K/DA Akali (2022)", "champion": "Akali", "rarity": "kMythic"}, "84061": {"name": "Star Guardian Akali", "champion": "Akali", "rarity": "kLegendary"}, "84068": {"name": "DRX Akali", "champion": "Akali", "rarity": "kEpic"}, "84070": {"name": "Coven Akali", "champion": "Akali", "rarity": "kEpic"}, "84071": {"name": "Prestige Coven Akali", "champion": "Akali", "rarity": "kMythic"}, "84082": {"name": "Empyrean <PERSON>", "champion": "Akali", "rarity": "kEpic"}, "85000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "85001": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "85002": {"name": "Swamp Master <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "85003": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "85004": {"name": "<PERSON>nen M.D.", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "85005": {"name": "Arctic Ops Kennen", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "85006": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "85007": {"name": "Super Kennen", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "85008": {"name": "Infernal Kennen", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "85023": {"name": "DWG <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "85025": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "86000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "86001": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "86002": {"name": "Desert Trooper <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "86003": {"name": "Commando Garen", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "86004": {"name": "Dreadknight Garen", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "86005": {"name": "Rugged <PERSON>n", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "86006": {"name": "Steel Legion Garen", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "86010": {"name": "Rogue Admiral <PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "86011": {"name": "Warring Kingdoms Garen", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "86013": {"name": "God-<PERSON> <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "86014": {"name": "De<PERSON>cia <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "86022": {"name": "Mecha <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "86023": {"name": "Prestige Mecha Kingdoms Garen", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "86024": {"name": "Battle Academia Garen", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "86033": {"name": "My<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "86044": {"name": "Fallen God-King <PERSON><PERSON>n", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "89000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "89001": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "89002": {"name": "Defender <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "89003": {"name": "Iron Solari Leona", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "89004": {"name": "Pool Party Leona", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "89008": {"name": "PROJECT: <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "89009": {"name": "Barbecue Leona", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "89010": {"name": "Solar Eclipse Leona", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "89011": {"name": "Lunar <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "89012": {"name": "<PERSON>cha <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "89021": {"name": "Battle Academia Leona", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "89022": {"name": "DWG Leona", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "89023": {"name": "Prestige Battle Academia Leona", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "89033": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "89034": {"name": "High Noon Leona", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "89050": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "89052": {"name": "Battle Lion Leon<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "89053": {"name": "Prestige Battle Lion Leon<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "90000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "90001": {"name": "Vizier <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "90002": {"name": "Shadow Prince <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "90003": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "90004": {"name": "Overlord <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "90005": {"name": "Snow Day Malzahar", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "90006": {"name": "Battle Boss <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "90007": {"name": "Hextech Malzahar", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "90009": {"name": "Worldbreaker <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "90018": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "90028": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "90038": {"name": "Three Honors Malzahar", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "90039": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "91000": {"name": "Talon", "champion": "Talon", "rarity": "kNoRarity"}, "91001": {"name": "Renegade Talon", "champion": "Talon", "rarity": "kNoRarity"}, "91002": {"name": "Crimson Elite Talon", "champion": "Talon", "rarity": "kNoRarity"}, "91003": {"name": "Dragonblade Talon", "champion": "Talon", "rarity": "kNoRarity"}, "91004": {"name": "SSW Talon", "champion": "Talon", "rarity": "kNoRarity"}, "91005": {"name": "Blood Moon Talon", "champion": "Talon", "rarity": "kEpic"}, "91012": {"name": "Enduring Sword Talon", "champion": "Talon", "rarity": "kEpic"}, "91020": {"name": "<PERSON>", "champion": "Talon", "rarity": "kEpic"}, "91029": {"name": "Withered <PERSON>", "champion": "Talon", "rarity": "kEpic"}, "91038": {"name": "High Noon Talon", "champion": "Talon", "rarity": "kEpic"}, "91039": {"name": "Prestige High Noon Talon", "champion": "Talon", "rarity": "kMythic"}, "91049": {"name": "Primal Ambush Talon", "champion": "Talon", "rarity": "kEpic"}, "91059": {"name": "Grand Reckoning Talon", "champion": "Talon", "rarity": "kNoRarity"}, "92000": {"name": "Riven", "champion": "Riven", "rarity": "kNoRarity"}, "92001": {"name": "Redeemed <PERSON><PERSON>n", "champion": "Riven", "rarity": "kNoRarity"}, "92002": {"name": "Crimson Elite Riven", "champion": "Riven", "rarity": "kNoRarity"}, "92003": {"name": "<PERSON> Bunny Riven", "champion": "Riven", "rarity": "kNoRarity"}, "92004": {"name": "Worlds 2012 Riven", "champion": "Riven", "rarity": "kNoRarity"}, "92005": {"name": "Dragonblade Riven", "champion": "Riven", "rarity": "kEpic"}, "92006": {"name": "Arcade Riven", "champion": "Riven", "rarity": "kEpic"}, "92007": {"name": "Reignited Worlds 2012 Riven", "champion": "Riven", "rarity": "kNoRarity"}, "92016": {"name": "Dawnbringer Riven", "champion": "Riven", "rarity": "kLegendary"}, "92018": {"name": "Pulsefire Riven", "champion": "Riven", "rarity": "kEpic"}, "92020": {"name": "Valiant Sword Riven", "champion": "Riven", "rarity": "kEpic"}, "92022": {"name": "Prestige Valiant Sword Riven", "champion": "Riven", "rarity": "kMythic"}, "92023": {"name": "Spirit Blossom Riven", "champion": "Riven", "rarity": "kEpic"}, "92034": {"name": "Sentinel Riven", "champion": "Riven", "rarity": "kEpic"}, "92044": {"name": "Battle Bunny Prime Riven", "champion": "Riven", "rarity": "kEpic"}, "92045": {"name": "Prestige Valiant Sword Riven (2022)", "champion": "Riven", "rarity": "kMythic"}, "92055": {"name": "Broken Covenant Riven", "champion": "Riven", "rarity": "kLegendary"}, "92063": {"name": "Primal Ambush Riven", "champion": "Riven", "rarity": "kEpic"}, "96000": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "96001": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "KogMaw", "rarity": "kNoRarity"}, "96002": {"name": "Sonoran <PERSON>", "champion": "KogMaw", "rarity": "kNoRarity"}, "96003": {"name": "Monarch <PERSON>", "champion": "KogMaw", "rarity": "kNoRarity"}, "96004": {"name": "Reindeer <PERSON>", "champion": "KogMaw", "rarity": "kNoRarity"}, "96005": {"name": "Lion Dance Kog'Maw", "champion": "KogMaw", "rarity": "kEpic"}, "96006": {"name": "Deep Sea Kog'Maw", "champion": "KogMaw", "rarity": "kNoRarity"}, "96007": {"name": "Jurassic Kog'Maw", "champion": "KogMaw", "rarity": "kNoRarity"}, "96008": {"name": "Battlecast Kog'Maw", "champion": "KogMaw", "rarity": "kEpic"}, "96009": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "champion": "KogMaw", "rarity": "kNoRarity"}, "96010": {"name": "Hextech Kog'Maw", "champion": "KogMaw", "rarity": "kMythic"}, "96019": {"name": "Arcanist <PERSON><PERSON><PERSON><PERSON><PERSON>", "champion": "KogMaw", "rarity": "kEpic"}, "96028": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "KogMaw", "rarity": "kEpic"}, "96037": {"name": "Zap'Maw", "champion": "KogMaw", "rarity": "kEpic"}, "96046": {"name": "Shan Hai Scrolls Kog'Maw", "champion": "KogMaw", "rarity": "kEpic"}, "96055": {"name": "<PERSON><PERSON>", "champion": "KogMaw", "rarity": "kNoRarity"}, "98000": {"name": "<PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "98001": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "98002": {"name": "Yellow Jacket Shen", "champion": "<PERSON>", "rarity": "kNoRarity"}, "98003": {"name": "Sur<PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "98004": {"name": "Blood Moon Shen", "champion": "<PERSON>", "rarity": "kNoRarity"}, "98005": {"name": "Warlord <PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "98006": {"name": "TPA Shen", "champion": "<PERSON>", "rarity": "kNoRarity"}, "98015": {"name": "Pulsefire Shen", "champion": "<PERSON>", "rarity": "kEpic"}, "98016": {"name": "Infernal Shen", "champion": "<PERSON>", "rarity": "kEpic"}, "98022": {"name": "PsyOps Shen", "champion": "<PERSON>", "rarity": "kEpic"}, "98040": {"name": "Shockblade Shen", "champion": "<PERSON>", "rarity": "kEpic"}, "98049": {"name": "Ashen Guardian Shen", "champion": "<PERSON>", "rarity": "kMythic"}, "98051": {"name": "Three Honors Shen", "champion": "<PERSON>", "rarity": "kNoRarity"}, "99000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "99001": {"name": "Sorceress Lux", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "99002": {"name": "Spellthief Lux", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "99003": {"name": "Commando Lux", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "99004": {"name": "Imperial Lux", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "99005": {"name": "Steel Legion Lux", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "99006": {"name": "Star Guardian Lux", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "99007": {"name": "Elementalist Lux", "champion": "<PERSON><PERSON>", "rarity": "kUltimate"}, "99008": {"name": "Lunar Empress <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "99014": {"name": "Pajama Guardian Lux", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "99015": {"name": "Battle Academia Lux", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "99016": {"name": "Prestige Battle Academia Lux", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "99017": {"name": "Dark Cosmic Lux", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "99018": {"name": "Cosmic Lux", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "99019": {"name": "Space Groove Lux", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "99029": {"name": "Porcelain Lu<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "99038": {"name": "Soul Fighter Lux", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "99039": {"name": "Prestige Battle Academia Lux (2022)", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "99040": {"name": "Prestige Porcelain Lux", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "99042": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "99061": {"name": "Faerie Court Lux", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "101000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "101001": {"name": "<PERSON><PERSON><PERSON> Xerath", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "101002": {"name": "Battlecast Xerath", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "101003": {"name": "Scorched Earth Xerath", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "101004": {"name": "Guardian of the Sands Xerath", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "101005": {"name": "Dark Star Xerath", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "101012": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "101021": {"name": "Astronaut X<PERSON>th", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "101030": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "102000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "102001": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "102002": {"name": "Boneclaw <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "102003": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "102004": {"name": "<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "102005": {"name": "Worlds 2014 Shyvana", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "102006": {"name": "Super Galaxy Shyvana", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "102008": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "102017": {"name": "Immort<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "103000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "103001": {"name": "Dynasty Ahri", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "103002": {"name": "Midnight <PERSON>ri", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "103003": {"name": "Foxfire Ahri", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "103004": {"name": "Popstar <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "103005": {"name": "Challenger Ah<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "103006": {"name": "Academy Ahri", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "103007": {"name": "<PERSON> Ah<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "103014": {"name": "Star Guardian Ahri", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "103015": {"name": "K/DA Ahri", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "103016": {"name": "Prestige K/DA Ahri", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "103017": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "103027": {"name": "Spirit Blossom <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "103028": {"name": "K/DA ALL OUT Ahri", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "103042": {"name": "Coven <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "103065": {"name": "Prestige K/DA Ahri (2022)", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "103066": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "103076": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "103085": {"name": "Risen Legend <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kTranscendent"}, "104000": {"name": "<PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "104001": {"name": "<PERSON><PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "104002": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "104003": {"name": "Crime City Graves", "champion": "<PERSON>", "rarity": "kNoRarity"}, "104004": {"name": "Riot Graves", "champion": "<PERSON>", "rarity": "kNoRarity"}, "104005": {"name": "Pool Party Graves", "champion": "<PERSON>", "rarity": "kEpic"}, "104006": {"name": "Cutthroat Graves", "champion": "<PERSON>", "rarity": "kNoRarity"}, "104007": {"name": "Snow Day Graves", "champion": "<PERSON>", "rarity": "kEpic"}, "104014": {"name": "<PERSON><PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "104018": {"name": "Praetorian Graves", "champion": "<PERSON>", "rarity": "kEpic"}, "104025": {"name": "Battle Professor <PERSON>", "champion": "<PERSON>", "rarity": "kEpic"}, "104035": {"name": "Sentinel Graves", "champion": "<PERSON>", "rarity": "kLegendary"}, "104042": {"name": "EDG Graves", "champion": "<PERSON>", "rarity": "kEpic"}, "104045": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON>", "rarity": "kEpic"}, "105000": {"name": "Fizz", "champion": "Fizz", "rarity": "kNoRarity"}, "105001": {"name": "Atlantean Fizz", "champion": "Fizz", "rarity": "kNoRarity"}, "105002": {"name": "Tundra Fizz", "champion": "Fizz", "rarity": "kNoRarity"}, "105003": {"name": "Fisherman Fizz", "champion": "Fizz", "rarity": "kNoRarity"}, "105004": {"name": "Void Fizz", "champion": "Fizz", "rarity": "kEpic"}, "105008": {"name": "Cottontail Fizz", "champion": "Fizz", "rarity": "kNoRarity"}, "105009": {"name": "Super Galaxy Fizz", "champion": "Fizz", "rarity": "kEpic"}, "105010": {"name": "Omega Squad Fizz", "champion": "Fizz", "rarity": "kEpic"}, "105014": {"name": "Fuzz Fizz", "champion": "Fizz", "rarity": "kEpic"}, "105015": {"name": "Prestige Fuzz Fizz", "champion": "Fizz", "rarity": "kMythic"}, "105016": {"name": "Little Devil Fizz", "champion": "Fizz", "rarity": "kEpic"}, "105025": {"name": "Prestige Fuzz Fizz (2022)", "champion": "Fizz", "rarity": "kMythic"}, "105026": {"name": "Astronaut Fizz", "champion": "Fizz", "rarity": "kEpic"}, "105035": {"name": "Rain Shepherd Fizz", "champion": "Fizz", "rarity": "kEpic"}, "106000": {"name": "Volibear", "champion": "Volibear", "rarity": "kNoRarity"}, "106001": {"name": "Thunder Lord Volibear", "champion": "Volibear", "rarity": "kNoRarity"}, "106002": {"name": "Northern Storm Volibear", "champion": "Volibear", "rarity": "kNoRarity"}, "106003": {"name": "<PERSON><PERSON><PERSON>", "champion": "Volibear", "rarity": "kNoRarity"}, "106004": {"name": "Captain <PERSON><PERSON><PERSON>", "champion": "Volibear", "rarity": "kNoRarity"}, "106005": {"name": "El Rayo Volibear", "champion": "Volibear", "rarity": "kNoRarity"}, "106006": {"name": "The Thousand-Pierced Bear", "champion": "Volibear", "rarity": "kNoRarity"}, "106007": {"name": "Duality Dragon Volibear", "champion": "Volibear", "rarity": "kEpic"}, "106009": {"name": "Prestige Duality Dragon Volibear", "champion": "Volibear", "rarity": "kMythic"}, "106019": {"name": "Inkshadow Volibear", "champion": "Volibear", "rarity": "kEpic"}, "107000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "107001": {"name": "Headhunter <PERSON>gar", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "107002": {"name": "Night Hunter <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "107003": {"name": "SSW Rengar", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "107008": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "107015": {"name": "Pretty <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "107023": {"name": "Guardian of the Sands Rengar", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "107030": {"name": "Sentinel Rengar", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "107040": {"name": "Street Demons Rengar", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "110000": {"name": "Varus", "champion": "Varus", "rarity": "kNoRarity"}, "110001": {"name": "Blight Crystal Varus", "champion": "Varus", "rarity": "kNoRarity"}, "110002": {"name": "Arclight Varus", "champion": "Varus", "rarity": "kNoRarity"}, "110003": {"name": "Arctic Ops Varus", "champion": "Varus", "rarity": "kEpic"}, "110004": {"name": "<PERSON><PERSON><PERSON>", "champion": "Varus", "rarity": "kEpic"}, "110005": {"name": "Varus Swiftbolt", "champion": "Varus", "rarity": "kNoRarity"}, "110006": {"name": "Dark Star Varus", "champion": "Varus", "rarity": "kEpic"}, "110007": {"name": "<PERSON><PERSON><PERSON>", "champion": "Varus", "rarity": "kNoRarity"}, "110009": {"name": "Infernal Varus", "champion": "Varus", "rarity": "kEpic"}, "110016": {"name": "PROJECT: Varus", "champion": "Varus", "rarity": "kEpic"}, "110017": {"name": "Cosmic Hunter Varus", "champion": "Varus", "rarity": "kEpic"}, "110034": {"name": "High Noon Varus", "champion": "Varus", "rarity": "kEpic"}, "110044": {"name": "<PERSON> <PERSON> Varus", "champion": "Varus", "rarity": "kEpic"}, "110053": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "Varus", "rarity": "kLegendary"}, "111000": {"name": "Na<PERSON><PERSON>", "champion": "Na<PERSON><PERSON>", "rarity": "kNoRarity"}, "111001": {"name": "Abyssal Nautilus", "champion": "Na<PERSON><PERSON>", "rarity": "kNoRarity"}, "111002": {"name": "Subterranean Nautilus", "champion": "Na<PERSON><PERSON>", "rarity": "kNoRarity"}, "111003": {"name": "AstroNautilus", "champion": "Na<PERSON><PERSON>", "rarity": "kEpic"}, "111004": {"name": "<PERSON>", "champion": "Na<PERSON><PERSON>", "rarity": "kNoRarity"}, "111005": {"name": "Worldbreaker <PERSON><PERSON><PERSON>", "champion": "Na<PERSON><PERSON>", "rarity": "kNoRarity"}, "111006": {"name": "<PERSON><PERSON><PERSON>", "champion": "Na<PERSON><PERSON>", "rarity": "kRare"}, "111009": {"name": "Shan Hai Scrolls Nautilus", "champion": "Na<PERSON><PERSON>", "rarity": "kEpic"}, "111018": {"name": "Fright Night Nautilus", "champion": "Na<PERSON><PERSON>", "rarity": "kEpic"}, "111027": {"name": "Cosmic Paladin <PERSON>lus", "champion": "Na<PERSON><PERSON>", "rarity": "kEpic"}, "111036": {"name": "<PERSON><PERSON>", "champion": "Na<PERSON><PERSON>", "rarity": "kMythic"}, "112000": {"name": "<PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "112001": {"name": "Full Machine Viktor", "champion": "<PERSON>", "rarity": "kNoRarity"}, "112002": {"name": "Prototype Viktor", "champion": "<PERSON>", "rarity": "kNoRarity"}, "112003": {"name": "Creator <PERSON>", "champion": "<PERSON>", "rarity": "kEpic"}, "112004": {"name": "Death Sworn Viktor", "champion": "<PERSON>", "rarity": "kEpic"}, "112005": {"name": "PsyOps Viktor", "champion": "<PERSON>", "rarity": "kEpic"}, "112014": {"name": "High Noon Viktor", "champion": "<PERSON>", "rarity": "kEpic"}, "112024": {"name": "Arcane Savior Viktor", "champion": "<PERSON>", "rarity": "kLegendary"}, "113000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "113001": {"name": "Sabretusk Sejuani", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "113002": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "113003": {"name": "Traditional Sejuani", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "113004": {"name": "Bear Cavalry Sejuani", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "113005": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "113006": {"name": "Beast Hunter <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "113007": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "113008": {"name": "Firecracker <PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "113015": {"name": "Hextech Sejuani", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "113016": {"name": "PROJECT: <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "113026": {"name": "Solar Eclipse Sejuani", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "113036": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "114000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "114001": {"name": "Royal Guard Fiora", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "114002": {"name": "Nightraven <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "114003": {"name": "Headmistress <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "114004": {"name": "PROJECT: <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "114005": {"name": "Pool Party Fiora", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "114022": {"name": "Soaring Sword Fiora", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "114023": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "114031": {"name": "iG Fiora", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "114041": {"name": "Pulsefire Fiora", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "114050": {"name": "Lunar Beast Fiora", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "114051": {"name": "Prestige Lunar Beast Fiora", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "114060": {"name": "Bewitching <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "114069": {"name": "Faerie Court Fiora", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "114080": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "114089": {"name": "Battle Queen Fiora", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "115000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "115001": {"name": "Mad <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "115002": {"name": "Major <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "115003": {"name": "Pool <PERSON> Ziggs", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "115004": {"name": "Snow Day Ziggs", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "115005": {"name": "Master <PERSON>ani<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "115006": {"name": "Battle <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "115007": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "115014": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "115023": {"name": "Hextech <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "115024": {"name": "BZZZiggs", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "115033": {"name": "La Ilusión Ziggs", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "117000": {"name": "<PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "117001": {"name": "Bittersweet Lulu", "champion": "<PERSON>", "rarity": "kNoRarity"}, "117002": {"name": "Wicked Lulu", "champion": "<PERSON>", "rarity": "kNoRarity"}, "117003": {"name": "Dragon Trainer Lulu", "champion": "<PERSON>", "rarity": "kEpic"}, "117004": {"name": "Winter Wonder Lulu", "champion": "<PERSON>", "rarity": "kEpic"}, "117005": {"name": "Pool Party Lulu", "champion": "<PERSON>", "rarity": "kEpic"}, "117006": {"name": "Star Guardian Lulu", "champion": "<PERSON>", "rarity": "kEpic"}, "117014": {"name": "Cosmic Enchantress Lulu", "champion": "<PERSON>", "rarity": "kEpic"}, "117015": {"name": "Pajama Guardian Lulu", "champion": "<PERSON>", "rarity": "kEpic"}, "117026": {"name": "Space Groove Lulu", "champion": "<PERSON>", "rarity": "kEpic"}, "117027": {"name": "Prestige Space Groove Lulu", "champion": "<PERSON>", "rarity": "kMythic"}, "117037": {"name": "Monster Tamer <PERSON>", "champion": "<PERSON>", "rarity": "kEpic"}, "117046": {"name": "Cafe Cuties Lulu", "champion": "<PERSON>", "rarity": "kEpic"}, "119000": {"name": "Draven", "champion": "Draven", "rarity": "kNoRarity"}, "119001": {"name": "Soul Reaver Draven", "champion": "Draven", "rarity": "kEpic"}, "119002": {"name": "Gladiator Draven", "champion": "Draven", "rarity": "kNoRarity"}, "119003": {"name": "Primetime Draven", "champion": "Draven", "rarity": "kLegendary"}, "119004": {"name": "Pool Party Draven", "champion": "Draven", "rarity": "kNoRarity"}, "119005": {"name": "Beast Hunter Draven", "champion": "Draven", "rarity": "kNoRarity"}, "119006": {"name": "Draven Draven", "champion": "Draven", "rarity": "kNoRarity"}, "119012": {"name": "Santa Draven", "champion": "Draven", "rarity": "kEpic"}, "119013": {"name": "Mecha Kingdoms Draven", "champion": "Draven", "rarity": "kEpic"}, "119020": {"name": "<PERSON><PERSON><PERSON>", "champion": "Draven", "rarity": "kEpic"}, "119029": {"name": "<PERSON><PERSON><PERSON>", "champion": "Draven", "rarity": "kEpic"}, "119039": {"name": "Fright Night Draven", "champion": "Draven", "rarity": "kEpic"}, "119048": {"name": "La Ilusión Draven", "champion": "Draven", "rarity": "kEpic"}, "119058": {"name": "Grand Reckoning Draven", "champion": "Draven", "rarity": "kEpic"}, "120000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "120001": {"name": "Blood Knight He<PERSON>im", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "120002": {"name": "<PERSON>im", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "120003": {"name": "Headless <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "120004": {"name": "<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "120005": {"name": "<PERSON><PERSON> He<PERSON>im", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "120006": {"name": "Worldbreaker <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "120007": {"name": "Lancer <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "120008": {"name": "High Noon He<PERSON>im", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "120014": {"name": "Cosmic Charger Hecarim", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "120022": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "120031": {"name": "Winterbles<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "121000": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "121001": {"name": "<PERSON><PERSON>", "champion": "KhaZix", "rarity": "kEpic"}, "121002": {"name": "Guardian of the Sands Kha'Zix", "champion": "KhaZix", "rarity": "kNoRarity"}, "121003": {"name": "Death Blossom <PERSON><PERSON><PERSON>", "champion": "KhaZix", "rarity": "kNoRarity"}, "121004": {"name": "Dark Star Kha'Zix", "champion": "KhaZix", "rarity": "kEpic"}, "121011": {"name": "Worlds 2018 Kha'Zix", "champion": "KhaZix", "rarity": "kEpic"}, "121060": {"name": "Odyssey Kha'Zix", "champion": "KhaZix", "rarity": "kEpic"}, "121069": {"name": "Lunar Guardian Kha'<PERSON>", "champion": "KhaZix", "rarity": "kEpic"}, "121079": {"name": "<PERSON><PERSON>", "champion": "KhaZix", "rarity": "kMythic"}, "122000": {"name": "<PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "122001": {"name": "Lord <PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "122002": {"name": "Bioforge Darius", "champion": "<PERSON>", "rarity": "kNoRarity"}, "122003": {"name": "Woad King <PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "122004": {"name": "Dunkmaster <PERSON>", "champion": "<PERSON>", "rarity": "kLegendary"}, "122008": {"name": "Academy Darius", "champion": "<PERSON>", "rarity": "kNoRarity"}, "122014": {"name": "Dread<PERSON> Darius", "champion": "<PERSON>", "rarity": "kMythic"}, "122015": {"name": "God-King <PERSON>", "champion": "<PERSON>", "rarity": "kLegendary"}, "122016": {"name": "High Noon Darius", "champion": "<PERSON>", "rarity": "kEpic"}, "122024": {"name": "Lunar Beast Darius", "champion": "<PERSON>", "rarity": "kEpic"}, "122033": {"name": "Crime City Nightmare Darius", "champion": "<PERSON>", "rarity": "kEpic"}, "122043": {"name": "Spirit Blossom Darius", "champion": "<PERSON>", "rarity": "kEpic"}, "122054": {"name": "Porc<PERSON><PERSON> Darius", "champion": "<PERSON>", "rarity": "kEpic"}, "122064": {"name": "Divine God-King <PERSON>", "champion": "<PERSON>", "rarity": "kMythic"}, "122065": {"name": "Prestige Triumphant General <PERSON>", "champion": "<PERSON>", "rarity": "kMythic"}, "126000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "126001": {"name": "Full Metal Jayce", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "126002": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "126003": {"name": "Forsaken Jayce", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "126004": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "126005": {"name": "Battle Academia Jayce", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "126015": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "126024": {"name": "<PERSON><PERSON> Inventor <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kRare"}, "126025": {"name": "Zenith Games Jayce", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "126034": {"name": "T1 Jayce", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "126035": {"name": "Arcane Survivor <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "126036": {"name": "Prestige T1 Jayce", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "127000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "127001": {"name": "Bloodstone Lissandra", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "127002": {"name": "Blade Queen <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "127003": {"name": "<PERSON> <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "127004": {"name": "Coven Lissandra", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "127012": {"name": "Dark Cosmic Lissandra", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "127023": {"name": "Po<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "127033": {"name": "Prestige Porcelain Lissandra", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "127034": {"name": "Space Groove <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "131000": {"name": "<PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "131001": {"name": "<PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "131002": {"name": "Lunar Goddess Diana", "champion": "<PERSON>", "rarity": "kNoRarity"}, "131003": {"name": "Infernal Diana", "champion": "<PERSON>", "rarity": "kEpic"}, "131011": {"name": "Blood Moon Diana", "champion": "<PERSON>", "rarity": "kEpic"}, "131012": {"name": "<PERSON> Diana", "champion": "<PERSON>", "rarity": "kEpic"}, "131018": {"name": "Dragonslayer <PERSON>", "champion": "<PERSON>", "rarity": "kEpic"}, "131025": {"name": "Battle Queen Diana", "champion": "<PERSON>", "rarity": "kEpic"}, "131026": {"name": "Prestige Battle Queen Diana", "champion": "<PERSON>", "rarity": "kMythic"}, "131027": {"name": "Sentinel Diana", "champion": "<PERSON>", "rarity": "kEpic"}, "131037": {"name": "Firecracker Diana", "champion": "<PERSON>", "rarity": "kEpic"}, "131047": {"name": "Winterblessed <PERSON>", "champion": "<PERSON>", "rarity": "kLegendary"}, "131054": {"name": "Heavenscale Diana", "champion": "<PERSON>", "rarity": "kEpic"}, "131064": {"name": "Dark Cosmic Diana", "champion": "<PERSON>", "rarity": "kEpic"}, "131065": {"name": "Prestige Dark Cosmic Diana", "champion": "<PERSON>", "rarity": "kMythic"}, "133000": {"name": "<PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "133001": {"name": "<PERSON> Quinn", "champion": "<PERSON>", "rarity": "kNoRarity"}, "133002": {"name": "Woad <PERSON> Quinn", "champion": "<PERSON>", "rarity": "kNoRarity"}, "133003": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "133004": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "133005": {"name": "<PERSON>", "champion": "<PERSON>", "rarity": "kEpic"}, "133014": {"name": "Star Guardian Quinn", "champion": "<PERSON>", "rarity": "kEpic"}, "134000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "134001": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "134002": {"name": "Atlantean Syndra", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "134003": {"name": "Queen of Diamonds Syndra", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "134004": {"name": "Snow Day Syndra", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "134005": {"name": "SKT T1 Syndra", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "134006": {"name": "Star Guardian Syndra", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "134007": {"name": "Pool Party Syndra", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "134016": {"name": "Withered <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "134025": {"name": "Bewitching <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "134034": {"name": "Prestige Star Guardian Syndra", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "134044": {"name": "Spirit Blossom Syndra", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "134054": {"name": "Coven Syndra", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "134065": {"name": "Dumpling <PERSON><PERSON> Syndra", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "136000": {"name": "Aurelion Sol", "champion": "Aurelion Sol", "rarity": "kNoRarity"}, "136001": {"name": "Ashen Lord Aurel<PERSON>", "champion": "AurelionSol", "rarity": "kEpic"}, "136002": {"name": "Mecha Aurelion Sol", "champion": "AurelionSol", "rarity": "kEpic"}, "136011": {"name": "Storm Dragon Aurelion Sol", "champion": "AurelionSol", "rarity": "kEpic"}, "136021": {"name": "Inkshadow Aurelion Sol", "champion": "AurelionSol", "rarity": "kEpic"}, "136031": {"name": "Porcelain Protector Aurelion Sol", "champion": "AurelionSol", "rarity": "kLegendary"}, "141000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "141001": {"name": "Soulhunter <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "141002": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "141008": {"name": "Nightbringer Kayn", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "141009": {"name": "Prestige Nightbringer Kayn", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "141015": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "141020": {"name": "HEARTSTEEL Kayn", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "142000": {"name": "<PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "142001": {"name": "Cyber Pop Zoe", "champion": "<PERSON>", "rarity": "kEpic"}, "142002": {"name": "Pool Party Zoe", "champion": "<PERSON>", "rarity": "kEpic"}, "142009": {"name": "Star Guardian Zoe", "champion": "<PERSON>", "rarity": "kEpic"}, "142018": {"name": "Arcanist <PERSON>", "champion": "<PERSON>", "rarity": "kEpic"}, "142019": {"name": "Prestige Arcanist Zoe", "champion": "<PERSON>", "rarity": "kMythic"}, "142020": {"name": "EDG Zoe", "champion": "<PERSON>", "rarity": "kEpic"}, "142022": {"name": "Winter<PERSON>sed <PERSON>", "champion": "<PERSON>", "rarity": "kEpic"}, "142033": {"name": "Dark Star Zoe", "champion": "<PERSON>", "rarity": "kEpic"}, "143000": {"name": "Zyra", "champion": "Zyra", "rarity": "kNoRarity"}, "143001": {"name": "Wildfire Zyra", "champion": "Zyra", "rarity": "kNoRarity"}, "143002": {"name": "Haunted <PERSON><PERSON>", "champion": "Zyra", "rarity": "kEpic"}, "143003": {"name": "SKT T1 Zyra", "champion": "Zyra", "rarity": "kNoRarity"}, "143004": {"name": "Dragon Sorceress Zyra", "champion": "Zyra", "rarity": "kEpic"}, "143005": {"name": "Coven Zyra", "champion": "Zyra", "rarity": "kEpic"}, "143006": {"name": "Prestige Coven Zyra", "champion": "Zyra", "rarity": "kMythic"}, "143007": {"name": "Crystal Rose Z<PERSON>", "champion": "Zyra", "rarity": "kEpic"}, "143016": {"name": "Crime City Nightmare Zyra", "champion": "Zyra", "rarity": "kEpic"}, "143036": {"name": "Myth<PERSON>", "champion": "Zyra", "rarity": "kEpic"}, "143046": {"name": "Street Demons Zyra", "champion": "Zyra", "rarity": "kEpic"}, "143055": {"name": "Blood Moon Zyra", "champion": "Zyra", "rarity": "kEpic"}, "145000": {"name": "Kai'Sa", "champion": "Kai'Sa", "rarity": "kNoRarity"}, "145001": {"name": "Bullet Angel <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "145014": {"name": "K/DA Kai'Sa", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "145015": {"name": "Prestige K/DA Kai'Sa", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "145016": {"name": "iG Kai'Sa", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "145017": {"name": "Arcade Kai'Sa", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "145026": {"name": "K/DA ALL OUT Kai'Sa", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "145027": {"name": "Prestige K/DA ALL OUT Kai'Sa", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "145029": {"name": "Lagoon Dragon Kai'Sa", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "145039": {"name": "Prestige K/DA Kai'Sa (2022)", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "145040": {"name": "Star Guardian Kai'Sa", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "145048": {"name": "Inkshadow Kai'Sa", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "145059": {"name": "Heavenscale Kai'Sa", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "145069": {"name": "Dark Star Kai'Sa", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "147000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "147001": {"name": "K/DA ALL OUT Seraphine Indie", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kUltimate"}, "147004": {"name": "Graceful <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "147014": {"name": "Ocean Song Seraphine", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "147015": {"name": "Prestige Ocean Song Seraphine", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "147024": {"name": "Faerie Court Seraphine", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "147034": {"name": "Star Guardian Seraphine", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "147043": {"name": "Battle Dove Seraphine", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "147050": {"name": "Dumpling <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "150000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "150001": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "150002": {"name": "Gentleman <PERSON>nar", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "150003": {"name": "Snow Day Gnar", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "150004": {"name": "El León Gnar", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "150013": {"name": "Super Galaxy Gnar", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "150014": {"name": "SSG Gnar", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "150015": {"name": "Astronaut Gnar", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "150022": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "150031": {"name": "La Ilusión Gnar", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "154000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "154001": {"name": "Special Weapon Zac", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "154002": {"name": "Pool Party Zac", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "154006": {"name": "SKT T1 Zac", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "154007": {"name": "Battlecast Zac", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "154014": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "154024": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "157000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "157001": {"name": "High <PERSON>on <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "157002": {"name": "PROJECT: <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "157003": {"name": "Blood Moon Ya<PERSON>o", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "157009": {"name": "Nightbringer Ya<PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "157010": {"name": "<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "157017": {"name": "Battle Boss <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "157018": {"name": "True Damage Yasuo", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "157035": {"name": "Prestige True Damage Yasuo", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "157036": {"name": "Spirit Blossom <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "157045": {"name": "Sea Dog <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "157054": {"name": "Truth Dragon Yasuo", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "157055": {"name": "Dream Dragon Yasuo", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "157056": {"name": "Inkshadow Yasuo", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "157057": {"name": "Prestige Inkshadow Yasuo", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "157068": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "157077": {"name": "Battle <PERSON>o", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "157087": {"name": "Genesis Nightbringer Yasuo", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "161000": {"name": "Vel'Koz", "champion": "Vel'Koz", "rarity": "kNoRarity"}, "161001": {"name": "Battlecast Vel'Koz", "champion": "Velkoz", "rarity": "kEpic"}, "161002": {"name": "Arclight Vel'Koz", "champion": "Velkoz", "rarity": "kEpic"}, "161003": {"name": "Definitely Not Vel'Ko<PERSON>", "champion": "Velkoz", "rarity": "kNoRarity"}, "161004": {"name": "Infernal Vel'Koz", "champion": "Velkoz", "rarity": "kEpic"}, "161011": {"name": "Blackfrost Vel'Koz", "champion": "Velkoz", "rarity": "kEpic"}, "161020": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "Velkoz", "rarity": "kEpic"}, "163000": {"name": "Taliyah", "champion": "Taliyah", "rarity": "kNoRarity"}, "163001": {"name": "Freljord Taliyah", "champion": "Taliyah", "rarity": "kEpic"}, "163002": {"name": "SSG Taliyah", "champion": "Taliyah", "rarity": "kEpic"}, "163003": {"name": "Pool Party Taliyah", "champion": "Taliyah", "rarity": "kEpic"}, "163011": {"name": "Star Guardian Taliyah", "champion": "Taliyah", "rarity": "kEpic"}, "163021": {"name": "<PERSON><PERSON>", "champion": "Taliyah", "rarity": "kMythic"}, "164000": {"name": "<PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "164001": {"name": "Program Camille", "champion": "<PERSON>", "rarity": "kEpic"}, "164002": {"name": "Coven Camille", "champion": "<PERSON>", "rarity": "kEpic"}, "164010": {"name": "iG Camille", "champion": "<PERSON>", "rarity": "kEpic"}, "164011": {"name": "Arcana Camille", "champion": "<PERSON>", "rarity": "kEpic"}, "164021": {"name": "Strike Commander <PERSON>", "champion": "<PERSON>", "rarity": "kEpic"}, "164031": {"name": "Winterblessed Camille", "champion": "<PERSON>", "rarity": "kEpic"}, "164032": {"name": "Prestige Winterblessed Camille", "champion": "<PERSON>", "rarity": "kMythic"}, "166000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "166001": {"name": "Cyber Pop Akshan", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "166010": {"name": "<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "166020": {"name": "Three <PERSON> Akshan", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "200000": {"name": "Bel'Veth", "champion": "Bel'Veth", "rarity": "kNoRarity"}, "200001": {"name": "Battle Boss Bel'Veth", "champion": "Belveth", "rarity": "kEpic"}, "200010": {"name": "Cosmic Matriarch Bel'Veth", "champion": "Belveth", "rarity": "kEpic"}, "200019": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "Belveth", "rarity": "kEpic"}, "201000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "201001": {"name": "Dragonslayer <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "201002": {"name": "El Tigre Braum", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "201003": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "201010": {"name": "Santa Braum", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "201011": {"name": "Crime City Braum", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "201024": {"name": "Sugar Rush Braum", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "201033": {"name": "Pool Party Braum", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "202000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "202001": {"name": "High Noon Jhin", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "202002": {"name": "Blood Moon Jhin", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "202003": {"name": "SKT T1 Jhin", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "202004": {"name": "PROJECT: <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "202005": {"name": "Dark Cosmic Jhin", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "202014": {"name": "Shan Hai Scrolls Jhin", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "202023": {"name": "DWG Jhin", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "202025": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "202036": {"name": "Soul Fighter Jhin", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "202037": {"name": "Dark Cosmic Erasure Jhin", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "202047": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "203000": {"name": "Kindred", "champion": "Kindred", "rarity": "kNoRarity"}, "203001": {"name": "Shadowfire Kindred", "champion": "Kindred", "rarity": "kEpic"}, "203002": {"name": "Super Galaxy Kindred", "champion": "Kindred", "rarity": "kEpic"}, "203003": {"name": "Spirit Blossom Kindred", "champion": "Kindred", "rarity": "kEpic"}, "203012": {"name": "Porcelain Kindred", "champion": "Kindred", "rarity": "kEpic"}, "203022": {"name": "<PERSON><PERSON> and <PERSON> Kindred", "champion": "Kindred", "rarity": "kEpic"}, "203023": {"name": "DRX Kindred", "champion": "Kindred", "rarity": "kEpic"}, "203033": {"name": "Prestige Porcelain Kindred", "champion": "Kindred", "rarity": "kMythic"}, "203034": {"name": "<PERSON><PERSON> of the Wolf Kindred", "champion": "Kindred", "rarity": "kEpic"}, "221000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "221001": {"name": "Withered <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "221010": {"name": "Ocean Song Zeri", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "221019": {"name": "Immortal Journey <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "221028": {"name": "Fright Night Zeri", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "221029": {"name": "Prestige Fright Night Zeri", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "222000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "222001": {"name": "Crime City Jinx", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "222002": {"name": "Firecracker Jinx", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "222003": {"name": "Zombie Slayer Jin<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "222004": {"name": "Star Guardian Jinx", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "222012": {"name": "Ambitious <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "222013": {"name": "Odyssey Jinx", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "222020": {"name": "PROJECT: <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "222029": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "222037": {"name": "Arcane Enemy Jinx", "champion": "<PERSON><PERSON>", "rarity": "kRare"}, "222038": {"name": "Battle Cat Jinx", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "222040": {"name": "Prestige Battle Cat Jinx", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "222051": {"name": "Cafe Cuties Jinx", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "222060": {"name": "Arcane Fractured Jinx", "champion": "<PERSON><PERSON>", "rarity": "kExalted"}, "222062": {"name": "T1 Jinx", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "223000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "223001": {"name": "Master Chef <PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "223002": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "223003": {"name": "Coin Emperor <PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "223011": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "223020": {"name": "High Noon <PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "223030": {"name": "Shan Hai Scrolls Tahm <PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "233000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "233001": {"name": "Street Demons Briar", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "233010": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "234000": {"name": "Viego", "champion": "Viego", "rarity": "kNoRarity"}, "234001": {"name": "Lunar Beast Viego", "champion": "Viego", "rarity": "kEpic"}, "234010": {"name": "Dissonance of Pentakill Viego", "champion": "Viego", "rarity": "kEpic"}, "234019": {"name": "EDG Viego", "champion": "Viego", "rarity": "kEpic"}, "234021": {"name": "King <PERSON>", "champion": "Viego", "rarity": "kEpic"}, "234030": {"name": "Soul Fighter Viego", "champion": "Viego", "rarity": "kLegendary"}, "234037": {"name": "Worlds 2024 Viego", "champion": "Viego", "rarity": "kEpic"}, "235000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "235001": {"name": "True Damage Senna", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "235009": {"name": "Prestige True Damage Senna", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "235010": {"name": "High Noon Senna", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "235016": {"name": "PROJECT: <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "235026": {"name": "Lunar <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "235027": {"name": "Prestige Lunar Eclipse Senna", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "235036": {"name": "Bewitching <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "235046": {"name": "Star Guardian Senna", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "235056": {"name": "Winterblessed <PERSON>na", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "235063": {"name": "Masked Justice <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "236000": {"name": "Lucian", "champion": "Lucian", "rarity": "kNoRarity"}, "236001": {"name": "<PERSON><PERSON>", "champion": "Lucian", "rarity": "kNoRarity"}, "236002": {"name": "Striker <PERSON>", "champion": "Lucian", "rarity": "kNoRarity"}, "236006": {"name": "PROJECT: Lucian", "champion": "Lucian", "rarity": "kEpic"}, "236007": {"name": "Heartseek<PERSON>", "champion": "Lucian", "rarity": "kEpic"}, "236008": {"name": "High Noon Lucian", "champion": "Lucian", "rarity": "kLegendary"}, "236009": {"name": "<PERSON><PERSON><PERSON>", "champion": "Lucian", "rarity": "kEpic"}, "236018": {"name": "Pulsefire Lucian", "champion": "Lucian", "rarity": "kEpic"}, "236019": {"name": "Prestige Pulsefire Lucian", "champion": "Lucian", "rarity": "kMythic"}, "236025": {"name": "<PERSON><PERSON>", "champion": "Lucian", "rarity": "kNoRarity"}, "236031": {"name": "Arcana <PERSON>", "champion": "Lucian", "rarity": "kEpic"}, "236040": {"name": "Strike Paladin <PERSON>", "champion": "Lucian", "rarity": "kEpic"}, "236052": {"name": "Winterblessed Lucian", "champion": "Lucian", "rarity": "kEpic"}, "236062": {"name": "Masked Justice <PERSON>", "champion": "Lucian", "rarity": "kEpic"}, "238000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "238001": {"name": "Shockblade Zed", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "238002": {"name": "SKT T1 Zed", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "238003": {"name": "PROJECT: <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "238010": {"name": "Worlds 2016 Zed", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "238011": {"name": "Death Sworn Zed", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "238013": {"name": "Galaxy Slayer Zed", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "238015": {"name": "PsyOps Zed", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "238030": {"name": "Prestige PROJECT: Zed", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "238031": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "238038": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "238049": {"name": "Immortal Journey Zed", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "238058": {"name": "Blood Moon Zed", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "238068": {"name": "Quantum Galaxy Slayer Zed", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "240000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "240001": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "240002": {"name": "Count <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "240009": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "240018": {"name": "Kibble-<PERSON>led", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "245000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "245001": {"name": "Sandstorm Ekko", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "245002": {"name": "Academy Ekko", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "245003": {"name": "PROJECT: <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "245011": {"name": "SKT T1 Ekko", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "245012": {"name": "Trick or Treat <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "245019": {"name": "True Damage Ekko", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "245028": {"name": "Pulsefire Ekko", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "245036": {"name": "Arcane Firelight Ekko", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "245045": {"name": "Star Guardian Ekko", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "245046": {"name": "Prestige Star Guardian Ekko", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "245056": {"name": "Breakout True Damage Ekko", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "245057": {"name": "Arcane Last Stand Ekko", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "246000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "246001": {"name": "Battle Boss <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "246002": {"name": "True Damage Qiyana", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "246010": {"name": "Prestige True Damage Qiyana", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "246011": {"name": "Battle Queen <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "246020": {"name": "Shockblade Qiyana", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "246021": {"name": "Prestige True Damage Qiyana (2022)", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "246030": {"name": "Lunar Empress <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "246040": {"name": "La Ilusión Qiyana", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "254000": {"name": "Vi", "champion": "Vi", "rarity": "kNoRarity"}, "254001": {"name": "Neon Strike Vi", "champion": "Vi", "rarity": "kNoRarity"}, "254002": {"name": "Officer Vi", "champion": "Vi", "rarity": "kNoRarity"}, "254003": {"name": "<PERSON><PERSON><PERSON>", "champion": "Vi", "rarity": "kNoRarity"}, "254004": {"name": "Demon Vi", "champion": "Vi", "rarity": "kEpic"}, "254005": {"name": "Warring Kingdoms Vi", "champion": "Vi", "rarity": "kEpic"}, "254011": {"name": "PROJECT: Vi", "champion": "Vi", "rarity": "kEpic"}, "254012": {"name": "Heartbreaker Vi", "champion": "Vi", "rarity": "kEpic"}, "254020": {"name": "PsyOps Vi", "champion": "Vi", "rarity": "kEpic"}, "254029": {"name": "Arcane Undercity Vi", "champion": "Vi", "rarity": "kRare"}, "254030": {"name": "Heartache Vi", "champion": "Vi", "rarity": "kEpic"}, "254039": {"name": "Primal Ambush Vi", "champion": "Vi", "rarity": "kEpic"}, "254048": {"name": "<PERSON>ane Brawler Vi", "champion": "Vi", "rarity": "kLegendary"}, "266000": {"name": "Aatrox", "champion": "Aatrox", "rarity": "kNoRarity"}, "266001": {"name": "Justicar A<PERSON>rox", "champion": "Aatrox", "rarity": "kNoRarity"}, "266002": {"name": "Mecha Aatrox", "champion": "Aatrox", "rarity": "kEpic"}, "266003": {"name": "Sea Hunter Aatrox", "champion": "Aatrox", "rarity": "kNoRarity"}, "266007": {"name": "Blood Moon Aatrox", "champion": "Aatrox", "rarity": "kEpic"}, "266008": {"name": "Prestige Blood Moon Aatrox", "champion": "Aatrox", "rarity": "kMythic"}, "266009": {"name": "<PERSON><PERSON>", "champion": "Aatrox", "rarity": "kNoRarity"}, "266011": {"name": "Odyssey Aatrox", "champion": "Aatrox", "rarity": "kEpic"}, "266020": {"name": "Prestige Blood Moon Aatrox (2022)", "champion": "Aatrox", "rarity": "kMythic"}, "266021": {"name": "Lunar Eclipse Aatrox", "champion": "Aatrox", "rarity": "kEpic"}, "266030": {"name": "DRX Aatrox", "champion": "Aatrox", "rarity": "kEpic"}, "266031": {"name": "Prestige DRX Aatrox", "champion": "Aatrox", "rarity": "kMythic"}, "266033": {"name": "Primordian Aatrox", "champion": "Aatrox", "rarity": "kLegendary"}, "267000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "267001": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "267002": {"name": "River Spirit Nami", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "267003": {"name": "Urf the Nami-tee", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "267007": {"name": "Deep Sea Nami", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "267008": {"name": "SKT T1 Nami", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "267009": {"name": "Program Nami", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "267015": {"name": "Splendid Staff Nami", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "267024": {"name": "Cosmic Destiny Nami", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "267032": {"name": "Bewitching <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "267041": {"name": "Space Groove Nami", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "267042": {"name": "Prestige Space Groove Nami", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "267051": {"name": "Coven <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "267058": {"name": "Myth<PERSON> <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "268000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "268001": {"name": "Galactic Azir", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "268002": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "268003": {"name": "SKT T1 Azir", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "268004": {"name": "Warring Kingdoms Azir", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "268005": {"name": "Elderwood Azir", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "268014": {"name": "Worlds 2022 Azir", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "268019": {"name": "Attorney <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "350000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "350001": {"name": "Battle Principal <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "350011": {"name": "<PERSON>eeker <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "350019": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "350028": {"name": "Bewitching <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "350037": {"name": "EDG <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "350039": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "350049": {"name": "Cyber Cat <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "350050": {"name": "Prestige Cyber Cat Yuumi", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "360000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "360001": {"name": "PsyOps Sami<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "360010": {"name": "Space Groove Samira", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "360020": {"name": "High Noon Samira", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "360030": {"name": "Soul Fighter Samira", "champion": "<PERSON><PERSON>", "rarity": "kUltimate"}, "360033": {"name": "Masque of the Black Rose Samira", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "412000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "412001": {"name": "Deep Terror Thresh", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "412002": {"name": "Worlds 2013 Thresh", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "412003": {"name": "Blood Moon Thresh", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "412004": {"name": "SSW Thresh", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "412005": {"name": "Dark Star Thresh", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "412006": {"name": "High Noon Thresh", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "412013": {"name": "Pulsefire Thresh", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "412014": {"name": "Prestige Pulsefire Thresh", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "412015": {"name": "FPX Thresh", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "412017": {"name": "Spirit Blossom T<PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "412027": {"name": "Unbound Thresh", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "412028": {"name": "Steel Dragon Thresh", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "412038": {"name": "Prestige Pulsefire Thresh (2022)", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "412039": {"name": "Lunar Emperor <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "412049": {"name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "412059": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "420000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "420001": {"name": "Void Bringer <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "420002": {"name": "<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "420010": {"name": "Cosmic Invoker <PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "420018": {"name": "<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "420027": {"name": "Battle Bear Illaoi", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "421000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "421001": {"name": "Eternum Rek'Sai", "champion": "RekSai", "rarity": "kEpic"}, "421002": {"name": "Pool Party Rek'Sai", "champion": "RekSai", "rarity": "kEpic"}, "421009": {"name": "Blackfrost Rek'Sai", "champion": "RekSai", "rarity": "kEpic"}, "421017": {"name": "<PERSON>wood Re<PERSON>Sai", "champion": "RekSai", "rarity": "kEpic"}, "421026": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "RekSai", "rarity": "kEpic"}, "427000": {"name": "Ivern", "champion": "Ivern", "rarity": "kNoRarity"}, "427001": {"name": "<PERSON>", "champion": "Ivern", "rarity": "kEpic"}, "427002": {"name": "Dunkmaster <PERSON>", "champion": "Ivern", "rarity": "kEpic"}, "427011": {"name": "Old God Ivern", "champion": "Ivern", "rarity": "kEpic"}, "427020": {"name": "Astronaut Ivern", "champion": "Ivern", "rarity": "kEpic"}, "429000": {"name": "Kalista", "champion": "Kalista", "rarity": "kNoRarity"}, "429001": {"name": "Blood Moon Kalista", "champion": "Kalista", "rarity": "kNoRarity"}, "429002": {"name": "Worlds 2015 Kalista", "champion": "Kalista", "rarity": "kNoRarity"}, "429003": {"name": "SKT T1 Kalista", "champion": "Kalista", "rarity": "kNoRarity"}, "429005": {"name": "<PERSON><PERSON><PERSON>", "champion": "Kalista", "rarity": "kEpic"}, "429014": {"name": "Faerie Court Kalista", "champion": "Kalista", "rarity": "kEpic"}, "432000": {"name": "Bard", "champion": "Bard", "rarity": "kNoRarity"}, "432001": {"name": "<PERSON><PERSON> Bard", "champion": "Bard", "rarity": "kNoRarity"}, "432005": {"name": "Snow Day Bard", "champion": "Bard", "rarity": "kEpic"}, "432006": {"name": "Bard Bard", "champion": "Bard", "rarity": "kNoRarity"}, "432008": {"name": "Astronaut Bard", "champion": "Bard", "rarity": "kEpic"}, "432017": {"name": "Cafe Cuties Bard", "champion": "Bard", "rarity": "kEpic"}, "432026": {"name": "Shan <PERSON> Scrolls Bard", "champion": "Bard", "rarity": "kEpic"}, "432035": {"name": "T1 Bard", "champion": "Bard", "rarity": "kEpic"}, "497000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "497001": {"name": "Cosmic Dawn Rakan", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "497002": {"name": "Sweetheart Rakan", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "497003": {"name": "SSG Rakan", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "497004": {"name": "iG Rakan", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "497005": {"name": "Star Guardian Rakan", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "497009": {"name": "Elderwood Rakan", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "497018": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "497027": {"name": "Broken Covenant Rakan", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "497036": {"name": "Redeemed Star Guardian Rakan", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "497037": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "497038": {"name": "Prestige Dragonmancer <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "498000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "498001": {"name": "Cosmic Dusk Xayah", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "498002": {"name": "Sweetheart <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "498003": {"name": "SSG Xayah", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "498004": {"name": "Star Guardian X<PERSON>h", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "498008": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "498017": {"name": "Brave <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "498026": {"name": "Prestige Brave Phoenix <PERSON>h", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "498028": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "498037": {"name": "Broken Covenant Xayah", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "498038": {"name": "Redeemed Star Guardian X<PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "498047": {"name": "Battle Bat Xayah", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "516000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "516001": {"name": "Thunder Lord <PERSON>nn", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "516002": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "516011": {"name": "Space Groove Ornn", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "516020": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "517000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "517001": {"name": "Lunar <PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "517008": {"name": "Freljord Sylas", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "517013": {"name": "PROJECT: <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "517014": {"name": "Prestige PROJECT: <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "517024": {"name": "<PERSON> <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "517034": {"name": "Ashen <PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "517036": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "517046": {"name": "Dark Star Sylas", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "518000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "518001": {"name": "Winter Wonder Neeko", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "518010": {"name": "Star Guardian Neeko", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "518011": {"name": "Prestige Star Guardian Neeko", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "518012": {"name": "Shan <PERSON> Scrolls Neeko", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "518021": {"name": "Prestige Star Guardian Neeko (2022)", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kMythic"}, "518022": {"name": "Bewitching <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "518031": {"name": "Street Demons Neeko", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "518040": {"name": "Cosplayer <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "523000": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "523001": {"name": "Nightbri<PERSON> Aphelios", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "523009": {"name": "Lunar Beast Aphelios", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "523018": {"name": "EDG Aphelios", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "523020": {"name": "Spirit Blossom A<PERSON><PERSON>s", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "523030": {"name": "HEARTSTEEL Aphelios", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "526000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "526001": {"name": "Battle Queen <PERSON>ll", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "526010": {"name": "Star Guardian Rell", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "526020": {"name": "High Noon <PERSON>ll", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "526030": {"name": "Grand Reckoning Rell", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "555000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "555001": {"name": "Sand Wraith Pyke", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "555009": {"name": "Blood Moon Pyke", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "555016": {"name": "PROJECT: <PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "555025": {"name": "PsyOps Pyke", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "555034": {"name": "Sentinel Pyke", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "555044": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "555045": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "555053": {"name": "Soul Fighter Pyke", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "555054": {"name": "Prestige Soul Fighter Pyke", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "555064": {"name": "Fright Night Pyke", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "711000": {"name": "Vex", "champion": "Vex", "rarity": "kNoRarity"}, "711001": {"name": "Dawnbringer Vex", "champion": "Vex", "rarity": "kNoRarity"}, "711010": {"name": "Empyrean Vex", "champion": "Vex", "rarity": "kNoRarity"}, "777000": {"name": "Yone", "champion": "Yone", "rarity": "kNoRarity"}, "777001": {"name": "Spirit Blossom Yone", "champion": "Yone", "rarity": "kEpic"}, "777010": {"name": "Battle Academia Yone", "champion": "Yone", "rarity": "kEpic"}, "777019": {"name": "Dawnbringer Yone", "champion": "Yone", "rarity": "kLegendary"}, "777026": {"name": "Ocean Song Yone", "champion": "Yone", "rarity": "kEpic"}, "777035": {"name": "Inkshadow Yone", "champion": "Yone", "rarity": "kEpic"}, "777045": {"name": "HEARTSTEEL Yone", "champion": "Yone", "rarity": "kEpic"}, "777046": {"name": "Prestige HEARTSTEEL Yone", "champion": "Yone", "rarity": "kMythic"}, "777055": {"name": "High Noon Yone", "champion": "Yone", "rarity": "kLegendary"}, "777058": {"name": "Peacemaker High Noon Yone", "champion": "Yone", "rarity": "kMythic"}, "777065": {"name": "Masked Justice <PERSON>", "champion": "Yone", "rarity": "kEpic"}, "799000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "799001": {"name": "<PERSON><PERSON> of the Wolf Ambessa", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kLegendary"}, "800000": {"name": "<PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "800001": {"name": "<PERSON>ane Councilor <PERSON>", "champion": "<PERSON>", "rarity": "kEpic"}, "875000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "875001": {"name": "Mecha <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "875008": {"name": "Obsidian Dragon Sett", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "875009": {"name": "Prestige Obsidian Dragon Sett", "champion": "<PERSON><PERSON>", "rarity": "kMythic"}, "875010": {"name": "Pool Party Sett", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "875019": {"name": "Firecracker Sett", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "875038": {"name": "Spirit Blossom Sett", "champion": "<PERSON><PERSON>", "rarity": "kLegendary"}, "875045": {"name": "Soul Fighter Sett", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "875056": {"name": "HEARTSTEEL Sett", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "875066": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kExalted"}, "876000": {"name": "Lillia", "champion": "Lillia", "rarity": "kNoRarity"}, "876001": {"name": "Spirit Blossom Lillia", "champion": "Lillia", "rarity": "kEpic"}, "876010": {"name": "Nightbringer Lillia", "champion": "Lillia", "rarity": "kEpic"}, "876019": {"name": "Shan Hai Scrolls Lillia", "champion": "Lillia", "rarity": "kEpic"}, "876028": {"name": "Faerie Court Lillia", "champion": "Lillia", "rarity": "kEpic"}, "887000": {"name": "<PERSON>", "champion": "<PERSON>", "rarity": "kNoRarity"}, "887001": {"name": "Space Groove Gwen", "champion": "<PERSON>", "rarity": "kEpic"}, "887011": {"name": "Cafe Cuties Gwen", "champion": "<PERSON>", "rarity": "kEpic"}, "887020": {"name": "Soul Fighter Gwen", "champion": "<PERSON>", "rarity": "kEpic"}, "887030": {"name": "Battle Queen Gwen", "champion": "<PERSON>", "rarity": "kEpic"}, "888000": {"name": "Renata <PERSON>", "champion": "Renata <PERSON>", "rarity": "kNoRarity"}, "888001": {"name": "Admiral <PERSON><PERSON>", "champion": "Renata", "rarity": "kEpic"}, "888010": {"name": "Fright Night Renata Glasc", "champion": "Renata", "rarity": "kEpic"}, "888020": {"name": "La Ilusión Renata Glasc", "champion": "Renata", "rarity": "kEpic"}, "888021": {"name": "Prestige La Ilusión Renata Glasc", "champion": "Renata", "rarity": "kMythic"}, "888031": {"name": "Masque of the Black Rose Renata Glasc", "champion": "Renata", "rarity": "kNoRarity"}, "893000": {"name": "Aurora", "champion": "Aurora", "rarity": "kNoRarity"}, "893001": {"name": "Battle Bunny Aurora", "champion": "Aurora", "rarity": "kEpic"}, "895000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "895001": {"name": "Star Guardian Nilah", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "895011": {"name": "Coven <PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "897000": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "897001": {"name": "<PERSON><PERSON><PERSON><PERSON>", "champion": "KSante", "rarity": "kEpic"}, "897002": {"name": "Prestige Empyrean K'Sante", "champion": "KSante", "rarity": "kMythic"}, "897008": {"name": "HEARTSTEEL K'Sante", "champion": "KSante", "rarity": "kEpic"}, "901000": {"name": "<PERSON><PERSON>lder", "champion": "<PERSON><PERSON>lder", "rarity": "kNoRarity"}, "901001": {"name": "Heavenscale Smolder", "champion": "<PERSON><PERSON>lder", "rarity": "kEpic"}, "902000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "902001": {"name": "Faerie Court Milio", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "902011": {"name": "<PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "910000": {"name": "<PERSON><PERSON>", "champion": "<PERSON><PERSON>", "rarity": "kNoRarity"}, "910001": {"name": "Winterblessed Hwei", "champion": "<PERSON><PERSON>", "rarity": "kEpic"}, "950000": {"name": "<PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kNoRarity"}, "950001": {"name": "Soul Fighter Naafiri", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}, "950011": {"name": "PROJECT: <PERSON><PERSON><PERSON>", "champion": "<PERSON><PERSON><PERSON>", "rarity": "kEpic"}}