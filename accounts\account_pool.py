from typing import Dict, List, Optional, Any
from database.manager import DatabaseManager
from league_api.auth import Authenticator
from custom_logger.logger import logger
from dataclasses import dataclass
from datetime import datetime
import base64
import json
from league_api.penalties import check_leaver_penalty
from league_api.rank import get_rank_info
import socket
import os
import asyncio
import logging
import aiohttp
from bs4 import BeautifulSoup
from pathlib import Path
import requests
import re
import random
import time
from collections import defaultdict
import pytz
from sqlalchemy.ext.asyncio import AsyncSession

# Add a wrapper for DatabaseManager to ensure it has all required methods
class DatabaseManagerWrapper:
    """A wrapper for DatabaseManager that adds missing methods"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.db_path = db_manager.db_path
        logger.info(f"Created DatabaseManagerWrapper with path {self.db_path}")
    
    def __getattr__(self, name):
        # Check if the method exists on the wrapped db_manager
        if hasattr(self.db_manager, name):
            return getattr(self.db_manager, name)
        
        # Handle specific missing methods
        if name == "get_champions":
            return self.get_champions
        elif name == "get_skins":
            return self.get_skins
        elif name == "get_account_data":
            return self.get_account_data
        
        # If not found, raise AttributeError
        raise AttributeError(f"Neither DatabaseManagerWrapper nor DatabaseManager has attribute {name}")
    
    async def init_db(self):
        """Initialize the database"""
        return await self.db_manager.init_db()
    
    async def get_account_info(self, username: str):
        """Get account info from database"""
        return await self.db_manager.get_account_info(username)
    
    async def get_owned_champions(self, username: str):
        """Get owned champions from database"""
        return await self.db_manager.get_owned_champions(username)
    
    async def get_owned_skins(self, username: str):
        """Get owned skins from database"""
        return await self.db_manager.get_owned_skins(username)
    
    async def get_champions(self, username: str) -> List[Dict]:
        """Get champions with proper formatting for the HTML template"""
        try:
            # Try to use the method from the wrapped db_manager
            if hasattr(self.db_manager, 'get_champions'):
                return await self.db_manager.get_champions(username)
            
            # Fallback implementation
            champions = await self.db_manager.get_owned_champions(username)
            formatted_champions = []
            
            for champ in champions:
                formatted_champions.append({
                    'id': champ.get('champion_id', 0),
                    'name': champ.get('champion_name', 'Unknown'),
                    'purchase_date': champ.get('purchase_date', 'Unknown')
                })
            
            return formatted_champions
        except Exception as e:
            logger.error(f"Error in get_champions wrapper: {str(e)}")
            return []
    
    async def get_skins(self, username: str) -> List[Dict]:
        """Get skins with proper formatting for the HTML template"""
        try:
            # Try to use the method from the wrapped db_manager
            if hasattr(self.db_manager, 'get_skins'):
                return await self.db_manager.get_skins(username)
            
            # Fallback implementation
            skins = await self.db_manager.get_owned_skins(username)
            formatted_skins = []
            
            for skin in skins:
                # Format champion name for URL (remove spaces)
                champion_id = skin.get('champion_name', '').replace(' ', '')
                
                formatted_skins.append({
                    'skin_id': skin.get('skin_id', 0),
                    'name': skin.get('skin_name', 'Unknown'),
                    'champion_name': skin.get('champion_name', 'Unknown'),
                    'champion_id': champion_id,
                    'purchase_date': skin.get('purchase_date', 'Unknown'),
                    'rarity': skin.get('rarity', 'Unknown')
                })
            
            return formatted_skins
        except Exception as e:
            logger.error(f"Error in get_skins wrapper: {str(e)}")
            return []
    
    async def get_account_data(self, username: str) -> Optional[Dict]:
        """Get complete account data including champions and skins"""
        try:
            # Try to use the method from the wrapped db_manager
            if hasattr(self.db_manager, 'get_account_data'):
                return await self.db_manager.get_account_data(username)
            
            # Fallback implementation
            account_info = await self.db_manager.get_account_info(username)
            if not account_info:
                return None
            
            # Get champions and skins
            champions = await self.get_champions(username)
            skins = await self.get_skins(username)
            
            # Add champions and skins to account info
            account_info['champions'] = champions
            account_info['skins'] = skins
            
            return account_info
        except Exception as e:
            logger.error(f"Error in get_account_data wrapper: {str(e)}")
            return None
            
# Define SKIN_URL_PATTERNS at the module level
SKIN_URL_PATTERNS = [
    # Standard pattern
    {
        "url": "https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v2/inventoriesWithLoyalty",
        "location_format": "lolriot.aws-euc1-prod.euw"
    },
    # Instance-based pattern
    {
        "url": "https://euc1-red.pp.sgp.pvp.net/lolinventoryservice-ledge/v2/inventoriesWithLoyalty",
        "location_format": "lolriot.aws-euc1-prod.euw"
    },
    # Alternative endpoint
    {
        "url": "https://euc1-red.pp.sgp.pvp.net/lolinventoryservice-ledge/v1/inventories/CHAMPION_SKIN",
        "location_format": "lolriot.aws-euc1-prod.euw"
    },
    # NA-specific pattern
    {
        "url": "https://na-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v2/inventoriesWithLoyalty",
        "location_format": "lolriot.aws-usw2-prod.na1"
    }
]

def decode_jwt_payload(token: str) -> dict:
    """Decode the payload part of a JWT token."""
    if not token:
        return {}
    
    try:
        # Split the token into its components
        parts = token.split('.')
        if len(parts) != 3:
            logger.warning(f"Invalid JWT token format, expected 3 parts but got {len(parts)}")
            return {}
            
        # Decode the payload (middle part)
        # Add padding if needed
        payload_bytes = parts[1]
        payload_bytes += '=' * (4 - len(payload_bytes) % 4) if len(payload_bytes) % 4 != 0 else ''
        
        try:
            # Try standard base64 first
            decoded_bytes = base64.b64decode(payload_bytes)
        except Exception:
            # Try URL-safe base64
            decoded_bytes = base64.urlsafe_b64decode(payload_bytes)
        
        # Convert bytes to string and parse as JSON
        payload_str = decoded_bytes.decode('utf-8')
        return json.loads(payload_str)
    except Exception as e:
        logger.error(f"Error decoding JWT payload: {str(e)}")
        return {}

@dataclass
class Account:
    username: str
    password: str
    region: str
    region_code: str
    game_name: str = ""
    tag_line: str = ""
    summoner_level: int = 0
    blue_essence: int = 0
    riot_points: int = 0
    champions_owned: int = 0
    last_checked: str = ""
    created_at: str = ""
    penalty_minutes: int = 0
    is_banned: bool = False
    ban_info: str = ""
    chat_restrictions: str = ""
    ranked_games_remaining: int = 0
    rank_info: dict = None
    country: str = ""
    creation_region: str = ""
    is_glitch_account: bool = False
    authenticator: Optional[Authenticator] = None
    note: str = ""
    db_manager: Any = None
    
    # These token fields should be available on the account object
    refresh_token: str = ""
    access_token: str = ""
    id_token: str = ""

    def __post_init__(self):
        # Initialize rank_info with default values
        if self.rank_info is None:
            self.rank_info = {
                'solo': {'tier': 'UNRANKED', 'division': '', 'lp': 0, 'wins': 0, 'losses': 0, 
                        'previous_tier': 'UNRANKED', 'previous_division': ''},
                'flex': {'tier': 'UNRANKED', 'division': '', 'lp': 0, 'wins': 0, 'losses': 0, 
                        'previous_tier': 'UNRANKED', 'previous_division': ''},
                'season_history': []
            }
        elif 'season_history' not in self.rank_info:
            self.rank_info['season_history'] = []

    @property
    def ign(self) -> str:
        """Get formatted in-game name"""
        if self.game_name and self.tag_line:
            return f"{self.game_name}#{self.tag_line}"
        return self.username

    def init_authenticator(self):
        """Initialize authenticator if not already done"""
        if not self.authenticator:
            # Check for JWT token and extract region information
            need_region_update = False
            actual_region = None
            
            if hasattr(self, 'id_token') and self.id_token:
                try:
                    # Extract region from JWT payload
                    jwt_payload = decode_jwt_payload(self.id_token)
                    logger.info(f"Extracted JWT payload for {self.username}, keys: {list(jwt_payload.keys())}")
                    
                    # Dump all region-related fields for debugging
                    logger.info(f"JWT region debugging for {self.username}:")
                    logger.info(f"  lol field: {jwt_payload.get('lol', {})}")
                    logger.info(f"  region field: {jwt_payload.get('region', {})}")
                    logger.info(f"  original_platform_id: {jwt_payload.get('original_platform_id', '')}")
                    logger.info(f"  lol_region: {jwt_payload.get('lol_region', [])}")
                    logger.info(f"  dat field: {jwt_payload.get('dat', {})}")
                    
                    # Map region codes to regions
                    region_mapping = {
                        'EUW1': 'EUW',
                        'NA1': 'NA',
                        'EUN1': 'EUNE',
                        'LA1': 'LAN',
                        'LA2': 'LAS',
                        'BR1': 'BR',
                        'TR1': 'TR',
                        'RU': 'RU',
                        'JP1': 'JP',
                        'KR': 'KR',
                        'OC1': 'OCE'
                    }
                    
                    # Prioritized region extraction:
                    # 1. Check lol_region array first (most reliable for EUNE accounts)
                    if 'lol_region' in jwt_payload and jwt_payload['lol_region'] and isinstance(jwt_payload['lol_region'], list):
                        for region_entry in jwt_payload['lol_region']:
                            if isinstance(region_entry, dict) and 'cpid' in region_entry:
                                actual_region = region_mapping.get(region_entry['cpid'], region_entry['cpid'][:2])
                                logger.info(f"Using lol_region[].cpid to determine region for {self.username}: {actual_region}")
                                break
                    
                    # 2. Check lol field (most common in NA accounts) if lol_region didn't work
                    if not actual_region and 'lol' in jwt_payload:
                        lol_info = jwt_payload.get('lol', {})
                        if isinstance(lol_info, list) and lol_info and isinstance(lol_info[0], dict) and 'cpid' in lol_info[0]:
                            lol_region = lol_info[0]['cpid']
                            actual_region = region_mapping.get(lol_region, lol_region[:2])
                            logger.info(f"Using lol[0].cpid to determine region for {self.username}: {actual_region}")
                        elif isinstance(lol_info, dict) and 'cpid' in lol_info:
                            lol_region = lol_info['cpid']
                            actual_region = region_mapping.get(lol_region, lol_region[:2])
                            logger.info(f"Using lol.cpid to determine region for {self.username}: {actual_region}")
                    
                    # 3. Check original_platform_id if that didn't work
                    if not actual_region and 'original_platform_id' in jwt_payload and jwt_payload['original_platform_id']:
                        platform_id = jwt_payload['original_platform_id']
                        actual_region = region_mapping.get(platform_id, platform_id[:2])
                        logger.info(f"Using original_platform_id to determine region for {self.username}: {actual_region}")
                    
                    # 4. Check region.id 
                    if not actual_region and 'region' in jwt_payload and isinstance(jwt_payload['region'], dict) and 'id' in jwt_payload['region']:
                        region_id = jwt_payload['region']['id']
                        actual_region = region_mapping.get(region_id, region_id[:2])
                        logger.info(f"Using region.id to determine region for {self.username}: {actual_region}")
                    
                    # 5. Check region.tag
                    if not actual_region and 'region' in jwt_payload and isinstance(jwt_payload['region'], dict) and 'tag' in jwt_payload['region']:
                        tag_mapping = {
                            'na': 'NA',
                            'euw': 'EUW',
                            'eune': 'EUNE',
                            'lan': 'LAN',
                            'las': 'LAS',
                            'br': 'BR',
                            'tr': 'TR',
                            'ru': 'RU',
                            'jp': 'JP',
                            'kr': 'KR',
                            'oce': 'OCE'
                        }
                        region_tag = jwt_payload['region']['tag'].lower()
                        actual_region = tag_mapping.get(region_tag, region_tag.upper())
                        logger.info(f"Using region.tag to determine region for {self.username}: {actual_region}")
                        
                    # 6. Check dat.r (legacy option)
                    if not actual_region and 'dat' in jwt_payload and isinstance(jwt_payload['dat'], dict) and 'r' in jwt_payload['dat']:
                        jwt_region = jwt_payload['dat']['r']
                        actual_region = region_mapping.get(jwt_region, jwt_region[:2])
                        logger.info(f"Using dat.r to determine region for {self.username}: {actual_region}")
                    
                    # Check if we need to update the region
                    if actual_region:
                        if self.region != actual_region:
                            logger.info(f"Found region mismatch for {self.username}: DB has {self.region}, JWT indicates {actual_region}")
                            need_region_update = True
                        else:
                            logger.info(f"Found region in JWT for {self.username}: {actual_region}")
                    elif not actual_region:
                        logger.warning(f"Could not determine region from JWT for {self.username}, will try to authenticate without specifying region")
                    
                except Exception as e:
                    logger.error(f"Error extracting region from JWT for {self.username}: {str(e)}")
                    # Print the token data for debugging
                    if self.id_token:
                        try:
                            payload = decode_jwt_payload(self.id_token)
                            logger.error(f"JWT payload for debugging: {payload}")
                        except:
                            logger.error(f"Failed to decode JWT payload for debugging")
            
            # Also check creation_region and country if JWT check didn't determine a region
            if not actual_region and (self.creation_region == 'EUNE' or self.country in ['pol', 'cze', 'svk', 'hun', 'rou', 'bgr', 'grc', 'hrv']):
                actual_region = 'EUNE'
                need_region_update = self.region != 'EUNE'
                logger.info(f"Using EUNE region based on creation_region or country for {self.username}")
            
            # Update region if needed before creating authenticator
            if need_region_update and actual_region:
                logger.info(f"Updating account region from {self.region} to {actual_region} based on JWT token before authenticator init")
                self.region = actual_region
                self.region_code = actual_region
            
            # Two-stage initialization:
            # 1. Initialize without region to allow authentication to succeed
            # 2. After authentication, the region will be extracted from JWT and updated
                            
            # Either use the region we detected, or None if we couldn't determine it
            region_to_use = self.region
            logger.info(f"Initializing authenticator for {self.username} with region {region_to_use}")
            
            self.authenticator = Authenticator(
                username=self.username,
                password=self.password,
                region_code=region_to_use,
                verify_ssl=False
            )
            
            # Set tokens on authenticator if they exist on the account
            if hasattr(self, 'access_token') and hasattr(self, 'id_token'):
                if self.access_token and self.id_token:
                    self.authenticator.set_tokens(self.access_token, self.id_token)
                    logger.info(f"Set existing tokens from account object for account: {self.username}")
            
            # Set refresh token if it exists on the account
            if hasattr(self, 'refresh_token') and self.refresh_token:
                self.authenticator.refresh_token = self.refresh_token
                logger.info(f"Using refresh token for {self.username}: yes")
            
            logger.info(f"Authenticator initialized for {self.username} in region {region_to_use}")

class AccountPool:
    """Pool of accounts with authentication and data retrieval capabilities."""

    def __init__(self, db_manager: DatabaseManager = None):
        """Initialize the account pool."""
        if db_manager is None:
            db_manager = DatabaseManager()
        
        # If the db_manager is not already a DatabaseManagerWrapper, wrap it
        if not isinstance(db_manager, DatabaseManagerWrapper):
            self.db_manager = DatabaseManagerWrapper(db_manager)
            logger.info("Wrapped db_manager with DatabaseManagerWrapper in AccountPool")
        else:
            self.db_manager = db_manager
            logger.info("Using provided DatabaseManagerWrapper in AccountPool")

        self.accounts = {}
        self.lock = asyncio.Lock()
        self._accounts = []
        self.banned_accounts = []  # Initialize banned_accounts list
        self.skin_database = self._load_skin_database()

    def _load_skin_database(self) -> Dict[int, Dict[str, str]]:
        """Load skin database from JSON file or download from Community Dragon API if needed"""
        try:
            # Path to the skin database file
            json_db_path = 'data/skin_database.json'
            
            # Create data directory if it doesn't exist
            data_dir = Path('data')
            data_dir.mkdir(exist_ok=True)
            
            # Check if the database file exists
            if os.path.exists(json_db_path):
                # Load the existing database
                with open(json_db_path, 'r', encoding='utf-8') as f:
                    skin_db = json.load(f)
                logger.info(f"Loaded {len(skin_db)} skin entries from JSON database")
                return skin_db
            else:
                # Database file doesn't exist, download from Community Dragon API
                logger.info("Skin database file not found, downloading from Community Dragon API...")
                
                # URL for the Community Dragon skins.json file
                url = "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/skins.json"
                
                # Download the skins data
                response = requests.get(url)
                response.raise_for_status()  # Raise an exception for HTTP errors
                
                # Parse the JSON data
                skins_data = response.json()
                logger.info(f"Downloaded data for {len(skins_data)} skins from Community Dragon API")
                
                # Process the data into our format
                skin_db = {}
                for skin_id, skin_info in skins_data.items():
                    # Extract the champion name from the skin name or path
                    champion_name = "Unknown"
                    
                    # The champion name is often in the path
                    if "Characters" in skin_info.get("splashPath", ""):
                        path_parts = skin_info.get("splashPath", "").split("/")
                        for i, part in enumerate(path_parts):
                            if part == "Characters" and i + 1 < len(path_parts):
                                champion_name = path_parts[i + 1]
                                break
                    
                    # For base skins, the name is usually just the champion name
                    if skin_info.get("isBase", False) and skin_info.get("name", ""):
                        champion_name = skin_info.get("name", "Unknown")
                    
                    # Store in our database format
                    skin_db[skin_id] = {
                        "name": skin_info.get("name", f"Unknown Skin ({skin_id})"),
                        "champion": champion_name,
                        "rarity": skin_info.get("rarity", "Unknown")
                    }
                
                # Save the database to file
                with open(json_db_path, 'w', encoding='utf-8') as f:
                    json.dump(skin_db, f, indent=2, ensure_ascii=False)
                
                logger.info(f"Successfully created skin database with {len(skin_db)} entries")
                return skin_db
            
            # Fall back to the old translation file if JSON download fails
            translation_path = 'skin translation.txt'
            if os.path.exists(translation_path):
                with open(translation_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # Extract all tuples from the content
                    import ast
                    # Find all tuples in the format ("id", "name")
                    translations_str = content.replace("translations = {", "{")
                    translations = ast.literal_eval(translations_str)
                    
                    skin_db = {}
                    for skin_id, skin_name in translations:
                        # Some entries might be variant discounts or special cases
                        if skin_id.startswith("9"):  # Skip variant discounts and special cases
                            continue
                            
                        # For regular skins
                        try:
                            skin_id_int = int(skin_id)
                            # If skin name contains " / ", take the first part
                            if " / " in skin_name:
                                skin_name = skin_name.split(" / ")[0]
                            
                            # Try to extract champion name from skin name
                            # Most skin names are in format "Skin Name ChampionName" or "ChampionName Skin Name"
                            words = skin_name.split()
                            champion_name = words[-1]  # Default to last word as champion name
                            
                            # Store in database
                            skin_db[str(skin_id_int)] = {
                                'name': skin_name,
                                'champion': champion_name,
                                'rarity': 'Unknown'  # You can add rarity if available
                            }
                        except ValueError:
                            continue  # Skip if skin_id can't be converted to int
                    
                    logger.info(f"Loaded {len(skin_db)} skin translations from text file")
                    return skin_db
            else:
                logger.warning("No skin database found (neither JSON nor text file)")
                return {}
        except Exception as e:
            logger.error(f"Error loading skin database: {str(e)}")
            return {}

    async def _load_accounts(self):
        """Load accounts from database"""
        try:
            accounts_data = await self.db_manager.get_all_accounts()
            self._accounts = []
            
            for account_data in accounts_data:
                try:
                    # Log detailed account data for debugging
                    logger.info(f"Loading account {account_data.get('username')} from database")
                    logger.info(f"Account details: BE={account_data.get('blue_essence')}, RP={account_data.get('riot_points')}, Level={account_data.get('summoner_level')}")
                    logger.info(f"Account info: created_at={account_data.get('created_at')}, creation_region={account_data.get('creation_region')}, country={account_data.get('country')}")
                    logger.info(f"Game info: game_name={account_data.get('game_name')}, tag_line={account_data.get('tag_line')}")
                    
                    # Check JWT token first for region information
                    region = None
                    region_code = None
                    jwt_token = account_data.get('id_token', '')
                    
                    if jwt_token:
                        try:
                            # Extract region from JWT token
                            logger.info(f"Checking JWT token for region information for {account_data.get('username')}")
                            jwt_payload = decode_jwt_payload(jwt_token)
                            
                            # Log all region-related fields for debugging
                            logger.info(f"JWT region debugging for {account_data.get('username')}:")
                            logger.info(f"  lol field: {jwt_payload.get('lol', {})}")
                            logger.info(f"  region field: {jwt_payload.get('region', {})}")
                            logger.info(f"  original_platform_id: {jwt_payload.get('original_platform_id', '')}")
                            logger.info(f"  lol_region: {jwt_payload.get('lol_region', [])}")
                            logger.info(f"  dat field: {jwt_payload.get('dat', {})}")
                            
                            # Map region codes to regions
                            region_mapping = {
                                'EUW1': 'EUW',
                                'NA1': 'NA',
                                'EUN1': 'EUNE',
                                'LA1': 'LAN',
                                'LA2': 'LAS',
                                'BR1': 'BR',
                                'TR1': 'TR',
                                'RU': 'RU',
                                'JP1': 'JP',
                                'KR': 'KR',
                                'OC1': 'OCE'
                            }
                            
                            # Prioritized region extraction:
                            # 1. Check lol.cpid (most common in NA accounts)
                            if 'lol' in jwt_payload and isinstance(jwt_payload['lol'], dict) and 'cpid' in jwt_payload['lol']:
                                lol_region = jwt_payload['lol']['cpid']
                                region = region_mapping.get(lol_region, lol_region[:2])
                                region_code = region
                                logger.info(f"Using lol.cpid to determine region for {account_data.get('username')}: {region}")
                            
                            # 2. Check original_platform_id
                            elif 'original_platform_id' in jwt_payload and jwt_payload['original_platform_id']:
                                platform_id = jwt_payload['original_platform_id']
                                region = region_mapping.get(platform_id, platform_id[:2])
                                region_code = region
                                logger.info(f"Using original_platform_id to determine region for {account_data.get('username')}: {region}")
                            
                            # 3. Check region.id
                            elif 'region' in jwt_payload and isinstance(jwt_payload['region'], dict) and 'id' in jwt_payload['region']:
                                region_id = jwt_payload['region']['id']
                                region = region_mapping.get(region_id, region_id[:2])
                                region_code = region
                                logger.info(f"Using region.id to determine region for {account_data.get('username')}: {region}")
                            
                            # 4. Check region.tag
                            elif 'region' in jwt_payload and isinstance(jwt_payload['region'], dict) and 'tag' in jwt_payload['region']:
                                tag_mapping = {
                                    'na': 'NA',
                                    'euw': 'EUW',
                                    'eune': 'EUNE',
                                    'lan': 'LAN',
                                    'las': 'LAS',
                                    'br': 'BR',
                                    'tr': 'TR',
                                    'ru': 'RU',
                                    'jp': 'JP',
                                    'kr': 'KR',
                                    'oce': 'OCE'
                                }
                                region_tag = jwt_payload['region']['tag'].lower()
                                region = tag_mapping.get(region_tag, region_tag.upper())
                                region_code = region
                                logger.info(f"Using region.tag to determine region for {account_data.get('username')}: {region}")
                            
                            # 5. Check dat.r (legacy option)
                            elif 'dat' in jwt_payload and isinstance(jwt_payload['dat'], dict) and 'r' in jwt_payload['dat']:
                                jwt_region = jwt_payload['dat']['r']
                                region = region_mapping.get(jwt_region, jwt_region[:2])
                                region_code = region
                                logger.info(f"Using dat.r to determine region for {account_data.get('username')}: {region}")
                                
                            # 6. Check lol_region array
                            elif 'lol_region' in jwt_payload and jwt_payload['lol_region'] and isinstance(jwt_payload['lol_region'], list):
                                for region_entry in jwt_payload['lol_region']:
                                    if isinstance(region_entry, dict) and 'cpid' in region_entry:
                                        region = region_mapping.get(region_entry['cpid'], region_entry['cpid'][:2])
                                        region_code = region
                                        logger.info(f"Using lol_region[].cpid to determine region for {account_data.get('username')}: {region}")
                                        break
                                
                            if region:
                                logger.info(f"Found region in JWT for {account_data.get('username')}: {region}")
                            else:
                                logger.warning(f"No region found in JWT for {account_data.get('username')}")
                                
                        except Exception as e:
                            logger.error(f"Error extracting region from JWT token for {account_data.get('username')}: {str(e)}")
                            # Continue with database values
                    
                    # If region not found in JWT, use database values
                    if not region:
                        region = account_data.get('region', '')
                        # Ensure region_code is set properly
                        region_code = account_data.get('region_code')
                        if not region_code and region:
                            # If region_code is not set but region is, derive region_code from region
                            region_code = region.upper()[:3] if region else None
                    
                    # Create rank_info dictionary
                    rank_info = {
                        'solo': {
                            'tier': account_data.get('solo_tier', 'UNRANKED'),
                            'division': account_data.get('solo_division', ''),
                            'lp': account_data.get('solo_lp', 0),
                            'wins': account_data.get('solo_wins', 0),
                            'losses': account_data.get('solo_losses', 0),
                            'previous_tier': account_data.get('solo_previous_tier', 'UNRANKED'),
                            'previous_division': account_data.get('solo_previous_division', '')
                        },
                        'flex': {
                            'tier': account_data.get('flex_tier', 'UNRANKED'),
                            'division': account_data.get('flex_division', ''),
                            'lp': account_data.get('flex_lp', 0),
                            'wins': account_data.get('flex_wins', 0),
                            'losses': account_data.get('flex_losses', 0),
                            'previous_tier': account_data.get('flex_previous_tier', 'UNRANKED'),
                            'previous_division': account_data.get('flex_previous_division', '')
                        }
                    }
                    
                    # Add season history if available
                    if 'rank_info' in account_data and 'season_history' in account_data['rank_info']:
                        rank_info['season_history'] = account_data['rank_info']['season_history']
                        logger.info(f"Loaded {len(account_data['rank_info']['season_history'])} season history entries for {account_data.get('username')}")
                    else:
                        # Initialize empty season history
                        rank_info['season_history'] = []
                    
                    # If we still don't have a region_code, check userinfo
                    if not region_code:
                        userinfo = account_data.get('userinfo', {})
                        if isinstance(userinfo, dict):
                            # Check 'lol' and 'lol_region' fields
                            lol_info = userinfo.get('lol', {})
                            if lol_info and 'cpid' in lol_info:
                                # Extract region from cpid (e.g., 'NA1' -> 'NA')
                                region_from_lol = lol_info.get('cpid', '')[:2]
                                if region_from_lol:
                                    region_code = region_from_lol
                                    region = region_code
                                    logger.info(f"Extracted region from userinfo lol.cpid: {region_code}")
                            
                            # Check region object
                            region_obj = userinfo.get('region', {})
                            if not region_code and region_obj and 'id' in region_obj:
                                # Extract region from id (e.g., 'NA1' -> 'NA')
                                region_from_obj = region_obj.get('id', '')[:2]
                                if region_from_obj:
                                    region_code = region_from_obj
                                    region = region_code
                                    logger.info(f"Extracted region from userinfo region.id: {region_code}")
                    
                    # NEW: Check JWT token for region information if no region is set
                    id_token = account_data.get('id_token')
                    if not region_code and id_token:
                        try:
                            # Extract region from JWT payload
                            jwt_payload = decode_jwt_payload(id_token)
                            logger.info(f"Checking JWT token for region during account loading for {account_data.get('username')}")
                            
                            # Map region codes to regions
                            region_mapping = {
                                'EUW1': 'EUW',
                                'NA1': 'NA',
                                'EUN1': 'EUNE',
                                'LA1': 'LAN',
                                'LA2': 'LAS',
                                'BR1': 'BR',
                                'TR1': 'TR',
                                'RU': 'RU',
                                'JP1': 'JP',
                                'KR': 'KR',
                                'OC1': 'OCE'
                            }
                            
                            # Check lol_region array first (most reliable for EUNE accounts)
                            if 'lol_region' in jwt_payload and jwt_payload['lol_region'] and isinstance(jwt_payload['lol_region'], list):
                                for region_entry in jwt_payload['lol_region']:
                                    if isinstance(region_entry, dict) and 'cpid' in region_entry:
                                        jwt_region = region_entry['cpid']
                                        region_code = region_mapping.get(jwt_region, jwt_region[:2])
                                        region = region_code
                                        logger.info(f"Using lol_region[].cpid from JWT to determine region: {region_code}")
                                        break
                            
                            # Then check lol field if lol_region didn't work
                            if not region_code and 'lol' in jwt_payload:
                                lol_info = jwt_payload.get('lol', {})
                                if isinstance(lol_info, list) and lol_info and isinstance(lol_info[0], dict) and 'cpid' in lol_info[0]:
                                    jwt_region = lol_info[0]['cpid']
                                    region_code = region_mapping.get(jwt_region, jwt_region[:2])
                                    region = region_code
                                    logger.info(f"Using lol[0].cpid from JWT to determine region: {region_code}")
                            
                            # Then check original_platform_id if that didn't work
                            if not region_code and 'original_platform_id' in jwt_payload:
                                jwt_region = jwt_payload['original_platform_id']
                                region_code = region_mapping.get(jwt_region, jwt_region[:2])
                                region = region_code
                                logger.info(f"Using original_platform_id from JWT to determine region: {region_code}")
                            
                        except Exception as e:
                            logger.error(f"Error extracting region from JWT during account loading for {account_data.get('username')}: {str(e)}")
                    
                    # If we still don't have a region_code, only then default to NA
                    if not region_code:
                        # Check original_platform_id
                        original_platform = account_data.get('original_platform_id', '')
                        if original_platform:
                            region_code = original_platform[:2]
                            region = region_code
                            logger.info(f"Using region from original_platform_id: {region_code}")
                        else:
                            # Don't default to any region, leave both as None
                            region_code = None
                            region = None
                            logger.info(f"No region information found for {account_data.get('username')}, leaving region as None")
                    
                    # If either region or region_code is still not set, set them both to the same value
                    if region and not region_code:
                        region_code = region
                    elif region_code and not region:
                        region = region_code
                    
                    logger.info(f"Using region={region}, region_code={region_code} for account {account_data.get('username')}")
                    
                    # Create account object
                    account = Account(
                        username=account_data.get('username'),
                        password=account_data.get('password', ''),
                        region=region,
                        region_code=region_code,
                        game_name=account_data.get('game_name', ''),
                        tag_line=account_data.get('tag_line', ''),
                        summoner_level=account_data.get('summoner_level', 0),
                        blue_essence=account_data.get('blue_essence', 0),
                        riot_points=account_data.get('riot_points', 0),
                        champions_owned=account_data.get('champion_count', 0),
                        last_checked=account_data.get('last_checked', ''),
                        created_at=account_data.get('created_at', ''),
                        penalty_minutes=account_data.get('penalty_minutes', 0),
                        is_banned=bool(account_data.get('is_banned', False)),
                        ban_info=account_data.get('ban_info', ''),
                        chat_restrictions=account_data.get('chat_restrictions', ''),
                        ranked_games_remaining=account_data.get('ranked_games_remaining', 0),
                        rank_info=rank_info,
                        country=account_data.get('country', ''),
                        creation_region=account_data.get('creation_region', ''),
                        db_manager=self.db_manager
                    )
                    
                    # Set tokens if available
                    if account_data.get('refresh_token'):
                        account.refresh_token = account_data.get('refresh_token')
                    if account_data.get('access_token'):
                        account.access_token = account_data.get('access_token')
                    if account_data.get('id_token'):
                        account.id_token = account_data.get('id_token')
                    
                    self._accounts.append(account)
                    
                except Exception as e:
                    logger.error(f"Error loading account {account_data.get('username')}: {str(e)}")
            
            logger.info(f"Loaded {len(self._accounts)} accounts from database")
            return self._accounts
        except Exception as e:
            logger.error(f"Error loading accounts: {str(e)}")
            return []

    def get_all_accounts(self) -> List[Account]:
        """Get all accounts"""
        return self._accounts

    async def get_account_credentials(self, region: str) -> List[Account]:
        """Get accounts for specific region"""
        return [acc for acc in self._accounts if acc.region == region]

    async def check_account(self, account):
        """Check account status and update info"""
        try:
            # Log what we're doing for debugging
            logger.info(f"Starting check for account {account.username} with region {account.region}")
            
            # Initialize authenticator if it's None (should already be done by recheck_account)
            if not account.authenticator:
                # Force EUNE region if account is from an EUNE country
                if account.creation_region == 'EUNE' or account.country in ['pol', 'cze', 'svk', 'hun', 'rou', 'bgr', 'grc', 'hrv']:
                    account.region = 'EUNE'
                    account.region_code = 'EUNE'
                    logger.info(f"Forcing EUNE region for account {account.username} from {account.country}")
                
                account.init_authenticator()
                
            # Get userinfo first to get account details
            logger.info(f"Getting userinfo for {account.username}")
            userinfo = await account.authenticator.get_userinfo()
            if not userinfo:
                logger.error(f"Failed to get userinfo for {account.username}")
                return
                
            # Decode userinfo and get tokens
            logger.info(f"Got userinfo for {account.username}, decoding JWT")
            decoded_info = decode_jwt_payload(userinfo)
            
            # Check ban status
            ban_info = decoded_info.get('ban', {}).get('restrictions', [])
            if ban_info:
                # Account has some restriction
                logger.warning(f"Account {account.username} has restrictions")
                
                # Get ban reasons and check if any are permanent
                ban_reasons = []
                is_permanent_ban = False
                has_game_restriction = False
                chat_restriction_info = []

                for restriction in ban_info:
                    ban_type = restriction.get('type', 'unknown')
                    reason = restriction.get('reason', 'unknown')
                    expires_at = restriction.get('dat', {}).get('expirationMillis', 0)
                    
                    # Check if it's a permanent ban (no expiration) or a chat restriction
                    if expires_at and expires_at > 0:
                        # Not a permanent ban since it has expiration
                        logger.info(f"Restriction {ban_type} for {account.username} is temporary, expires at {expires_at}")
                    else:
                        # No expiration date indicates a permanent ban
                        is_permanent_ban = True
                        logger.warning(f"Restriction {ban_type} for {account.username} is permanent")
                    
                    # Check if it's a game restriction or just a chat restriction
                    if ban_type == "TEXT_CHAT_RESTRICTION":
                        # This is a chat restriction - store it but don't mark account as banned
                        chat_restriction_detail = f"{ban_type} - {reason}"
                        if expires_at and expires_at > 0:
                            chat_restriction_detail += f" (expires: {expires_at})"
                        chat_restriction_info.append(chat_restriction_detail)
                        logger.info(f"Chat restriction found for {account.username}: {chat_restriction_detail}")
                    else:
                        has_game_restriction = True
                        logger.warning(f"Game restriction {ban_type} for {account.username}: {reason}")

                # Store chat restriction information separately from ban info
                if chat_restriction_info:
                    account.chat_restrictions = ', '.join(chat_restriction_info)
                    # Make sure to save this to the database
                    try:
                        # Use the pool's db_manager instead of account.db_manager
                        await self.db_manager.update_account(
                            username=account.username,
                            chat_restrictions=account.chat_restrictions
                        )
                        logger.info(f"Saved chat restrictions for {account.username} to database")
                    except Exception as e:
                        logger.error(f"Error saving chat restrictions for {account.username}: {str(e)}")
                
                # Only mark as banned if it has a permanent ban or game restriction
                if is_permanent_ban or has_game_restriction:
                    # Create a readable ban info string from the restrictions
                    ban_reasons = []
                    for restriction in ban_info:
                        ban_type = restriction.get('type', 'UNKNOWN')
                        reason = restriction.get('reason', 'UNKNOWN')
                        ban_reasons.append(f"{ban_type} - {reason}")
                    
                    ban_info_string = ", ".join(ban_reasons)
                    logger.warning(f"Account {account.username} appears to be banned: {ban_info_string}")
                    logger.info(f"Testing next request for {account.username} to check if it's a glitch account")
                    
                    # Mark as potentially banned but don't save to database yet
                    account.is_banned = True
                    account.ban_info = ban_info_string
                    
                    # Set a flag to test if the account actually works
                    account_appears_banned = True
                else:
                    # The account has chat restrictions but is not banned
                    logger.info(f"Account {account.username} has chat restrictions but is not banned")
                    account_appears_banned = False
            else:
                # No restrictions found
                account_appears_banned = False
                    
            # Update account region based on userinfo/JWT if needed
            # Extract the real region from JWT token
            jwt_region = decoded_info.get('dat', {}).get('r', '')
            if jwt_region:
                # Map region codes to regions
                region_mapping = {
                    'EUW1': 'EUW',
                    'NA1': 'NA',
                    'EUN1': 'EUNE',
                    'LA1': 'LAN',
                    'LA2': 'LAS',
                    'BR1': 'BR',
                    'TR1': 'TR',
                    'RU': 'RU',
                    'JP1': 'JP',
                    'KR': 'KR',
                    'OC1': 'OCE'
                }
                
                # Update the account's region if it differs
                actual_region = region_mapping.get(jwt_region, jwt_region[:2])
                if actual_region != account.region:
                    logger.info(f"Updating account region from {account.region} to {actual_region} based on JWT token")
                    account.region = actual_region
                    account.region_code = actual_region
                    
                    # Update the region in the database
                    await self.db_manager.save_account_info(
                        account.username,
                        actual_region,
                        {'region': actual_region}
                    )

            # Get tokens
            try:
                logger.info(f"Getting tokens for {account.username}")
                queue_token = await account.authenticator.get_queue_token()
                session_token = await account.authenticator.get_session_token(queue_token)
                entitlements_token = await account.authenticator.get_entitlements()
                logger.info(f"Got all tokens for {account.username}")
                
                # If account appeared banned but tokens worked, it's a glitch account
                if account_appears_banned:
                    logger.info(f"Account {account.username} appeared banned but tokens work - it's a glitch account, continuing check")
                    # Reset the ban status since the account actually works
                    account.is_banned = False
                    account.is_glitch_account = True
                    account.ban_info = f"Glitch Account - Appeared banned but working: {account.ban_info}"
                else:
                    account.is_glitch_account = False
                
                # Get champion data
                try:
                    logger.info(f"Importing get_owned_champions for {account.username}")
                    from main import get_owned_champions
                    
                    # Determine region code for champions
                    region_code_for_champions = account.region_code
                    
                    # If region_code is not set, try to get it from the JWT token
                    if not region_code_for_champions:
                        # Try to get region from JWT token
                        jwt_region = decoded_info.get('dat', {}).get('r', '')
                        if jwt_region:
                            # Map region codes to regions
                            region_mapping = {
                                'EUW1': 'EUW',
                                'NA1': 'NA',
                                'EUN1': 'EUNE',
                                'LA1': 'LAN',
                                'LA2': 'LAS',
                                'BR1': 'BR',
                                'TR1': 'TR',
                                'RU': 'RU',
                                'JP1': 'JP',
                                'KR': 'KR',
                                'OC1': 'OCE'
                            }
                            region_code_for_champions = region_mapping.get(jwt_region, jwt_region[:2])
                            logger.info(f"Using region code from JWT token: {region_code_for_champions} for {account.username}")
                        else:
                            # Try to extract region from userinfo if available
                            if not region_code_for_champions and account.authenticator and account.authenticator.userinfo:
                                try:
                                    decoded_userinfo = decode_jwt_payload(account.authenticator.userinfo)
                                    # Check for original_platform_id
                                    original_platform = decoded_userinfo.get('original_platform_id', '')
                                    if original_platform:
                                        # Use region mapping instead of truncating to first two characters
                                        region_mapping = {
                                            'EUW1': 'EUW',
                                            'NA1': 'NA',
                                            'EUN1': 'EUNE',
                                            'LA1': 'LAN',
                                            'LA2': 'LAS',
                                            'BR1': 'BR',
                                            'TR1': 'TR',
                                            'RU': 'RU',
                                            'JP1': 'JP',
                                            'KR': 'KR',
                                            'OC1': 'OCE'
                                        }
                                        region_code_for_champions = region_mapping.get(original_platform, original_platform[:2])
                                        logger.info(f"Extracted region from userinfo original_platform_id: {region_code_for_champions}")
                                    
                                    # If still not found, check lol info
                                    if not region_code_for_champions:
                                        lol_info = decoded_userinfo.get('lol', {})
                                        if lol_info and 'cpid' in lol_info:
                                            cpid = lol_info.get('cpid', '')
                                            region_code_for_champions = region_mapping.get(cpid, cpid[:2])
                                            logger.info(f"Extracted region from userinfo lol.cpid: {region_code_for_champions}")
                                    
                                    # If still not found, check region object
                                    if not region_code_for_champions:
                                        region_obj = decoded_userinfo.get('region', {})
                                        if region_obj and 'id' in region_obj:
                                            region_id = region_obj.get('id', '')
                                            region_code_for_champions = region_mapping.get(region_id, region_id[:2])
                                            logger.info(f"Extracted region from userinfo region.id: {region_code_for_champions}")
                                except Exception as e:
                                    logger.error(f"Error extracting region from userinfo: {str(e)}")
                            
                            # If not in JWT or userinfo, use account.region
                            if not region_code_for_champions:
                                region_code_for_champions = account.region_code or account.region
                            logger.info(f"Using account region for champion data retrieval: {region_code_for_champions} for {account.username}")
                    else:
                        logger.info(f"Using existing region code for champion data retrieval: {region_code_for_champions} for {account.username}")
                    
                    logger.info(f"Calling get_owned_champions for {account.username} with region {region_code_for_champions}")
                    
                    # Get champion data
                    champion_data = await get_owned_champions(
                        session_token=session_token,
                        entitlements_token=entitlements_token,
                        puuid=decoded_info.get('sub', ''),
                        account_id=str(decoded_info.get('original_account_id', '')),
                        region_code=region_code_for_champions
                    )
                    logger.info(f"Returned from get_owned_champions for {account.username}")
                    
                    # Update account info
                    account.summoner_level = decoded_info.get('lol_account', {}).get('summoner_level', 0)
                    account.game_name = decoded_info.get('acct', {}).get('game_name', account.username)
                    account.tag_line = decoded_info.get('acct', {}).get('tag_line', '')
                    account.blue_essence = champion_data.get('blue_essence', 0)
                    account.riot_points = champion_data.get('riot_points', 0)
                    account.champions_owned = len(champion_data.get('champions', []))
                    account.last_checked = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    account.country = decoded_info.get('country', '')
                    
                    # Extract creation date and region
                    acct_info = decoded_info.get('acct', {})
                    created_at_timestamp = acct_info.get('created_at')
                    created_at = None
                    if created_at_timestamp:
                        try:
                            # Convert timestamp to readable date
                            created_at = datetime.fromtimestamp(created_at_timestamp / 1000).strftime("%Y-%m-%d %H:%M:%S")
                            logger.info(f"Extracted creation date for {account.username}: {created_at} from timestamp {created_at_timestamp}")
                        except Exception as e:
                            logger.error(f"Error converting creation timestamp: {str(e)}")
                    else:
                        # Try to get creation date from country_at as fallback
                        country_at = decoded_info.get('country_at')
                        if country_at:
                            try:
                                created_at = datetime.fromtimestamp(country_at / 1000).strftime("%Y-%m-%d %H:%M:%S")
                                logger.info(f"Using country_at as fallback for creation date for {account.username}: {created_at}")
                            except Exception as e:
                                logger.error(f"Error converting country_at timestamp: {str(e)}")
                    
                    # Get creation region from region info
                    creation_region = account.region  # Default to current region
                    region_info = decoded_info.get('region', {})
                    if region_info and isinstance(region_info, dict):
                        region_tag = region_info.get('tag', '')
                        if region_tag:
                            # Map region tag to region code
                            region_tag_mapping = {
                                'na': 'NA',
                                'euw': 'EUW',
                                'eune': 'EUNE',
                                'br': 'BR',
                                'lan': 'LAN',
                                'las': 'LAS',
                                'oce': 'OCE',
                                'tr': 'TR',
                                'ru': 'RU',
                                'jp': 'JP',
                                'kr': 'KR'
                            }
                            creation_region = region_tag_mapping.get(region_tag.lower(), creation_region)
                            logger.info(f"Extracted creation region for {account.username}: {creation_region} from tag {region_tag}")
                    
                    account.created_at = created_at
                    account.creation_region = creation_region
                    
                    # Create a comprehensive account info dictionary to save to database
                    account_info = {
                        'summoner_level': account.summoner_level,
                        'game_name': account.game_name,
                        'tag_line': account.tag_line,
                        'blue_essence': account.blue_essence,
                        'riot_points': account.riot_points,
                        'champion_count': account.champions_owned,
                        'last_checked': account.last_checked,
                        'country': account.country,
                        'creation_region': account.creation_region,
                        'created_at': account.created_at,
                        'is_glitch_account': getattr(account, 'is_glitch_account', False)
                        # Removed region_code as it's not a column in the database
                    }
                    
                    # Set account region based on region derived from JWT/userinfo if available
                    if region_code_for_champions and not account.region:
                        # Make sure to standardize region codes
                        region_standardization = {
                            'EUW1': 'EUW',
                            'NA1': 'NA',
                            'EUN1': 'EUNE',
                            'LA1': 'LAN',
                            'LA2': 'LAS',
                            'BR1': 'BR',
                            'TR1': 'TR',
                            'OCE1': 'OCE',
                            'OC1': 'OCE',
                            'JP1': 'JP'
                        }
                        
                        standardized_region = region_standardization.get(region_code_for_champions, region_code_for_champions)
                        account.region = standardized_region
                        logger.info(f"Setting account region to {standardized_region} based on API data")
                    
                    # Save account info to database
                    logger.info(f"Saving comprehensive account info for {account.username}: BE={account.blue_essence}, RP={account.riot_points}, Level={account.summoner_level}")
                    await self.db_manager.save_account_info(
                        account.username,
                        account.region,
                        account_info
                    )
                    
                    # Save champions to database
                    champions_to_save = []
                    for champ in champion_data.get('champions', []):
                        if isinstance(champ, dict) and 'id' in champ and 'name' in champ:
                            # Validate champion ID is within valid range (1-950)
                            try:
                                champ_id = int(champ['id'])
                                if 1 <= champ_id <= 950:
                                    champions_to_save.append({
                                        'id': champ_id,
                                        'name': champ['name'],
                                        'purchase_date': champ.get('purchase_date', '')
                                    })
                                else:
                                    logger.warning(f"Skipping invalid champion ID {champ_id} for {account.username}")
                            except (ValueError, TypeError):
                                logger.warning(f"Skipping invalid champion ID format {champ['id']} for {account.username}")
                    
                    if champions_to_save:
                        logger.info(f"Saving {len(champions_to_save)} champions for {account.username}")
                        await self.db_manager.save_champions(account.username, champions_to_save)
                    else:
                        logger.warning(f"No valid champions to save for {account.username}")
                    
                    # Get skin data
                    try:
                        logger.info(f"Getting skin data for {account.username}")
                        
                        # Determine region code for skins
                        region_code_for_skins = account.region_code
                        
                        # If region_code is not set, try to get it from the JWT token
                        if not region_code_for_skins:
                            # Try to get region from JWT token
                            jwt_region = decoded_info.get('dat', {}).get('r', '')
                            if jwt_region:
                                # Map region codes to regions
                                region_mapping = {
                                    'EUW1': 'EUW',
                                    'NA1': 'NA',
                                    'EUN1': 'EUNE',
                                    'LA1': 'LAN',
                                    'LA2': 'LAS',
                                    'BR1': 'BR',
                                    'TR1': 'TR',
                                    'RU': 'RU',
                                    'JP1': 'JP',
                                    'KR': 'KR',
                                    'OC1': 'OCE'
                                }
                                region_code_for_skins = region_mapping.get(jwt_region, jwt_region[:2])
                                logger.info(f"Using region code from JWT token for skin data: {region_code_for_skins} for {account.username}")
                            else:
                                # Try to extract region from userinfo if available
                                if not region_code_for_skins and account.authenticator and account.authenticator.userinfo:
                                    try:
                                        decoded_userinfo = decode_jwt_payload(account.authenticator.userinfo)
                                        # Check for original_platform_id
                                        original_platform = decoded_userinfo.get('original_platform_id', '')
                                        if original_platform:
                                            # Use region mapping instead of truncating to first two characters
                                            region_mapping = {
                                                'EUW1': 'EUW',
                                                'NA1': 'NA',
                                                'EUN1': 'EUNE',
                                                'LA1': 'LAN',
                                                'LA2': 'LAS',
                                                'BR1': 'BR',
                                                'TR1': 'TR',
                                                'RU': 'RU',
                                                'JP1': 'JP',
                                                'KR': 'KR',
                                                'OC1': 'OCE'
                                            }
                                            region_code_for_skins = region_mapping.get(original_platform, original_platform[:2])
                                            logger.info(f"Extracted skin region from userinfo original_platform_id: {region_code_for_skins}")
                                        
                                        # If still not found, check lol info
                                        if not region_code_for_skins:
                                            lol_info = decoded_userinfo.get('lol', {})
                                            if lol_info and 'cpid' in lol_info:
                                                cpid = lol_info.get('cpid', '')
                                                region_code_for_skins = region_mapping.get(cpid, cpid[:2])
                                                logger.info(f"Extracted skin region from userinfo lol.cpid: {region_code_for_skins}")
                                        
                                        # If still not found, check region object
                                        if not region_code_for_skins:
                                            region_obj = decoded_userinfo.get('region', {})
                                            if region_obj and 'id' in region_obj:
                                                region_id = region_obj.get('id', '')
                                                region_code_for_skins = region_mapping.get(region_id, region_id[:2])
                                                logger.info(f"Extracted skin region from userinfo region.id: {region_code_for_skins}")
                                    except Exception as e:
                                        logger.error(f"Error extracting region from userinfo for skins: {str(e)}")
                                
                                # If not in JWT or userinfo, use account.region
                                if not region_code_for_skins:
                                    region_code_for_skins = account.region_code or account.region
                                logger.info(f"Using account region for skin data retrieval: {region_code_for_skins} for {account.username}")
                        else:
                            logger.info(f"Using existing region code for skin data retrieval: {region_code_for_skins} for {account.username}")
                        
                        # Get skin data
                        skins = await self.get_owned_skins(
                            puuid=decoded_info.get('sub', ''),
                            region_code=region_code_for_skins
                        )
                        
                        # Process skins
                        processed_skins = []
                        for skin in skins:
                            if isinstance(skin, dict) and 'id' in skin and 'name' in skin:
                                # Get champion name from skin info
                                skin_info = self._get_skin_info(skin['id'])
                                processed_skin = {
                                    'id': skin['id'],
                                    'name': skin['name'],
                                    'champion_name': skin_info.get("champion", 'Unknown'),
                                    'purchase_date': skin.get('purchase_date', '')
                                }
                                processed_skins.append(processed_skin)
                        
                        # Save skins to database
                        if processed_skins:
                            logger.info(f"Saving {len(processed_skins)} skins for {account.username}")
                            await self.db_manager.save_skins(account.username, processed_skins)
                        else:
                            logger.warning(f"No skins to save for {account.username}")
                        
                        logger.info(f"Got skin data for {account.username}")
                    except Exception as e:
                        logger.error(f"Error getting skin data: {str(e)}")
                    
                    # Check penalties
                    try:
                        logger.info(f"Checking penalties for {account.username}")
                        try:
                            penalty_minutes, ranked_games = await check_leaver_penalty(
                                session_token,
                                account.region_code,
                                account.authenticator.session
                            )
                            account.penalty_minutes = penalty_minutes
                            account.ranked_games_remaining = ranked_games
                            logger.info(f"Got penalty info for {account.username}: {penalty_minutes} minutes, {ranked_games} games")
                        except Exception as e:
                            # Handle 403 error for NA accounts gracefully
                            if "403" in str(e) and account.region_code == "NA":
                                logger.info(f"Penalty info not available for NA account {account.username} (403 Forbidden)")
                                account.penalty_minutes = 0
                                account.ranked_games_remaining = 0
                            else:
                                logger.error(f"Failed to get penalty info: {str(e)}")
                                account.penalty_minutes = 0
                                account.ranked_games_remaining = 0
                        
                        # Update penalty info in database
                        await self.db_manager.save_account_info(
                            account.username,
                            account.region,
                            {
                                'penalty_minutes': account.penalty_minutes,
                                'ranked_games_remaining': account.ranked_games_remaining
                            }
                        )
                    except Exception as e:
                        logger.error(f"Error checking penalties: {str(e)}")
                        logger.error(f"Error details: {type(e).__name__}: {str(e)}")
                    
                    # Check rank
                    try:
                        logger.info(f"Checking rank for {account.game_name}#{account.tag_line}")

                        # Get the correct region code from various sources
                        region_code_for_rank = None
                        
                        # First try to get from JWT token
                        jwt_region = decoded_info.get('dat', {}).get('r', '')
                        if jwt_region:
                            if jwt_region == 'NA1':
                                region_code_for_rank = 'NA'
                            else:
                                region_code_for_rank = jwt_region
                        
                        # If not found in JWT, use account.region_code
                        if not region_code_for_rank and account.region_code:
                            region_code_for_rank = account.region_code
                        
                        # If still not found, use account.region
                        if not region_code_for_rank and account.region:
                            region_code_for_rank = account.region
                        
                        # Last resort, check the userinfo token to extract region
                        if not region_code_for_rank:
                            userinfo_token = account.authenticator.userinfo
                            if userinfo_token:
                                try:
                                    decoded_userinfo = decode_jwt_payload(userinfo_token)
                                    # Check for original_platform_id
                                    original_platform = decoded_userinfo.get('original_platform_id', '')
                                    if original_platform:
                                        region_code_for_rank = original_platform[:2]
                                    
                                    # Check for lol region info
                                    lol_info = decoded_userinfo.get('lol', {})
                                    if not region_code_for_rank and lol_info and 'cpid' in lol_info:
                                        region_code_for_rank = lol_info.get('cpid', '')[:2]
                                    
                                    # Check region object
                                    region_obj = decoded_userinfo.get('region', {})
                                    if not region_code_for_rank and region_obj and 'id' in region_obj:
                                        region_code_for_rank = region_obj.get('id', '')[:2]
                                except Exception as e:
                                    logger.error(f"Error extracting region from userinfo: {str(e)}")
                        
                        # If all else fails, default to EUW
                        if not region_code_for_rank:
                            region_code_for_rank = 'EUW'
                        
                        logger.info(f"Using region code {region_code_for_rank} for rank info retrieval")
                        
                        try:
                            rank_data = await get_rank_info(
                                session_token=session_token,
                                puuid=decoded_info.get('sub', ''),
                                region_code=region_code_for_rank,
                                session=account.authenticator.session,
                                entitlements_token=entitlements_token,
                                game_name=account.game_name,
                                tag_line=account.tag_line,
                                authenticator=account.authenticator
                            )
                            account.rank_info = rank_data
                            logger.info(f"Got rank info for {account.username}")
                            
                            # Extract rank info for database
                            solo_rank = rank_data.get('solo', {})
                            flex_rank = rank_data.get('flex', {})
                        except Exception as e:
                            logger.error(f"Error getting rank info: {str(e)}")
                            logger.error(f"Error details: {type(e).__name__}: {str(e)}")
                            # Initialize empty rank info
                            solo_rank = {}
                            flex_rank = {}
                        
                        # Save rank info to database
                        rank_info_to_save = {
                            'solo_tier': solo_rank.get('tier', 'UNRANKED'),
                            'solo_division': solo_rank.get('division', ''),
                            'solo_lp': solo_rank.get('lp', 0),
                            'solo_wins': solo_rank.get('wins', 0),
                            'solo_losses': solo_rank.get('losses', 0),
                            'solo_previous_tier': solo_rank.get('previous_tier', 'UNRANKED'),
                            'solo_previous_division': solo_rank.get('previous_division', ''),
                            'flex_tier': flex_rank.get('tier', 'UNRANKED'),
                            'flex_division': flex_rank.get('division', ''),
                            'flex_lp': flex_rank.get('lp', 0),
                            'flex_wins': flex_rank.get('wins', 0),
                            'flex_losses': flex_rank.get('losses', 0),
                            'flex_previous_tier': flex_rank.get('previous_tier', 'UNRANKED'),
                            'flex_previous_division': flex_rank.get('previous_division', ''),
                            'season_history': rank_data.get('season_history', [])
                        }
                        
                        # Add season history to the data to save
                        if 'season_history' in rank_data and rank_data['season_history']:
                            rank_info_to_save['season_history'] = rank_data['season_history']
                            logger.info(f"Including {len(rank_data['season_history'])} season history entries for {account.username}")
                        
                        logger.info(f"Saving rank info for {account.username}: Solo={rank_info_to_save['solo_tier']} {rank_info_to_save['solo_division']}, Flex={rank_info_to_save['flex_tier']} {rank_info_to_save['flex_division']}")
                        await self.db_manager.save_account_info(
                            account.username,
                            account.region,
                            rank_info_to_save
                        )
                    except Exception as e:
                        logger.error(f"Error checking rank: {str(e)}")
                
                except Exception as e:
                    logger.error(f"Error getting champion data: {str(e)}")
                    
                    # Update basic account info
                    account.summoner_level = decoded_info.get('lol_account', {}).get('summoner_level', 0)
                    account.game_name = decoded_info.get('acct', {}).get('game_name', account.username)
                    account.tag_line = decoded_info.get('acct', {}).get('tag_line', '')
                    account.last_checked = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    account.country = decoded_info.get('country', '')
                    account.creation_region = account.region
                    
                    # Save basic info to database
                    await self.db_manager.save_account_info(
                        account.username,
                        account.region,
                        {
                            'summoner_level': account.summoner_level,
                            'game_name': account.game_name,
                            'tag_line': account.tag_line,
                            'last_checked': account.last_checked,
                            'country': account.country,
                            'creation_region': account.creation_region
                        }
                    )
            except Exception as e:
                logger.error(f"Error getting tokens: {str(e)}")
                
                # If account appeared banned and tokens also failed, it's really banned
                if account_appears_banned:
                    logger.warning(f"Account {account.username} appeared banned and tokens failed - account is really banned, stopping check")
                    
                    # Save ban info to database
                    try:
                        await self.db_manager.update_account(
                            username=account.username,
                            is_banned=True,
                            ban_info=account.ban_info,
                            is_glitch_account=getattr(account, 'is_glitch_account', False)
                        )
                        logger.info(f"Updated ban status for {account.username} in database")
                    except Exception as db_error:
                        logger.error(f"Error updating ban status for {account.username}: {str(db_error)}")
                    
                    # Add to banned accounts list
                    self.banned_accounts.append(account.username)
                    return False
                
                # Update basic account info from userinfo
                account.summoner_level = decoded_info.get('lol_account', {}).get('summoner_level', 0)
                account.game_name = decoded_info.get('acct', {}).get('game_name', account.username)
                account.tag_line = decoded_info.get('acct', {}).get('tag_line', '')
                account.last_checked = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                account.country = decoded_info.get('country', '')
                account.creation_region = account.region
                
                # Save basic info to database
                await self.db_manager.save_account_info(
                    account.username,
                    account.region,
                    {
                        'summoner_level': account.summoner_level,
                        'game_name': account.game_name,
                        'tag_line': account.tag_line,
                        'last_checked': account.last_checked,
                        'country': account.country,
                        'creation_region': account.creation_region
                    }
                )
        except socket.gaierror as e:
            # Handle DNS resolution errors
            logger.error(f"DNS resolution error for {account.username}: {str(e)}")
            
            # Try to update basic account info if possible
            if hasattr(account, 'authenticator') and account.authenticator and hasattr(account.authenticator, 'userinfo'):
                try:
                    decoded_info = decode_jwt_payload(account.authenticator.userinfo)
                    
                    # Update basic account info
                    account.summoner_level = decoded_info.get('lol_account', {}).get('summoner_level', 0)
                    account.game_name = decoded_info.get('acct', {}).get('game_name', account.username)
                    account.tag_line = decoded_info.get('acct', {}).get('tag_line', '')
                    account.last_checked = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    account.country = decoded_info.get('country', '')
                    account.creation_region = account.region

                    # Save to database
                    await self.db_manager.save_account_info(
                        account.username,
                        account.region,
                        {
                            'summoner_level': account.summoner_level,
                            'game_name': account.game_name,
                            'tag_line': account.tag_line,
                            'last_checked': account.last_checked,
                            'country': account.country,
                            'creation_region': account.creation_region
                        }
                    )
                    logger.info(f"Saved basic info for {account.username} after DNS resolution error")
                except Exception as e:
                    logger.error(f"Error saving basic info after DNS error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error checking account {account.username}: {str(e)}")
            logger.error(f"Error details: {type(e).__name__}: {str(e)}")
            raise

    async def get_owned_skins(self, puuid, region_code):
        """Get owned skins for an account."""
        try:
            logger.info(f"Getting owned skins for account {puuid}")
            
            # Find the account with this puuid by checking userinfo tokens
            account = None
            for acc in self._accounts:
                if acc.authenticator:
                    try:
                        # Get userinfo token and extract the sub (puuid)
                        userinfo_token = await acc.authenticator.get_userinfo()
                        if userinfo_token:
                            userinfo_data = decode_jwt_payload(userinfo_token)
                            acc_puuid = userinfo_data.get('sub')
                            if acc_puuid == puuid:
                                account = acc
                                break
                    except Exception as e:
                        logger.error(f"Error checking account puuid: {str(e)}")
            
            if not account:
                logger.error(f"Could not find account for puuid {puuid}")
                return []
            
            logger.info(f"Found account for puuid {puuid}")
            
            # Get account ID from userinfo token
            try:
                userinfo_token = await account.authenticator.get_userinfo()
                userinfo_data = decode_jwt_payload(userinfo_token)
                account_id = str(userinfo_data.get('original_account_id', ''))
                logger.info(f"Extracted account ID {account_id} from userinfo token")
            except Exception as e:
                logger.error(f"Error getting account ID: {str(e)}")
                return []
            
            # Get tokens
            if not account.authenticator:
                logger.error(f"No authenticator for account")
                return []
            
            try:
                # Get queue token first
                queue_token = await account.authenticator.get_queue_token()
                if not queue_token:
                    logger.error(f"Failed to get queue token")
                    return []
                
                # Get session token using queue token
                session_token = await account.authenticator.get_session_token(queue_token)
                if not session_token:
                    logger.error(f"Failed to get session token")
                    return []
                
                # Get entitlements token
                entitlements_token = await account.authenticator.get_entitlements()
                if not entitlements_token:
                    logger.error(f"Failed to get entitlements token")
                    return []
                
                logger.info(f"Successfully retrieved all tokens for skin retrieval")
            except Exception as e:
                logger.error(f"Error getting tokens: {str(e)}")
                return []
            
            # Map region to data center using OpenBullet config approach
            region_to_datacenter = {
                'BR': 'usw',
                'LA1': 'usw',
                'LA2': 'usw',
                'NA': 'usw',
                'OC1': 'usw',
                'PBE1': 'usw',
                'RU': 'euc',
                'TR': 'euc',
                'EUNE': 'euc',
                'EUW': 'euc',
                'ME1': 'euc',
                'JP': 'apne',
                'KR': 'apne',
                'PH2': 'sea',
                'SG2': 'sea',
                'TH2': 'sea',
                'TW2': 'sea',
                'VN2': 'sea'
            }
            
            # Get datacenter from region
            region_upper = region_code.upper()
            datacenter = region_to_datacenter.get(region_upper, 'euc')
            
            # Construct URL
            base_url = f"https://{region_code.lower()}-red.lol.sgp.pvp.net"
            inventory_url = f"{base_url}/lolinventoryservice-ledge/v2/inventoriesWithLoyalty"
            
            # Construct location
            location_format = f"lolriot.aws-{datacenter}1-prod.{region_code.lower()}"
            
            # Set up headers - Use session_token directly as a string in the Authorization header
            headers = {
                "Authorization": f"Bearer {session_token}",
                "X-Riot-Entitlements-JWT": entitlements_token,
                "Accept": "application/json",
                "Content-Type": "application/json"
            }
            
            # Set up params
            params = {
                "puuid": puuid,
                "accountId": account_id,
                "inventoryTypes": "CHAMPION_SKIN",
                "location": location_format
            }
            
            # Make request
            try:
                logger.info(f"Making request to {inventory_url} for skin data")
                response = account.authenticator.session.get(inventory_url, headers=headers, params=params)
                if response.status_code != 200:
                    logger.error(f"Failed to get skin inventory: Status code {response.status_code}")
                    logger.error(f"Response: {response.text}")
                    return []
                
                data = response.json()
                logger.info(f"Successfully retrieved skin data response")
            except Exception as e:
                logger.error(f"Error making request for skin data: {str(e)}")
                return []
            
            # Extract skins
            try:
                skins = []
                if "data" in data and "items" in data["data"]:
                    items = data["data"]["items"]
                    logger.info(f"Found items in skin data response: {items}")
                    
                    # Handle different possible formats of the items data
                    if isinstance(items, dict):
                        # Case 1: items is a dictionary with CHAMPION_SKIN as a key
                        if "CHAMPION_SKIN" in items:
                            champion_skin_data = items["CHAMPION_SKIN"]
                            logger.info(f"Found CHAMPION_SKIN in items: {champion_skin_data}")
                            
                            # Handle case where CHAMPION_SKIN is a list
                            if isinstance(champion_skin_data, list):
                                logger.info(f"CHAMPION_SKIN is a list with {len(champion_skin_data)} items")
                                for item in champion_skin_data:
                                    if isinstance(item, dict) and "itemId" in item:
                                        skin_id = item["itemId"]
                                        try:
                                            skin_info = self._get_skin_info(skin_id)
                                            skin_name = skin_info.get("name", f"Unknown Skin {skin_id}")
                                            purchase_date = item.get("purchaseDate", "")
                                            
                                            skins.append({
                                                "id": skin_id,
                                                "name": skin_name,
                                                "purchase_date": purchase_date
                                            })
                                        except Exception as skin_error:
                                            logger.error(f"Error processing skin ID {skin_id}: {str(skin_error)}")
                            
                            # Handle case where CHAMPION_SKIN is a string (possibly JSON)
                            elif isinstance(champion_skin_data, str):
                                logger.info(f"CHAMPION_SKIN is a string, attempting to parse as JSON")
                                try:
                                    import json
                                    parsed_data = json.loads(champion_skin_data)
                                    if isinstance(parsed_data, list):
                                        logger.info(f"Successfully parsed CHAMPION_SKIN as a list with {len(parsed_data)} items")
                                        for item in parsed_data:
                                            if isinstance(item, dict) and "itemId" in item:
                                                skin_id = item["itemId"]
                                                try:
                                                    skin_info = self._get_skin_info(skin_id)
                                                    skin_name = skin_info.get("name", f"Unknown Skin {skin_id}")
                                                    purchase_date = item.get("purchaseDate", "")
                                                    
                                                    skins.append({
                                                        "id": skin_id,
                                                        "name": skin_name,
                                                        "purchase_date": purchase_date
                                                    })
                                                except Exception as skin_error:
                                                    logger.error(f"Error processing skin ID {skin_id}: {str(skin_error)}")
                                except Exception as json_error:
                                    logger.error(f"Error parsing CHAMPION_SKIN as JSON: {str(json_error)}")
                        
                        # Case 2: items is a dictionary with other keys
                        else:
                            logger.info("Items is a dictionary without CHAMPION_SKIN key, checking all values")
                            for key, value in items.items():
                                logger.info(f"Checking key {key} with value type {type(value)}")
                                if isinstance(value, list):
                                    for item in value:
                                        if isinstance(item, dict) and "itemId" in item:
                                            skin_id = item["itemId"]
                                            try:
                                                skin_info = self._get_skin_info(skin_id)
                                                skin_name = skin_info.get("name", f"Unknown Skin {skin_id}")
                                                purchase_date = item.get("purchaseDate", "")
                                                
                                                skins.append({
                                                    "id": skin_id,
                                                    "name": skin_name,
                                                    "purchase_date": purchase_date
                                                })
                                            except Exception as skin_error:
                                                logger.error(f"Error processing skin ID {skin_id}: {str(skin_error)}")
                    
                    # Case 3: items is a list
                    elif isinstance(items, list):
                        logger.info(f"Items is a list with {len(items)} elements")
                        for item in items:
                            if isinstance(item, dict) and "itemId" in item:
                                skin_id = item["itemId"]
                                try:
                                    skin_info = self._get_skin_info(skin_id)
                                    skin_name = skin_info.get("name", f"Unknown Skin {skin_id}")
                                    purchase_date = item.get("purchaseDate", "")
                                    
                                    skins.append({
                                        "id": skin_id,
                                        "name": skin_name,
                                        "purchase_date": purchase_date
                                    })
                                except Exception as skin_error:
                                    logger.error(f"Error processing skin ID {skin_id}: {str(skin_error)}")
                    
                    # Case 4: items is something else
                    else:
                        logger.warning(f"Unexpected items type: {type(items)}")
                        logger.warning(f"Items content: {items}")
                
                logger.info(f"Extracted {len(skins)} skins from response")
                return skins
            except Exception as e:
                logger.error(f"Error extracting skin data: {str(e)}")
                return []
                
        except Exception as e:
            logger.error(f"Error in get_owned_skins: {str(e)}")
            # Use string formatting instead of traceback to avoid import issues
            logger.error(f"Error details: {type(e).__name__}: {str(e)}")
            return []

    def _get_skin_info(self, skin_id: int) -> Dict:
        """Get skin information from the skin database"""
        try:
            # Load skin database if not already loaded
            if not hasattr(self, '_skin_db'):
                self._skin_db = self._load_skin_database()
                logger.info(f"Loaded skin database with {len(self._skin_db)} entries")
            
            skin_id_str = str(skin_id)
            logger.info(f"Looking up skin info for ID: {skin_id_str}")
            
            # First check if it's a main skin
            if skin_id_str in self._skin_db:
                skin_info = self._skin_db[skin_id_str]
                logger.info(f"Found skin info: {skin_info}")
                return {
                    'name': skin_info.get('name', f'Unknown Skin ({skin_id})'),
                    'champion': self._extract_champion_from_skin(skin_info),
                    'rarity': skin_info.get('rarity', 'Unknown')
                }
            
            # If not a main skin, check if it's a chroma
            for main_skin_id, skin_info in self._skin_db.items():
                if "chromas" in skin_info and skin_info["chromas"]:
                    for chroma in skin_info["chromas"]:
                        if str(chroma.get("id")) == skin_id_str:
                            # Found the chroma
                            champion = self._extract_champion_from_skin(skin_info)
                            
                            # Get the base skin name
                            base_skin_name = skin_info.get('name', f'Unknown Skin ({main_skin_id})')
                            
                            # Try to get a descriptive name for the chroma
                            chroma_description = ""
                            
                            # Check if there's a description
                            if "descriptions" in chroma and chroma["descriptions"]:
                                for desc in chroma["descriptions"]:
                                    if "description" in desc:
                                        chroma_description = desc["description"]
                                        break
                            
                            # If no description, try to use color
                            if not chroma_description and "colors" in chroma and chroma["colors"]:
                                chroma_description = f"Color: {chroma['colors'][0]}"
                            
                            # Create a name that includes the base skin and chroma info
                            if chroma_description:
                                # Extract a short version of the description
                                short_desc = self._extract_short_chroma_description(chroma_description)
                                skin_name = f"{base_skin_name} ({short_desc})"
                            else:
                                skin_name = f"{base_skin_name} (Chroma)"
                            
                            logger.info(f"Found chroma info: {skin_name} for champion {champion}")
                            return {
                                'name': skin_name,
                                'champion': champion,
                                'rarity': 'Chroma'
                            }
            
            # If not found, try to determine champion from skin ID
            champion_id = skin_id // 1000
            champion_name = self._get_champion_name_from_id(champion_id)
            
            logger.warning(f"Skin not found in database: {skin_id_str}, champion_id: {champion_id}, champion_name: {champion_name}")
            return {
                'name': f'Unknown Skin ({skin_id})',
                'champion': champion_name if champion_name else 'Unknown'
            }
            
        except Exception as e:
            logger.error(f"Error getting skin info: {str(e)}")
            return {'name': f'Unknown Skin ({skin_id})', 'champion': 'Unknown'}
            
    def _get_champion_name_from_id(self, champion_id: int) -> str:
        """Get champion name from champion ID"""
        try:
            # Load champion database if not already loaded
            if not hasattr(self, '_champion_db'):
                self._champion_db = self._load_champion_database()
                logger.info(f"Loaded champion database with {len(self._champion_db)} entries")
            
            champion_id_str = str(champion_id)
            logger.info(f"Looking up champion name for ID: {champion_id_str}")
            
            if champion_id_str in self._champion_db:
                champion_name = self._champion_db[champion_id_str].get('name', 'Unknown')
                logger.info(f"Found champion name: {champion_name} for ID: {champion_id_str}")
                return champion_name
            
            # Log the database contents for debugging
            logger.warning(f"Champion ID {champion_id_str} not found in database. Available IDs: {list(self._champion_db.keys())[:10]}...")
            return f'Unknown Champion ({champion_id_str})'
        except Exception as e:
            logger.error(f"Error getting champion name from ID: {str(e)}")
            return f'Unknown Champion ({champion_id_str})'
    
    def _extract_short_chroma_description(self, description: str) -> str:
        """Extract a short description from a chroma description"""
        try:
            # For ranked rewards, extract the rank
            if "reached" in description and "rank" in description:
                # Try to extract the rank
                rank_keywords = ["Bronze", "Silver", "Gold", "Platinum", "Diamond", "Master", "Grandmaster", "Challenger"]
                for rank in rank_keywords:
                    if rank in description:
                        return rank
            
            # For bundle exclusives
            if "bundle exclusive" in description:
                return "Bundle"
            
            # For event exclusives
            if "Loot exclusive" in description and "event" in description:
                # Try to extract the event name
                event_start = description.find("in the ") + 7
                if event_start > 7:  # Found "in the "
                    event_end = description.find(" event", event_start)
                    if event_end > 0:
                        return description[event_start:event_end]
                return "Event"
            
            # For chromas with specific colors
            color_keywords = ["Ruby", "Sapphire", "Emerald", "Pearl", "Obsidian", "Rose Quartz", "Tanzanite", "Turquoise"]
            for color in color_keywords:
                if color in description:
                    return color
            
            # For ranked chromas
            rank_keywords = ["Bronze", "Silver", "Gold", "Platinum", "Diamond"]
            for rank in rank_keywords:
                if rank in description:
                    return rank
            
            # Default to a short version only if it's not an award message
            if len(description) > 20 and "award" not in description.lower():
                return description[:20] + "..."
            return description
        except Exception:
            return "Chroma"
    
    def _extract_champion_from_skin(self, skin_info: Dict) -> str:
        """Extract champion name from skin info"""
        try:
            # The champion name is often in the path
            if "splashPath" in skin_info and "Characters" in skin_info["splashPath"]:
                path_parts = skin_info["splashPath"].split("/")
                for i, part in enumerate(path_parts):
                    if part == "Characters" and i + 1 < len(path_parts):
                        return path_parts[i + 1]
            
            # For base skins, the name is usually just the champion name
            if skin_info.get("isBase", False) and "name" in skin_info:
                return skin_info["name"]
            
            # If all else fails, return Unknown
            return "Unknown"
        except Exception:
            return "Unknown"

    async def get_account_id(self, puuid: str, region: str) -> str:
        """Get account ID from PUUID."""
        try:
            # For now, just return the PUUID as the account ID
            # In a real implementation, you would make an API call to get the account ID
            return puuid
        except Exception as e:
            logger.error(f"Error getting account ID: {str(e)}")
            return ""

    async def copy_account_info(self, username, profile_name):
        """Copy account info according to the profile settings"""
        try:
            logger.info(f"Copying account info for {username} with profile {profile_name}")
            
            # Get account data
            account_data = await self.db_manager.get_account_data(username)
            if not account_data:
                logger.error(f"No data found for account {username}")
                return
            
            # Load profile settings
            try:
                with open('settings.json', 'r') as f:
                    profiles = json.load(f)
                
                if profile_name not in profiles:
                    logger.error(f"Profile {profile_name} not found in settings.json")
                    return
                
                profile = profiles[profile_name]
            except Exception as e:
                logger.error(f"Error loading profile {profile_name}: {str(e)}")
                return
            
            # Create clipboard content based on profile settings
            clipboard_lines = []
            
            # User:Pass
            if profile.get('User:Pass', False):
                clipboard_lines.append(f"{account_data.get('username', '')}:{account_data.get('password', '')}")
            
            # Username
            # if profile.get('Username', False):
               # clipboard_lines.append(f"Username: {account_data.get('username', '')}")
            
            # IGN (In-Game Name)
            if profile.get('IGN', False):
                game_name = account_data.get('game_name', '')
                tag_line = account_data.get('tag_line', '')
                if game_name and tag_line:
                    clipboard_lines.append(f"IGN: {game_name}#{tag_line}")
                elif game_name:
                    clipboard_lines.append(f"IGN: {game_name}")
            
            # Region
            if profile.get('Region', False):
                clipboard_lines.append(f"Region: {account_data.get('region', '')}")

                        # Penalties
            if profile.get('Penalties', False):
                penalty_minutes = account_data.get('penalty_minutes', 0)
                if penalty_minutes > 0:
                    clipboard_lines.append(f"Penalties: {penalty_minutes} minutes")
                else:
                    clipboard_lines.append("Penalties: None")
                
                is_banned = account_data.get('is_banned', False)
                if is_banned:
                    ban_info = account_data.get('ban_info', '')
                    clipboard_lines.append(f"Banned: Yes - {ban_info}")
            
            # Country
            if profile.get('Country', False):
                clipboard_lines.append(f"Country: {account_data.get('country', '')}")
            
            # Creation Region
            if profile.get('Creation Region', False):
                clipboard_lines.append(f"Creation Region: {account_data.get('creation_region', '')}")
            
            # Account Created
            if profile.get('Account Created', False):
                clipboard_lines.append(f"Account Created: {account_data.get('created_at', '')}")
            
            # Summoner Level
            if profile.get('Summoner Level', False):
                clipboard_lines.append(f"Summoner Level: {account_data.get('summoner_level', '')}")
            
            # Blue Essence
            if profile.get('Blue Essence', False):
                clipboard_lines.append(f"Blue Essence: {account_data.get('blue_essence', '')}")
            
            # Riot Points
            if profile.get('Riot Points', False):
                clipboard_lines.append(f"Riot Points: {account_data.get('riot_points', '')}")
            
            # Ranked Info
            if profile.get('Ranked Info', False):
                solo_tier = account_data.get('solo_tier', 'UNRANKED')
                solo_division = account_data.get('solo_division', '')
                solo_lp = account_data.get('solo_lp', None)
                
                flex_tier = account_data.get('flex_tier', 'UNRANKED')
                flex_division = account_data.get('flex_division', '')
                flex_lp = account_data.get('flex_lp', 0)
                
                solo_rank = f"{solo_tier} {solo_division}" if solo_division else solo_tier
                flex_rank = f"{flex_tier} {flex_division}" if flex_division else flex_tier
                
                if solo_lp and solo_tier != 'UNRANKED':
                    clipboard_lines.append(f"SoloQ Rank: {solo_rank} ({solo_lp} LP)")
                else:
                    clipboard_lines.append(f"SoloQ Rank: {solo_rank}")

            if flex_lp and flex_tier != 'UNRANKED':
                clipboard_lines.append(f"Flex Rank: {flex_rank} ({flex_lp} LP)")
            else:
                    clipboard_lines.append(f"Flex Rank: {flex_rank}")
            
            # Champions Owned
            if profile.get('Champions Owned', False):
                champions_count = len(account_data.get('champions', []))
                clipboard_lines.append(f"Champions Owned: {champions_count}")
            
            # Last Checked
            if profile.get('Last Checked', False):
                clipboard_lines.append(f"Last Checked: {account_data.get('last_checked', '')}")
            
            # First Champions
            if profile.get('First Champions', False):
                champions = account_data.get('champions', [])
                logger.info(f"DEBUG: Found {len(champions)} champions for first 5 check")
                
                # Print first few champions to debug
                for i, c in enumerate(champions[:3]):
                    logger.info(f"DEBUG: Champion {i+1}: {c.get('name', 'Unknown')} - Purchase date: {c.get('purchase_date', 'None')}")
                
                # Sort by purchase date
                champions.sort(key=lambda x: x.get('purchase_date', ''), reverse=False)
                logger.info(f"DEBUG: Sorted champions by purchase date")
                
                # Get first 5 champions
                first_champions = champions[:5]
                logger.info(f"DEBUG: Got first {len(first_champions)} champions")
                
                if first_champions:
                    clipboard_lines.append("First 5 Champions:")
                    for champion in first_champions:
                        name = champion.get('name', 'Unknown')
                        purchase_date = champion.get('purchase_date', 'Unknown')
                        clipboard_lines.append(f"- {name} ({purchase_date})")
                    logger.info(f"DEBUG: Added {len(first_champions)} champions to clipboard")
                else:
                    logger.info(f"DEBUG: No first champions found after sorting")
            else:
                logger.info(f"DEBUG: First Champions option is disabled in profile")
            
            # Join all lines and copy to clipboard
            clipboard_content = "\n".join(clipboard_lines)
            
            # Copy to clipboard
            # We'll use pyperclip which needs to be run in the main thread
            # Return the content so the GUI can handle clipboard operations
            return clipboard_content
        except Exception as e:
            logger.error(f"Error copying account info: {str(e)}")
            logger.error(f"Error details: {type(e).__name__}: {str(e)}")
            return None
    
    async def copy_champions_info(self, username):
        """Copy champions info to clipboard"""
        try:
            logger.info(f"Copying champions info for {username}")
            
            # Get account data
            account_data = await self.db_manager.get_account_data(username)
            if not account_data:
                logger.error(f"No data found for account {username}")
                return
            
            # Create champions list
            champions = account_data.get('champions', [])
            
            # Sort champions alphabetically by name
            champions.sort(key=lambda x: x.get('name', ''))
            
            # Create clipboard content
            clipboard_lines = [f"Champions for {username} ({len(champions)}):", ""]
            
            for champion in champions:
                name = champion.get('name', 'Unknown')
                purchase_date = champion.get('purchase_date', 'Unknown')
                clipboard_lines.append(f"{name} - Purchased: {purchase_date}")
            
            # Join all lines and copy to clipboard
            clipboard_content = "\n".join(clipboard_lines)
            
            # Return the content so the GUI can handle clipboard operations
            return clipboard_content
        except Exception as e:
            logger.error(f"Error copying champions info: {str(e)}")
            logger.error(f"Error details: {type(e).__name__}: {str(e)}")
            return None
    
    async def copy_skins_info(self, username):
        """Copy skins info to clipboard"""
        try:
            logger.info(f"Copying skins info for {username}")
            
            # Get account data
            account_data = await self.db_manager.get_account_data(username)
            if not account_data:
                logger.error(f"No data found for account {username}")
                return
            
            # Create skins list
            skins = account_data.get('skins', [])
            
            # Don't include chromas by default
            filtered_skins = [skin for skin in skins if not self._is_chroma(skin.get('name', ''), skin.get('skin_id', 0))]
            
            # Sort skins by champion name and then skin name
            filtered_skins.sort(key=lambda x: (x.get('champion_name', ''), x.get('name', '')))
            
            # Create clipboard content
            clipboard_lines = [f"Skins for {username} ({len(filtered_skins)}):", ""]
            
            current_champion = None
            for skin in filtered_skins:
                champion_name = skin.get('champion_name', 'Unknown')
                skin_name = skin.get('name', 'Unknown')
                purchase_date = skin.get('purchase_date', 'Unknown')
                
                # Add champion header if it's a new champion
                if current_champion != champion_name:
                    clipboard_lines.append(f"\n{champion_name}:")
                    current_champion = champion_name
                
                clipboard_lines.append(f"  - {skin_name} - Purchased: {purchase_date}")
            
            # Join all lines and copy to clipboard
            clipboard_content = "\n".join(clipboard_lines)
            
            # Return the content so the GUI can handle clipboard operations
            return clipboard_content
        except Exception as e:
            logger.error(f"Error copying skins info: {str(e)}")
            logger.error(f"Error details: {type(e).__name__}: {str(e)}")
            return None
    
    async def copy_champions_and_skins_info(self, username):
        """Copy both champions and skins info to clipboard"""
        try:
            logger.info(f"Copying champions and skins info for {username}")
            
            # Get champions and skins info
            champions_content = await self.copy_champions_info(username)
            skins_content = await self.copy_skins_info(username)
            
            # Combine the content
            clipboard_content = f"{champions_content}\n\n{'-' * 50}\n\n{skins_content}"
            
            # Return the content so the GUI can handle clipboard operations
            return clipboard_content
        except Exception as e:
            logger.error(f"Error copying champions and skins info: {str(e)}")
            logger.error(f"Error details: {type(e).__name__}: {str(e)}")
            return None
    
    async def copy_all_accounts_info(self, include_skins=False, include_chromas=False):
        """Copy info for all accounts"""
        try:
            logger.info(f"Copying info for all accounts (include_skins={include_skins}, include_chromas={include_chromas})")
            
            # Get all accounts
            accounts = await self._load_accounts()
            
            # Create clipboard content
            clipboard_lines = [f"All Accounts ({len(accounts)}):", ""]
            
            for account in accounts:
                # Add account header
                clipboard_lines.append(f"\n{'-' * 50}")
                clipboard_lines.append(f"Account: {account.username}")
                clipboard_lines.append(f"Region: {account.region}")
                clipboard_lines.append(f"IGN: {account.game_name}#{account.tag_line}" if account.game_name and account.tag_line else f"Username: {account.username}")
                clipboard_lines.append(f"Level: {account.summoner_level}")
                clipboard_lines.append(f"BE: {account.blue_essence} | RP: {account.riot_points}")
                
                # Add account creation info
                if account.created_at:
                    clipboard_lines.append(f"Created: {account.created_at}")
                if account.creation_region:
                    clipboard_lines.append(f"Creation Region: {account.creation_region}")
                if account.country:
                    clipboard_lines.append(f"Country: {account.country}")
                
                # Add penalty info if any
                if account.penalty_minutes > 0:
                    clipboard_lines.append(f"Penalty: {account.penalty_minutes} minutes")
                if account.is_banned:
                    clipboard_lines.append(f"Banned: Yes")
                    if account.ban_info:
                        clipboard_lines.append(f"Ban Info: {account.ban_info}")
                
                # Add ranked info
                if account.rank_info:
                    # Solo/Duo queue
                    solo_rank = account.rank_info.get('solo', {})
                    solo_tier = solo_rank.get('tier', 'UNRANKED')
                    solo_division = solo_rank.get('division', '')
                    solo_lp = solo_rank.get('lp', None)
                    
                    # Flex queue
                    flex_rank = account.rank_info.get('flex', {})
                    flex_tier = flex_rank.get('tier', 'UNRANKED')
                    flex_division = flex_rank.get('division', '')
                    flex_lp = flex_rank.get('lp', None)
                    
                    solo_rank_str = f"{solo_tier} {solo_division}" if solo_division else solo_tier
                    flex_rank_str = f"{flex_tier} {flex_division}" if flex_division else flex_tier
                    
                    # Only show LP if not unranked
                    solo_lp_str = f" ({solo_lp} LP)" if solo_tier != 'UNRANKED' else ""
                    flex_lp_str = f" ({flex_lp} LP)" if flex_tier != 'UNRANKED' else ""
                    
                    clipboard_lines.append(f"Solo: {solo_rank_str}{solo_lp_str}")
                    clipboard_lines.append(f"Flex: {flex_rank_str}{flex_lp_str}")
                
                # Add last checked info
                if account.last_checked:
                    clipboard_lines.append(f"Last Checked: {account.last_checked}")
                
                # Add note if exists
                if account.note:
                    clipboard_lines.append(f"Note: {account.note}")
                
                clipboard_lines.append("")  # Add blank line before champions section
                
                # Get champions and skins data
                champions = await self.db_manager.get_champions(account.username)
                skins = await self.db_manager.get_skins(account.username)
                
                # Champions count and list
                clipboard_lines.append(f"Champions for {account.game_name} ({len(champions)}):")
                clipboard_lines.append("")
                
                # Sort champions by purchase date
                champions.sort(key=lambda x: x.get('purchase_date', ''))
                
                # Make sure champion database is loaded
                if not hasattr(self, '_champion_db') or not self._champion_db:
                    self._champion_db = self._load_champion_database()
                    logger.info(f"Loaded champion database with {len(self._champion_db)} entries for copy_all_accounts_info")
                
                for champion in champions:
                    # Get champion name, using database lookup
                    champion_id = str(champion.get('id', '0'))
                    if champion_id in self._champion_db:
                        name = self._champion_db[champion_id].get('name', f'Unknown Champion ({champion_id})')
                    else:
                        name = champion.get('name', f'Unknown Champion ({champion_id})')
                    
                    purchase_date = champion.get('purchase_date', 'Unknown')
                    if purchase_date != 'Unknown':
                        # Format the date
                        try:
                            if 'T' in purchase_date:
                                purchase_date = purchase_date.split('.')[0]  # Remove milliseconds
                                dt = datetime.strptime(purchase_date, '%Y%m%dT%H%M%S')
                                purchase_date = dt.strftime('%B %d, %Y %H:%M:%S')
                        except:
                            pass
                    clipboard_lines.append(f"{name} - Purchased: {purchase_date}")
                
                # Add skins if requested
                if include_skins:
                    # Filter out chromas if not including them
                    if not include_chromas:
                        skins = [skin for skin in skins if not self._is_chroma(skin.get('name', ''), skin.get('skin_id', 0))]
                    
                    clipboard_lines.append(f"\nSkins for {account.game_name} ({len(skins)}):")
                    clipboard_lines.append("")
                    
                    # Sort skins by champion name and purchase date
                    skins.sort(key=lambda x: (x.get('champion_name', ''), x.get('purchase_date', '')))
                    
                    # Group skins by champion
                    current_champion = None
                    for skin in skins:
                        champion_name = skin.get('champion_name', 'Unknown')
                        skin_id = skin.get('skin_id', 0)
                        purchase_date = skin.get('purchase_date', 'Unknown')
                        
                        # Get skin name from database
                        skin_info = self._get_skin_info(skin_id)
                        skin_name = skin_info.get('name', f'Unknown Skin ({skin_id})')
                        
                        if purchase_date != 'Unknown':
                            # Format the date
                            try:
                                if 'T' in purchase_date:
                                    purchase_date = purchase_date.split('.')[0]  # Remove milliseconds
                                    dt = datetime.strptime(purchase_date, '%Y%m%dT%H%M%S')
                                    purchase_date = dt.strftime('%B %d, %Y %H:%M:%S')
                            except:
                                pass
                        
                        # Add champion header if it's a new champion
                        if current_champion != champion_name:
                            clipboard_lines.append(f"\n{champion_name}:")
                            current_champion = champion_name
                        
                        clipboard_lines.append(f"  - {skin_name} - Purchased: {purchase_date}")
            
            # Join all lines and copy to clipboard
            clipboard_content = "\n".join(clipboard_lines)
            
            # Return the content so the GUI can handle clipboard operations
            return clipboard_content
        except Exception as e:
            logger.error(f"Error copying all accounts info: {str(e)}")
            logger.error(f"Error details: {type(e).__name__}: {str(e)}")
            return None

    async def recheck_account(self, username):
        """Recheck a specific account by username and update its data"""
        try:
            logger.info(f"Starting to recheck account: {username}")
            
            # Find the account by username
            account = None
            for acc in self._accounts:
                if acc.username == username:
                    account = acc
                    break
                    
            if not account:
                logger.error(f"Account {username} not found")
                return
            
            # Get account info from DB to ensure we have latest tokens
            account_info = await self.db_manager.get_account(username)
            if account_info:
                # Update tokens on account object first
                if account_info.get('refresh_token'):
                    account.refresh_token = account_info.get('refresh_token')
                if account_info.get('access_token'):
                    account.access_token = account_info.get('access_token')
                if account_info.get('id_token'):
                    account.id_token = account_info.get('id_token')

                # Check JWT token for region information before any other region checks
                actual_region = None
                if account_info.get('id_token'):
                    try:
                        # Extract region from JWT payload
                        jwt_payload = decode_jwt_payload(account_info.get('id_token'))
                        logger.info(f"Checking JWT token for region during recheck for {username}")
                        
                        # Map region codes to regions
                        region_mapping = {
                            'EUW1': 'EUW',
                            'NA1': 'NA',
                            'EUN1': 'EUNE',
                            'LA1': 'LAN',
                            'LA2': 'LAS',
                            'BR1': 'BR',
                            'TR1': 'TR',
                            'RU': 'RU',
                            'JP1': 'JP',
                            'KR': 'KR',
                            'OC1': 'OCE'
                        }
                        
                        # Check lol_region array first (most reliable for EUNE accounts)
                        if 'lol_region' in jwt_payload and jwt_payload['lol_region'] and isinstance(jwt_payload['lol_region'], list):
                            for region_entry in jwt_payload['lol_region']:
                                if isinstance(region_entry, dict) and 'cpid' in region_entry:
                                    actual_region = region_mapping.get(region_entry['cpid'], region_entry['cpid'][:2])
                                    logger.info(f"Using lol_region[].cpid from JWT to determine region: {actual_region}")
                                    break
                        
                        # Then check lol field if lol_region didn't work
                        if not actual_region and 'lol' in jwt_payload:
                            lol_info = jwt_payload.get('lol', {})
                            if isinstance(lol_info, list) and lol_info and isinstance(lol_info[0], dict) and 'cpid' in lol_info[0]:
                                jwt_region = lol_info[0]['cpid']
                                actual_region = region_mapping.get(jwt_region, jwt_region[:2])
                                logger.info(f"Using lol[0].cpid from JWT to determine region: {actual_region}")
                            elif isinstance(lol_info, dict) and 'cpid' in lol_info:
                                jwt_region = lol_info['cpid']
                                actual_region = region_mapping.get(jwt_region, jwt_region[:2])
                                logger.info(f"Using lol.cpid from JWT to determine region: {actual_region}")
                        
                        # Then check original_platform_id if that didn't work
                        if not actual_region and 'original_platform_id' in jwt_payload:
                            jwt_region = jwt_payload['original_platform_id']
                            actual_region = region_mapping.get(jwt_region, jwt_region[:2])
                            logger.info(f"Using original_platform_id from JWT to determine region: {actual_region}")
                            
                    except Exception as e:
                        logger.error(f"Error extracting region from JWT during recheck for {username}: {str(e)}")
                
                # Update region from JWT token if found
                if actual_region:
                    account.region = actual_region
                    account.region_code = actual_region
                    logger.info(f"Updated region to {actual_region} based on JWT token for account {username}")
                # Otherwise, update region code from DB if available
                elif account_info.get('region_code'):
                    account.region_code = account_info.get('region_code')
                    logger.info(f"Updated region_code to {account.region_code} from database for account {username}")
                if account_info.get('region') and not actual_region:
                    account.region = account_info.get('region')
                    logger.info(f"Updated region to {account.region} from database for account {username}")
                
                # Check if account should be EUNE based on creation_region or country
                if not actual_region and account_info.get('creation_region') == 'EUNE':
                    account.region = 'EUNE'
                    account.region_code = 'EUNE'
                    logger.info(f"Overriding region to EUNE based on creation_region during recheck for account {username}")
                elif not actual_region and account_info.get('country') in ['pol', 'cze', 'svk', 'hun', 'rou', 'bgr', 'grc', 'hrv']:
                    account.region = 'EUNE'
                    account.region_code = 'EUNE'
                    logger.info(f"Overriding region to EUNE based on country ({account_info.get('country')}) during recheck for account {username}")
            
            # Initialize authenticator only if it's None
            if not account.authenticator:
                logger.info(f"Initializing authenticator for account: {account.username}")
                account.init_authenticator()
            else:
                logger.info(f"Using existing authenticator for account: {account.username}")
                # Ensure authenticator has the correct region
                account.authenticator.region_code = account.region
                
            # Now also ensure the authenticator has the tokens
            if account.authenticator:
                if hasattr(account, 'access_token') and hasattr(account, 'id_token'):
                    if account.access_token and account.id_token:
                        account.authenticator.set_tokens(account.access_token, account.id_token)
                        logger.info(f"Set existing tokens from account object for account: {account.username}")
                
                # Set refresh_token if available
                if hasattr(account, 'refresh_token') and account.refresh_token:
                    account.authenticator.refresh_token = account.refresh_token
                    logger.info(f"Using refresh token for {account.username}: yes")
                else:
                    logger.info(f"No refresh token available for {account.username}")
                
            # Check the account
            logger.info(f"Rechecking account: {account.username}")
            await self.check_account(account)
            logger.info(f"Successfully rechecked account: {username}")
        except Exception as e:
            logger.error(f"Error rechecking account {username}: {str(e)}")
            # Don't use logger.exception to avoid the 'no attribute' error
            logger.error(f"Error details: {type(e).__name__}: {str(e)}")
            raise

    async def recheck_all_accounts(self):
        """Recheck all accounts in the pool and update their data"""
        try:
            logger.info("Starting to recheck all accounts")
            # Reload accounts from the database to ensure we have the latest list
            await self._load_accounts()
            # Check each account
            for account in self._accounts:
                try:
                    logger.info(f"Rechecking account: {account.username}")
                    
                    # Initialize authenticator if it's None
                    if not account.authenticator:
                        logger.info(f"Initializing authenticator for account: {account.username}")
                        account.init_authenticator()
                        
                    await self.check_account(account)
                    logger.info(f"Successfully rechecked account: {account.username}")
                except Exception as e:
                    logger.error(f"Error rechecking account {account.username}: {str(e)}")
                    logger.error(f"Error details: {type(e).__name__}: {str(e)}")
                    # Continue with other accounts even if one fails
                    continue
            
            logger.info("Successfully completed rechecking all accounts")
        except Exception as e:
            logger.error(f"Error rechecking accounts: {str(e)}")
            logger.error(f"Error details: {type(e).__name__}: {str(e)}")
            raise

    def _is_chroma(self, skin_name: str, skin_id: int) -> bool:
        """Check if a skin is a chroma based on its name or ID."""
        # Check if the name contains "Chroma"
        if not skin_name or not skin_id:
            return False
            
        # Common chroma indicators in skin names
        skin_name_lower = skin_name.lower()
        if "(chroma)" in skin_name_lower:
            return True
            
        # Look for color names in parentheses - common for chromas
        color_names = ["pearl", "ruby", "sapphire", "emerald", "obsidian", "rose quartz", "tanzanite", "turquoise"]
        for color in color_names:
            if f"({color}" in skin_name_lower:
                return True
            
        # Check for ranked chromas (Bronze, Silver, Gold, Platinum, Diamond)
        rank_names = ["bronze", "silver", "gold", "platinum", "diamond"]
        for rank in rank_names:
            if f"({rank}" in skin_name_lower:
                return True
                
        return False

    def _extract_short_chroma_description(self, description: str) -> str:
        """Extract a short description from a chroma description"""
        try:
            # For ranked rewards, extract the rank
            if "reached" in description and "rank" in description:
                # Try to extract the rank
                rank_keywords = ["Bronze", "Silver", "Gold", "Platinum", "Diamond", "Master", "Grandmaster", "Challenger"]
                for rank in rank_keywords:
                    if rank in description:
                        return rank
            
            # For bundle exclusives
            if "bundle exclusive" in description:
                return "Bundle"
            
            # For event exclusives
            if "Loot exclusive" in description and "event" in description:
                # Try to extract the event name
                event_start = description.find("in the ") + 7
                if event_start > 7:  # Found "in the "
                    event_end = description.find(" event", event_start)
                    if event_end > 0:
                        return description[event_start:event_end]
                return "Event"
            
            # For chromas with specific colors
            color_keywords = ["Ruby", "Sapphire", "Emerald", "Pearl", "Obsidian", "Rose Quartz", "Tanzanite", "Turquoise"]
            for color in color_keywords:
                if color in description:
                    return color
            
            # For ranked chromas
            rank_keywords = ["Bronze", "Silver", "Gold", "Platinum", "Diamond"]
            for rank in rank_keywords:
                if rank in description:
                    return rank
            
            # Default to a short version only if it's not an award message
            if len(description) > 20 and "award" not in description.lower():
                return description[:20] + "..."
            return description
        except Exception:
            return "Chroma"
    
    def _extract_champion_from_skin(self, skin_info: Dict) -> str:
        """Extract champion name from skin info"""
        try:
            # The champion name is often in the path
            if "splashPath" in skin_info and "Characters" in skin_info["splashPath"]:
                path_parts = skin_info["splashPath"].split("/")
                for i, part in enumerate(path_parts):
                    if part == "Characters" and i + 1 < len(path_parts):
                        return path_parts[i + 1]
            
            # For base skins, the name is usually just the champion name
            if skin_info.get("isBase", False) and "name" in skin_info:
                return skin_info["name"]
            
            # If all else fails, return Unknown
            return "Unknown"
        except Exception:
            return "Unknown"

    async def load_accounts(self):
        """Load accounts from database."""
        try:
            logger.info("Loading accounts from database...")
            self._accounts = []
            accounts_data = await self.db_manager.get_all_accounts()
            
            # Define a helper function to extract region from JWT token
            def extract_region_from_jwt(token):
                if not token:
                    return None
                
                try:
                    # Decode the JWT payload
                    jwt_payload = decode_jwt_payload(token)
                    
                    # Map region codes to regions
                    region_mapping = {
                        'EUW1': 'EUW',
                        'NA1': 'NA',
                        'EUN1': 'EUNE',
                        'LA1': 'LAN',
                        'LA2': 'LAS',
                        'BR1': 'BR',
                        'TR1': 'TR',
                        'RU': 'RU',
                        'JP1': 'JP',
                        'KR': 'KR',
                        'OC1': 'OCE'
                    }
                    
                    # Check lol_region array first (most reliable for EUNE accounts)
                    if 'lol_region' in jwt_payload and jwt_payload['lol_region'] and isinstance(jwt_payload['lol_region'], list):
                        for region_entry in jwt_payload['lol_region']:
                            if isinstance(region_entry, dict) and 'cpid' in region_entry:
                                region_code = region_mapping.get(region_entry['cpid'], region_entry['cpid'][:2])
                                logger.info(f"Extracted region from JWT lol_region[].cpid: {region_code}")
                                return region_code
                    
                    # Check lol field if lol_region didn't work
                    if 'lol' in jwt_payload:
                        lol_info = jwt_payload.get('lol', {})
                        if isinstance(lol_info, list) and lol_info and isinstance(lol_info[0], dict) and 'cpid' in lol_info[0]:
                            jwt_region = lol_info[0]['cpid']
                            region_code = region_mapping.get(jwt_region, jwt_region[:2])
                            logger.info(f"Extracted region from JWT lol[0].cpid: {region_code}")
                            return region_code
                        elif isinstance(lol_info, dict) and 'cpid' in lol_info:
                            jwt_region = lol_info['cpid']
                            region_code = region_mapping.get(jwt_region, jwt_region[:2])
                            logger.info(f"Extracted region from JWT lol.cpid: {region_code}")
                            return region_code
                    
                    # Check original_platform_id
                    if 'original_platform_id' in jwt_payload and jwt_payload['original_platform_id']:
                        jwt_region = jwt_payload['original_platform_id']
                        region_code = region_mapping.get(jwt_region, jwt_region[:2])
                        logger.info(f"Extracted region from JWT original_platform_id: {region_code}")
                        return region_code
                    
                    # Check region.id
                    if 'region' in jwt_payload and isinstance(jwt_payload['region'], dict) and 'id' in jwt_payload['region']:
                        region_id = jwt_payload['region']['id']
                        region_code = region_mapping.get(region_id, region_id[:2])
                        logger.info(f"Extracted region from JWT region.id: {region_code}")
                        return region_code
                    
                    # Check region.tag
                    if 'region' in jwt_payload and isinstance(jwt_payload['region'], dict) and 'tag' in jwt_payload['region']:
                        tag = jwt_payload['region']['tag'].lower()
                        if tag == 'eune':
                            return 'EUNE'
                        elif tag == 'euw':
                            return 'EUW'
                        elif tag == 'na':
                            return 'NA'
                        # Add more mappings as needed
                        logger.info(f"Extracted region from JWT region.tag: {tag.upper()}")
                        return tag.upper()
                    
                    # Check dat.r (legacy option)
                    if 'dat' in jwt_payload and isinstance(jwt_payload['dat'], dict) and 'r' in jwt_payload['dat']:
                        jwt_region = jwt_payload['dat']['r']
                        region_code = region_mapping.get(jwt_region, jwt_region[:2])
                        logger.info(f"Extracted region from JWT dat.r: {region_code}")
                        return region_code
                    
                except Exception as e:
                    logger.error(f"Error extracting region from JWT: {str(e)}")
                
                return None
            
            for account_data in accounts_data:
                try:
                    username = account_data.get('username', '')
                    password = account_data.get('password', '')
                    
                    logger.info(f"Loading account {username} from database")
                    
                    # Initialize basic properties with defaults
                    region = None
                    region_code = None
                    
                    # STEP 1: Check JWT token for region information as the FIRST priority
                    id_token = account_data.get('id_token')
                    if id_token:
                        logger.info(f"Found ID token for {username}, checking for region information")
                        jwt_region = extract_region_from_jwt(id_token)
                        if jwt_region:
                            region = jwt_region
                            region_code = jwt_region
                            logger.info(f"Found region {region} in JWT token for {username}")
                    
                    # STEP 2: If JWT token didn't provide region, check creation_region and country
                    if not region and account_data.get('creation_region') == 'EUNE':
                        region = 'EUNE'
                        region_code = 'EUNE'
                        logger.info(f"Using EUNE region based on creation_region for {username}")
                    elif not region and account_data.get('country') in ['pol', 'cze', 'svk', 'hun', 'rou', 'bgr', 'grc', 'hrv']:
                        region = 'EUNE'
                        region_code = 'EUNE'
                        logger.info(f"Using EUNE region based on country ({account_data.get('country')}) for {username}")
                    
                    # STEP 3: If still no region, check region fields in database
                    if not region and account_data.get('region'):
                        region = account_data.get('region')
                        logger.info(f"Using region from database: {region} for {username}")
                    if not region_code and account_data.get('region_code'):
                        region_code = account_data.get('region_code')
                        if not region:
                            region = region_code
                        logger.info(f"Using region_code from database: {region_code} for {username}")
                    
                    # STEP 4: If we still don't have a region_code, check userinfo
                    if not region_code:
                        userinfo = account_data.get('userinfo', {})
                        if isinstance(userinfo, dict):
                            # Check 'lol' and 'lol_region' fields
                            lol_info = userinfo.get('lol', {})
                            if lol_info and 'cpid' in lol_info:
                                # Extract region from cpid (e.g., 'NA1' -> 'NA')
                                region_from_lol = lol_info.get('cpid', '')[:2]
                                if region_from_lol:
                                    region_code = region_from_lol
                                    region = region_code
                                    logger.info(f"Extracted region from userinfo lol.cpid: {region_code}")
                            
                            # Check region object
                            region_obj = userinfo.get('region', {})
                            if not region_code and region_obj and 'id' in region_obj:
                                # Extract region from id (e.g., 'NA1' -> 'NA')
                                region_from_obj = region_obj.get('id', '')[:2]
                                if region_from_obj:
                                    region_code = region_from_obj
                                    region = region_code
                                    logger.info(f"Extracted region from userinfo region.id: {region_code}")
                    
                    # STEP 5: If we still don't have a region_code, leave it as None
                    if not region_code:
                        # Check original_platform_id
                        original_platform = account_data.get('original_platform_id', '')
                        if original_platform:
                            region_code = original_platform[:2]
                            region = region_code
                            logger.info(f"Using region from original_platform_id: {region_code}")
                        else:
                            # Don't default to any region, leave both as None
                            region_code = None
                            region = None
                            logger.info(f"No region information found for {username}, leaving region as None")
                    
                    # If either region or region_code is still not set, set them both to the same value
                    if region and not region_code:
                        region_code = region
                    elif region_code and not region:
                        region = region_code
                    
                    logger.info(f"Using region={region}, region_code={region_code} for account {username}")
                    
                    # Create account object with region information determined above
                    account = Account(
                        username=username,
                        password=password,
                        region=region,
                        region_code=region_code,
                        db_manager=self.db_manager
                    )
                    
                    # Log the region information
                    logger.info(f"Created account object for {username} with region={region} (type: {type(region)}), region_code={region_code} (type: {type(region_code)})")
                    
                    # Set additional properties from database
                    for key, value in account_data.items():
                        if key not in ['username', 'password', 'region', 'region_code'] and hasattr(account, key):
                            setattr(account, key, value)
                    
                    # Log account details
                    logger.info(f"Account details: BE={account_data.get('blue_essence', 0)}, RP={account_data.get('riot_points', 0)}, Level={account_data.get('summoner_level', 0)}")
                    logger.info(f"Account info: created_at={account_data.get('created_at')}, creation_region={account_data.get('creation_region')}, country={account_data.get('country')}")
                    logger.info(f"Game info: game_name={account_data.get('game_name')}, tag_line={account_data.get('tag_line')}")
                    
                    # Load season history
                    season_history = await self.db_manager.get_account_season_history(username)
                    account.season_history = season_history
                    logger.info(f"Loaded {len(season_history)} season history entries for {username}")
                    
                    # Add to accounts list
                    self._accounts.append(account)
                    
                except Exception as e:
                    logger.error(f"Error loading account {account_data.get('username', 'unknown')}: {str(e)}")
            
            logger.info(f"Loaded {len(self._accounts)} accounts from database")
        except Exception as e:
            logger.error(f"Error loading accounts from database: {str(e)}")
            raise

    async def _extract_region_from_tokens(self, username, id_token, access_token, entitlements_token, userinfo):
        """Extract region information from tokens."""
        try:
            if userinfo:
                try:
                    if isinstance(userinfo, str) and userinfo.startswith('eyJ'):
                        # This is a JWT token, decode the payload part
                        parts = userinfo.split('.')
                        if len(parts) == 3:
                            payload = parts[1]
                            # Add padding if needed
                            payload += '=' * ((4 - len(payload) % 4) % 4)
                            # Decode base64
                            decoded = base64.b64decode(payload)
                            data = json.loads(decoded)
                            
                            # Look for region information in various places
                            if 'region' in data and isinstance(data['region'], dict) and 'tag' in data['region']:
                                region_tag = data['region']['tag']
                                if region_tag.upper() == 'EUNE':
                                    return 'EUNE'
                            
                            if 'lol_region' in data and isinstance(data['lol_region'], list) and data['lol_region']:
                                for region_data in data['lol_region']:
                                    if 'cpid' in region_data:
                                        if region_data['cpid'] == 'EUN1':
                                            return 'EUNE'
                
                except Exception as e:
                    logger.error(f"Error decoding JWT userinfo for {username}: {e}")
            
            # Check access token
            if access_token and isinstance(access_token, str) and access_token.startswith('eyJ'):
                try:
                    parts = access_token.split('.')
                    if len(parts) == 3:
                        payload = parts[1]
                        payload += '=' * ((4 - len(payload) % 4) % 4)
                        decoded = base64.b64decode(payload)
                        data = json.loads(decoded)
                        
                        if 'dat' in data and 'r' in data['dat'] and data['dat']['r'] == 'EUN1':
                            return 'EUNE'
                except Exception as e:
                    logger.error(f"Error decoding JWT access token for {username}: {e}")
            
            return None
        except Exception as e:
            logger.error(f"Error extracting region from tokens: {e}")
            return None

    async def import_accounts_from_file(self, file_path):
        """Import accounts from a text file with username:password format"""
        try:
            logger.info(f"Importing accounts from file: {file_path}")
            accounts = []
            
            with open(file_path, 'r') as file:
                for line in file:
                    line = line.strip()
                    if ':' in line:
                        username, password = line.split(':', 1)
                        accounts.append({
                            'username': username.strip(),
                            'password': password.strip()
                        })
            
            # Save accounts to database without region (it will be detected during auth)
            for acc in accounts:
                await self.db_manager.update_account(
                    username=acc['username'],
                    password=acc['password'],
                    refresh_token='',
                    region=''  # Leave region empty, it will be detected during auth
                )
            
            # Reload accounts
            await self._load_accounts()
            
            logger.info(f"Successfully imported {len(accounts)} accounts from {file_path}")
            return len(accounts)
        except Exception as e:
            logger.error(f"Error importing accounts from file: {str(e)}")
            raise

    async def import_accounts_from_clipboard(self, clipboard_text):
        """Import accounts from clipboard with username:password format"""
        try:
            logger.info("Importing accounts from clipboard")
            accounts = []
            
            # Split clipboard text by lines
            lines = clipboard_text.strip().split('\n')
            
            for line in lines:
                line = line.strip()
                if ':' in line and line:  # Check if line is not empty and contains ':'
                    username, password = line.split(':', 1)
                    accounts.append({
                        'username': username.strip(),
                        'password': password.strip()
                    })
            
            if not accounts:
                logger.warning("No valid accounts found in clipboard (format should be username:password)")
                return 0
            
            # Save accounts to database without region (it will be detected during auth)
            for acc in accounts:
                await self.db_manager.update_account(
                    username=acc['username'],
                    password=acc['password'],
                    refresh_token='',
                    region=''  # Leave region empty, it will be detected during auth
                )
            
            # Reload accounts
            await self._load_accounts()
            
            logger.info(f"Successfully imported {len(accounts)} accounts from clipboard")
            return len(accounts)
        except Exception as e:
            logger.error(f"Error importing accounts from clipboard: {str(e)}")
            raise

    async def load_accounts_from_database(self):
        """Load accounts from database"""
        try:
            accounts = await self.db_manager.get_all_accounts()
            logger.info(f"Loading {len(accounts)} accounts from database")
            
            for account_data in accounts:
                # Skip accounts with missing username or region
                if not account_data.get('username') or not account_data.get('region'):
                    continue
                
                try:
                    # Get region and region code
                    region = account_data.get('region', '')
                    region_code = account_data.get('region_code', '')
                    
                    # Ensure region_code is set
                    if not region_code and region:
                        region_code = region.upper()[:3]
                    
                    # Check if we've already loaded this account
                    if account_data.get('username') in self.accounts:
                        continue

                    # Prepare rank info
                    rank_info = account_data.get('rank_info', {})
                    if not rank_info:
                        # Build from individual fields if needed
                        rank_info = {
                            'solo': {
                                'tier': account_data.get('solo_tier', 'UNRANKED'),
                                'division': account_data.get('solo_division', ''),
                                'lp': account_data.get('solo_lp', 0),
                                'wins': account_data.get('solo_wins', 0),
                                'losses': account_data.get('solo_losses', 0),
                                'previous_tier': account_data.get('solo_previous_tier', 'UNRANKED'),
                                'previous_division': account_data.get('solo_previous_division', '')
                            },
                            'flex': {
                                'tier': account_data.get('flex_tier', 'UNRANKED'),
                                'division': account_data.get('flex_division', ''),
                                'lp': account_data.get('flex_lp', 0),
                                'wins': account_data.get('flex_wins', 0),
                                'losses': account_data.get('flex_losses', 0),
                                'previous_tier': account_data.get('flex_previous_tier', 'UNRANKED'),
                                'previous_division': account_data.get('flex_previous_division', '')
                            },
                            'season_history': []
                        }
                    
                    # Create account object
                    account = Account(
                        username=account_data.get('username'),
                        password=account_data.get('password', ''),
                        region=region,
                        region_code=region_code,
                        game_name=account_data.get('game_name', ''),
                        tag_line=account_data.get('tag_line', ''),
                        summoner_level=account_data.get('summoner_level', 0),
                        blue_essence=account_data.get('blue_essence', 0),
                        riot_points=account_data.get('riot_points', 0),
                        champions_owned=account_data.get('champion_count', 0),
                        last_checked=account_data.get('last_checked', ''),
                        created_at=account_data.get('created_at', ''),
                        penalty_minutes=account_data.get('penalty_minutes', 0),
                        is_banned=bool(account_data.get('is_banned', False)),
                        ban_info=account_data.get('ban_info', ''),
                        chat_restrictions=account_data.get('chat_restrictions', ''),
                        ranked_games_remaining=account_data.get('ranked_games_remaining', 0),
                        rank_info=rank_info,
                        country=account_data.get('country', ''),
                        creation_region=account_data.get('creation_region', ''),
                        db_manager=self.db_manager
                    )
                    
                    # Set tokens if available
                    if account_data.get('refresh_token'):
                        account.refresh_token = account_data.get('refresh_token')
                    if account_data.get('access_token'):
                        account.access_token = account_data.get('access_token')
                    if account_data.get('id_token'):
                        account.id_token = account_data.get('id_token')
                    
                    self._accounts.append(account)
                    self.accounts[account.username] = account
                    
                    # Log progress for larger account lists
                    if len(self._accounts) % 100 == 0:
                        logger.info(f"Loaded {len(self._accounts)} accounts so far")
                        
                except Exception as e:
                    logger.error(f"Error loading account {account_data.get('username')}: {str(e)}")
                    
            logger.info(f"Loaded {len(self._accounts)} accounts from database")
            return True
            
        except Exception as e:
            logger.error(f"Error loading accounts from database: {str(e)}")
            logger.exception(e)
            return False

    async def clear_accounts(self):
        """Clear all accounts from database and memory"""
        try:
            logger.info("Clearing all accounts from database and memory")
            
            # Clear database
            await self.db_manager.clear_all_accounts()
            
            # Clear accounts from memory
            self.accounts.clear()
            self._accounts.clear()
            self.banned_accounts.clear()
            
            logger.info("Successfully cleared all accounts")
            
        except Exception as e:
            logger.error(f"Error clearing accounts: {str(e)}")
            logger.exception(e)
            raise

    async def delete_account(self, username: str):
        """Delete a specific account from database and memory"""
        try:
            logger.info(f"Deleting account {username} from database and memory")
            
            # Delete from database
            await self.db_manager.delete_account(username)
            
            # Remove from memory
            if username in self.accounts:
                del self.accounts[username]
                logger.info(f"Removed {username} from accounts dict")
            
            # Remove from _accounts list
            self._accounts = [acc for acc in self._accounts if acc.username != username]
            
            # Remove from banned_accounts list if present
            self.banned_accounts = [acc for acc in self.banned_accounts if acc.username != username]
            
            logger.info(f"Successfully deleted account {username}")
            
        except Exception as e:
            logger.error(f"Error deleting account {username}: {str(e)}")
            logger.exception(e)
            raise

    async def delete_multiple_accounts(self, usernames: list):
        """Delete multiple specific accounts from database and memory"""
        try:
            logger.info(f"Deleting {len(usernames)} accounts from database and memory")
            
            deleted_count = 0
            failed_accounts = []
            
            for username in usernames:
                try:
                    await self.delete_account(username)
                    deleted_count += 1
                except Exception as e:
                    logger.error(f"Failed to delete account {username}: {str(e)}")
                    failed_accounts.append(username)
            
            logger.info(f"Successfully deleted {deleted_count} accounts")
            if failed_accounts:
                logger.warning(f"Failed to delete {len(failed_accounts)} accounts: {failed_accounts}")
            
            return deleted_count, failed_accounts
            
        except Exception as e:
            logger.error(f"Error in delete_multiple_accounts: {str(e)}")
            logger.exception(e)
            raise
