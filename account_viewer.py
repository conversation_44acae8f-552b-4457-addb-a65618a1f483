import os
import asyncio
import logging
import json
import requests
from datetime import datetime
from PyQt6.QtWidgets import QMainWindow, QTextEdit, QVBoxLayout, QWidget, QLabel, QScrollArea, QHBoxLayout, QPushButton, QMenu, QFileDialog, QToolBar
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QCursor, QAction
from database.manager import DatabaseManager

logger = logging.getLogger(__name__)

class AccountViewer(QMainWindow):
    """Viewer for account details."""
    
    def __init__(self, parent=None):
        """Initialize the account viewer."""
        super().__init__(parent)
        self.setWindowTitle("League Account Manager")
        self.setMinimumSize(1200, 800)
        
        # Initialize champion and skin data
        self.champion_data = {}
        self.skin_data = {}
        self.champion_id_mapping = self._get_champion_id_mapping()
        self._load_champion_data()
        
        # Create central widget and layout
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.layout = QVBoxLayout(self.central_widget)
        
        # Create a prominent button bar at the top
        self.button_bar = QWidget()
        self.button_bar.setStyleSheet("""
            background-color: #0A1428;
            border-bottom: 2px solid #C89B3C;
            padding: 10px;
            margin-bottom: 15px;
        """)
        self.button_layout = QHBoxLayout(self.button_bar)
        self.button_layout.setContentsMargins(20, 10, 20, 10)
        self.layout.addWidget(self.button_bar)
        
        # Add League Account Manager label
        self.app_title = QLabel("League Account Manager")
        self.app_title.setStyleSheet("""
            color: #C89B3C;
            font-size: 20pt;
            font-weight: bold;
        """)
        self.button_layout.addWidget(self.app_title)
        
        # Add spacer to push buttons to the right
        self.button_layout.addStretch()
        
        # Create File button with dropdown menu
        self.file_button = QPushButton("File")
        self.file_button.setStyleSheet("""
            QPushButton {
                background-color: #1E2328;
                color: #C89B3C;
                border: 1px solid #785A28;
                padding: 12px 25px;
                border-radius: 4px;
                margin-right: 15px;
                font-size: 14pt;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2A2F35;
                border: 1px solid #C89B3C;
            }
        """)
        self.button_layout.addWidget(self.file_button)
        
        # Create Export button with dropdown menu
        self.export_button = QPushButton("Export")
        self.export_button.setStyleSheet("""
            QPushButton {
                background-color: #1E2328;
                color: #C89B3C;
                border: 1px solid #785A28;
                padding: 12px 25px;
                border-radius: 4px;
                font-size: 14pt;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2A2F35;
                border: 1px solid #C89B3C;
            }
        """)
        self.button_layout.addWidget(self.export_button)
        
        # Create content layout
        self.content_layout = QVBoxLayout()
        self.layout.addLayout(self.content_layout)
        
        # Create header layout with title and buttons
        self.header_layout = QHBoxLayout()
        self.content_layout.addLayout(self.header_layout)
        
        # Create title label
        self.title_label = QLabel("Account Details")
        self.title_label.setStyleSheet("font-size: 18pt; font-weight: bold; color: #C89B3C;")
        self.header_layout.addWidget(self.title_label)
        
        # Add spacer to push buttons to the right
        self.header_layout.addStretch()
        
        # Create scroll area for content
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.content_layout.addWidget(self.scroll_area)
        
        # Create content widget
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.scroll_area.setWidget(self.content_widget)
        
        # Create text display
        self.text_display = QTextEdit()
        self.text_display.setReadOnly(True)
        self.text_display.setStyleSheet("""
            QTextEdit {
                background-color: #0A1428;
                color: #F0E6D2;
                border: none;
                font-family: Arial, sans-serif;
            }
        """)
        self.content_layout.addWidget(self.text_display)
        
        # Set up JavaScript bridge
        self.setup_js_bridge()
        
        # Store current account username
        self.current_account = None
        
        # Create File menu
        self.file_button_menu = QMenu(self)
        self.file_button_menu.setStyleSheet("""
            QMenu {
                background-color: #1E2328;
                color: #F0E6D2;
                border: 1px solid #785A28;
                font-size: 12pt;
                padding: 5px;
            }
            QMenu::item {
                padding: 8px 20px;
                border-bottom: 1px solid #2A2F35;
            }
            QMenu::item:selected {
                background-color: #2A2F35;
                border-left: 3px solid #C89B3C;
            }
            QMenu::item:hover {
                background-color: #2A2F35;
                color: #C89B3C;
            }
        """)
        
        # Add actions to File button menu
        self.select_txt_action = QAction("Select txt file user:pass", self)
        self.select_txt_action.triggered.connect(self.select_txt_file)
        self.file_button_menu.addAction(self.select_txt_action)
        
        self.save_db_action = QAction("Save Database", self)
        self.save_db_action.triggered.connect(self.save_database)
        self.file_button_menu.addAction(self.save_db_action)
        
        self.load_db_action = QAction("Load Database", self)
        self.load_db_action.triggered.connect(self.load_database)
        self.file_button_menu.addAction(self.load_db_action)
        
        self.settings_action = QAction("Settings", self)
        self.settings_action.triggered.connect(self.show_settings)
        self.file_button_menu.addAction(self.settings_action)
        
        self.clear_accounts_action = QAction("Clear All Accounts", self)
        self.clear_accounts_action.triggered.connect(self.clear_all_accounts)
        self.file_button_menu.addAction(self.clear_accounts_action)
        
        self.refresh_button_action = QAction("Refresh", self)
        self.refresh_button_action.triggered.connect(self.refresh_account)
        self.file_button_menu.addAction(self.refresh_button_action)
        
        self.close_button_action = QAction("Close", self)
        self.close_button_action.triggered.connect(self.close)
        self.file_button_menu.addAction(self.close_button_action)
        
        # Connect button to show menu on click
        self.file_button.clicked.connect(self.show_file_menu)
        
        # Create Export menu
        self.export_menu = QMenu(self)
        self.export_menu.setStyleSheet("""
            QMenu {
                background-color: #1E2328;
                color: #F0E6D2;
                border: 1px solid #785A28;
                font-size: 12pt;
                padding: 5px;
            }
            QMenu::item {
                padding: 8px 20px;
                border-bottom: 1px solid #2A2F35;
            }
            QMenu::item:selected {
                background-color: #2A2F35;
                border-left: 3px solid #C89B3C;
            }
            QMenu::item:hover {
                background-color: #2A2F35;
                color: #C89B3C;
            }
        """)
        
        # Add actions to Export menu
        self.copy_all_info_action = QAction("Copy All Accs Info", self)
        self.copy_all_info_action.triggered.connect(self.copy_all_accounts_info)
        self.export_menu.addAction(self.copy_all_info_action)
        
        self.copy_no_chromas_action = QAction("Copy All Accs Info (without Chromas)", self)
        self.copy_no_chromas_action.triggered.connect(self.copy_accounts_no_chromas)
        self.export_menu.addAction(self.copy_no_chromas_action)
        
        self.copy_no_skins_chromas_action = QAction("Copy All Accs Info (without Skins and Chromas)", self)
        self.copy_no_skins_chromas_action.triggered.connect(self.copy_accounts_no_skins_chromas)
        self.export_menu.addAction(self.copy_no_skins_chromas_action)
        
        self.export_html_action = QAction("Export as HTML", self)
        self.export_html_action.triggered.connect(self.export_as_html)
        self.export_menu.addAction(self.export_html_action)
        
        self.export_json_action = QAction("Export as JSON", self)
        self.export_json_action.triggered.connect(self.export_as_json)
        self.export_menu.addAction(self.export_json_action)
        
        self.export_text_action = QAction("Export as Text", self)
        self.export_text_action.triggered.connect(self.export_as_text)
        self.export_menu.addAction(self.export_text_action)
        
        # Connect button to show menu on click
        self.export_button.clicked.connect(self.show_export_menu)
    
    def setup_js_bridge(self):
        """Set up JavaScript bridge to handle events from HTML."""
        # Create a QWebChannel to communicate with JavaScript
        from PyQt6.QtWebChannel import QWebChannel
        from PyQt6.QtCore import QObject, pyqtSlot
        
        # Create a bridge object
        class Bridge(QObject):
            def __init__(self, parent=None):
                super().__init__(parent)
                self.viewer = parent
            
            @pyqtSlot(str)
            def call_function(self, function_name):
                """Call a function in the AccountViewer class."""
                if function_name == "close":
                    self.viewer.close()
                elif hasattr(self.viewer, function_name):
                    method = getattr(self.viewer, function_name)
                    if callable(method):
                        method()
        
        # Create the bridge and channel
        self.bridge = Bridge(self)
        self.channel = QWebChannel()
        self.channel.registerObject("bridge", self.bridge)
        
        # Connect the channel to the text display
        page = self.text_display.document().documentLayout().anchorAt
        # Note: This is a simplified approach. In a real implementation,
        # you would need to use QWebEngineView instead of QTextEdit for full JavaScript support.
    
    def _get_champion_id_mapping(self):
        """Get champion ID mapping from Community Dragon API."""
        try:
            # Get champion data from champion-summary.json
            champions_url = "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/champion-summary.json"
            logger.info(f"Fetching champion data from {champions_url}")
            champions_response = requests.get(champions_url, timeout=10)
            champions_response.raise_for_status()
            champions_data = champions_response.json()
            
            # Create mapping from champion data
            champion_mapping = {}
            for champion in champions_data:
                champion_id = str(champion.get('id', '0'))
                if champion_id != '-1':  # Skip the "None" champion
                    champion_mapping[champion_id] = champion.get('name', f'Unknown ({champion_id})')
            
            logger.info(f"Loaded {len(champion_mapping)} champion entries from Community Dragon API")
            return champion_mapping
            
        except Exception as e:
            logger.error(f"Error loading champion data: {str(e)}")
            return {}  # Return empty mapping on error
        
    def _load_champion_data(self):
        """Load champion and skin data from Community Dragon API."""
        try:
            # Get champion data from champion-summary.json
            champions_url = "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/champion-summary.json"
            logger.info(f"Fetching champion data from {champions_url}")
            champions_response = requests.get(champions_url, timeout=10)
            champions_response.raise_for_status()
            champions_data = champions_response.json()
            
            logger.info(f"Retrieved {len(champions_data)} champion entries from Community Dragon API")
            
            # Process champion data
            for champion in champions_data:
                champion_id = str(champion.get('id', '0'))
                if champion_id != '-1':  # Skip the "None" champion
                    name = champion.get('name', f'Unknown ({champion_id})')
                    alias = champion.get('alias', '')
                    icon_path = champion.get('squarePortraitPath', '')
                    
                    # Store in champion_data
                    self.champion_data[champion_id] = {
                        'id': champion_id,
                        'name': name,
                        'alias': alias,
                        'icon_path': icon_path
                    }
            
            # Get skin data from skins.json
            skins_url = "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/skins.json"
            logger.info(f"Fetching skin data from {skins_url}")
            skins_response = requests.get(skins_url, timeout=10)
            skins_response.raise_for_status()
            skins_data = skins_response.json()
            
            logger.info(f"Retrieved {len(skins_data)} skin entries from Community Dragon API")
            
            # Process skin data
            for skin_id, skin_info in skins_data.items():
                # Extract champion ID from skin ID (first digits before last 3 digits)
                try:
                    skin_id_int = int(skin_id)
                    champion_id = str(skin_id_int // 1000)
                except ValueError:
                    logger.warning(f"Invalid skin ID format: {skin_id}")
                    champion_id = '0'
                    skin_id_int = 0
                
                # Get champion name from champion_data
                champion_name = self.champion_data.get(champion_id, {}).get('name', 'Unknown')
                
                # Add skin data
                self.skin_data[skin_id] = {
                    'id': skin_id,
                    'name': skin_info.get('name', 'Unknown Skin'),
                    'champion_id': champion_id,
                    'champion_name': champion_name,
                    'splash_path': skin_info.get('uncenteredSplashPath', ''),
                    'is_base': skin_info.get('isBase', False),
                    'skin_num': skin_id_int % 1000  # Extract skin number (last 3 digits)
                }
            
            logger.info(f"Loaded data for {len(self.champion_data)} champions and {len(self.skin_data)} skins from Community Dragon API")
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Network error loading champion and skin data: {str(e)}")
            # Don't clear data on network error, keep existing data
        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing error loading champion and skin data: {str(e)}")
            # Don't clear data on JSON error, keep existing data
        except Exception as e:
            logger.error(f"Unexpected error loading champion and skin data: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            # Don't clear data on unexpected error, keep existing data
        
    def display_account(self, username):
        """Display account information for the given username."""
        # Store current account username
        self.current_account = username
        
        # Create a new event loop for this operation
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Get account data and generate text
            logger.info(f"Getting account data for {username}")
            account_data = loop.run_until_complete(self._get_account_data(username))
            logger.info(f"Generating HTML display for {username}")
            
            # Update title
            self.title_label.setText(f"Account: {username}")
            
            # Generate and set HTML content
            html_content = self._generate_text_content(account_data)
            self.text_display.setHtml(html_content)
            logger.info(f"Displayed account details for {username}")
        except Exception as e:
            logger.error(f"Error displaying account {username}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
        finally:
            # Close the loop properly
            loop.close()
    
    async def _get_account_data(self, username):
        """Get account data from the database."""
        db_manager = DatabaseManager()
        account_info = await db_manager.get_account_info(username)
        
        logger.debug(f"Raw account_info from database for {username}: {account_info.keys() if account_info else 'None'}")
        
        if not account_info:
            return {
                'username': username,
                'error': 'Account not found'
            }
        
        # Log specific account restrictions data
        is_banned = account_info.get('is_banned', False)
        ban_info = account_info.get('ban_info', '')
        chat_restrictions = account_info.get('chat_restrictions', '')
        logger.debug(f"Account restrictions data: is_banned={is_banned}, ban_info='{ban_info}', chat_restrictions='{chat_restrictions}'")
        
        # Get champions
        champions = await db_manager.get_owned_champions(username)
        
        # Log champion data for debugging
        if champions:
            logger.info(f"Champion data sample: {champions[0]}")
            # Fix champion IDs if needed
            for champion in champions:
                # Convert champion_id to string for consistency
                if 'champion_id' in champion:
                    champion['id'] = champion['champion_id']
                
                if 'id' not in champion and 'champion_id' in champion:
                    champion['id'] = champion['champion_id']
                
                # Ensure we have an ID field
                if 'id' not in champion:
                    champion['id'] = 0
                
                # Convert to string for lookup
                champion_id = str(champion['id'])
                
                # If ID is 0 or missing, try to find by name
                if champion_id == '0' and 'champion_name' in champion and champion['champion_name']:
                    champion_name = champion['champion_name']
                    # Find champion ID by name in our mapping
                    for cid, cname in self.champion_id_mapping.items():
                        if cname.lower() == champion_name.lower():
                            champion['id'] = int(cid)
                            break
                
                # Ensure we have a name field
                if 'name' not in champion and 'champion_name' in champion:
                    champion['name'] = champion['champion_name']
                
                # If name is missing, try to get it from our mapping
                if ('name' not in champion or not champion['name']) and 'id' in champion:
                    champion_id = str(champion['id'])
                    if champion_id in self.champion_id_mapping:
                        champion['name'] = self.champion_id_mapping[champion_id]
                    elif champion_id in self.champion_data:
                        champion['name'] = self.champion_data[champion_id]['name']
        
        account_info['champions'] = champions
        
        # Get skins
        skins = await db_manager.get_owned_skins(username)
        
        # Log skin data for debugging
        if skins:
            logger.info(f"Skin data sample: {skins[0]}")
            # Fix skin IDs if needed
            for skin in skins:
                # Convert skin_id to string for consistency
                if 'skin_id' in skin:
                    skin['id'] = skin['skin_id']
                
                # Ensure we have an ID field
                if 'id' not in skin:
                    skin['id'] = 0
                
                # Extract champion ID from skin ID if possible
                if 'id' in skin and skin['id'] != 0:
                    try:
                        skin_id_int = int(skin['id'])
                        extracted_champion_id = skin_id_int // 1000
                        skin['champion_id'] = extracted_champion_id
                    except (ValueError, TypeError):
                        pass
                
                # If champion_id is 0 or missing, try to find by name
                if ('champion_id' not in skin or skin['champion_id'] == 0) and 'champion_name' in skin and skin['champion_name']:
                    champion_name = skin['champion_name']
                    # Find champion ID by name in our mapping
                    for cid, cname in self.champion_id_mapping.items():
                        if cname.lower() == champion_name.lower():
                            skin['champion_id'] = int(cid)
                            break
                
                # Ensure we have a name field
                if 'name' not in skin and 'skin_name' in skin:
                    skin['name'] = skin['skin_name']
                
                # If champion_name is missing, try to get it from our mapping
                if ('champion_name' not in skin or not skin['champion_name']) and 'champion_id' in skin:
                    champion_id = str(skin['champion_id'])
                    if champion_id in self.champion_id_mapping:
                        skin['champion_name'] = self.champion_id_mapping[champion_id]
                    elif champion_id in self.champion_data:
                        skin['champion_name'] = self.champion_data[champion_id]['name']
        
        account_info['skins'] = skins
        
        # Get season history
        season_history = await db_manager.get_season_history(username)
        
        # Filter for solo queue entries
        solo_queue_history = [entry for entry in season_history if entry.get('queue_type', '').lower() == 'solo']
        
        # Add season history to account info
        if 'rank_info' not in account_info:
            account_info['rank_info'] = {}
        
        account_info['rank_info']['season_history'] = solo_queue_history
        
        return account_info
    
    def _generate_text_content(self, account_data):
        """Generate HTML content for the account."""
        # Get account data
        username = account_data.get('username', 'Unknown')
        game_name = account_data.get('game_name', username)
        tag_line = account_data.get('tag_line', '')
        region = account_data.get('region', 'Unknown')
        country = account_data.get('country', '')
        creation_region = account_data.get('creation_region', region)
        created_at = account_data.get('created_at', '')
        last_checked = account_data.get('last_checked', datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        blue_essence = account_data.get('blue_essence', 0)
        riot_points = account_data.get('riot_points', 0)
        summoner_level = account_data.get('summoner_level', 0)
        
        # Check for ban info and chat restrictions
        is_banned = account_data.get('is_banned', False)
        ban_info = account_data.get('ban_info', '')
        chat_restrictions = account_data.get('chat_restrictions', '')
        
        # Debug logging for chat restrictions
        logger.debug(f"Account data for {username} - is_banned: {is_banned}, ban_info: {ban_info}, chat_restrictions: '{chat_restrictions}'")
        logger.debug(f"Full account data keys: {', '.join(account_data.keys())}")
        
        # Format game name with tag line
        game_name_display = f"{game_name}#{tag_line}" if tag_line else game_name
        
        # Get champions and skins
        champions = account_data.get('champions', [])
        skins = account_data.get('skins', [])
        
        # Get season history
        season_history = account_data.get('season_history', [])
        
        # Get penalties
        penalties = account_data.get('penalties', {})
        low_priority_queue = penalties.get('low_priority_queue', 0)
        ranked_restriction = penalties.get('ranked_restriction', 0)
        
        # Create HTML content with better styling - using multi-line string with explicit concatenation
        html = (
            f'<div style="font-family: Arial, sans-serif; color: #F0E6D2; background-color: #0A1428; padding: 20px; animation: fadeIn 0.5s ease-in-out;">'
            f'<div style="background-color: #1E2328; padding: 25px; border-radius: 8px; margin-bottom: 25px; border: 1px solid #785A28; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3); animation: slideDown 0.5s ease-in-out;">'
            f'<h1 style="color: #C89B3C; margin-top: 0; font-size: 28px; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);">{game_name_display}</h1>'
            f'<p style="margin-bottom: 5px;">Username: {username}</p>'
            f'<div style="display: flex; align-items: center; margin-top: 15px;">'
            f'<div style="background-color: #091428; padding: 8px 15px; border-radius: 4px; margin-right: 10px; border: 1px solid #785A28;">{region}</div>'
            f'<div style="background-color: #091428; padding: 8px 15px; border-radius: 4px; margin-right: 10px; border: 1px solid #785A28;">{country}</div>'
            f'<div style="background-color: #091428; padding: 8px 15px; border-radius: 4px; border: 1px solid #785A28;">Level {summoner_level}</div>'
            f'</div>'
            
            # Add penalties if any
            f'{"<div style=\'margin-top: 15px; color: #E84057;\'><span style=\'display: inline-block; width: 20px; height: 20px; background-color: #E84057; border-radius: 50%; text-align: center; line-height: 20px; margin-right: 5px;\'>!</span>Penalties</div>" if low_priority_queue > 0 or ranked_restriction > 0 else ""}'
            f'</div>'
            
            # Create tabbed interface
            f'<div class="tabs-container" style="width: 100%; margin-bottom: 20px;">'
            f'<div class="tabs-nav" style="display: flex; background-color: rgba(9, 20, 40, 0.8); border-bottom: 2px solid #785A28; overflow: hidden; border-top-left-radius: 8px; border-top-right-radius: 8px;">'
            f'<div class="tab active" data-tab="overview" style="padding: 15px 25px; cursor: pointer; color: #C89B3C; font-weight: bold; transition: all 0.3s ease; position: relative; text-align: center; flex: 1; background-color: rgba(200, 155, 60, 0.15);">Overview</div>'
            f'<div class="tab" data-tab="champions" style="padding: 15px 25px; cursor: pointer; color: #A8A8A8; font-weight: bold; transition: all 0.3s ease; position: relative; text-align: center; flex: 1;">Champions</div>'
            f'<div class="tab" data-tab="skins" style="padding: 15px 25px; cursor: pointer; color: #A8A8A8; font-weight: bold; transition: all 0.3s ease; position: relative; text-align: center; flex: 1;">Skins</div>'
            f'<div class="tab" data-tab="chromas" style="padding: 15px 25px; cursor: pointer; color: #A8A8A8; font-weight: bold; transition: all 0.3s ease; position: relative; text-align: center; flex: 1;">Chromas</div>'
            f'</div>'
            
            # Overview tab content
            f'<div id="overview" class="tab-content active" style="display: block; padding: 20px; animation: fadeIn 0.5s ease-out forwards; background-color: rgba(9, 20, 40, 0.7); border-bottom-left-radius: 8px; border-bottom-right-radius: 8px; border: 1px solid rgba(120, 90, 40, 0.5); border-top: none;">'
            f'{self._generate_account_info_html(username, game_name_display, region, country, creation_region, created_at, last_checked, account_data.get('is_banned', False), account_data.get('ban_info', ''), account_data.get('chat_restrictions', ''))}'
            f'{self._generate_currency_html(blue_essence, riot_points, summoner_level, low_priority_queue, ranked_restriction)}'
            f'{self._generate_ranked_html(account_data)}'
            f'</div>'
            
            # Champions tab content
            f'<div id="champions" class="tab-content" style="display: none; padding: 20px; animation: fadeIn 0.5s ease-out forwards; background-color: rgba(9, 20, 40, 0.7); border-bottom-left-radius: 8px; border-bottom-right-radius: 8px; border: 1px solid rgba(120, 90, 40, 0.5); border-top: none;">'
            f'{self._generate_champions_html(champions)}'
            f'</div>'
            
            # Skins tab content
            f'<div id="skins" class="tab-content" style="display: none; padding: 20px; animation: fadeIn 0.5s ease-out forwards; background-color: rgba(9, 20, 40, 0.7); border-bottom-left-radius: 8px; border-bottom-right-radius: 8px; border: 1px solid rgba(120, 90, 40, 0.5); border-top: none;">'
            f'{self._generate_skins_html(skins)}'
            f'</div>'
            
            # Chromas tab content
            f'<div id="chromas" class="tab-content" style="display: none; padding: 20px; animation: fadeIn 0.5s ease-out forwards; background-color: rgba(9, 20, 40, 0.7); border-bottom-left-radius: 8px; border-bottom-right-radius: 8px; border: 1px solid rgba(120, 90, 40, 0.5); border-top: none;">'
            f'{self._generate_chromas_html(skins)}'
            f'</div>'
            f'</div>'
            
            # Add JavaScript for tab switching
            f'<script>'
            f'document.addEventListener("DOMContentLoaded", function() {{'
            f'  const tabs = document.querySelectorAll(".tab");'
            f'  tabs.forEach(tab => {{'
            f'    tab.addEventListener("click", function() {{'
            f'      // Remove active class from all tabs and content'
            f'      document.querySelectorAll(".tab").forEach(t => t.classList.remove("active"));'
            f'      document.querySelectorAll(".tab-content").forEach(c => c.style.display = "none");'
            f'      '
            f'      // Add active class to clicked tab and corresponding content'
            f'      this.classList.add("active");'
            f'      document.getElementById(this.getAttribute("data-tab")).style.display = "block";'
            f'    }});'
            f'  }});'
            f'}});'
            f'</script>'
            
            # Add CSS styles
            f'<style>'
            f'@keyframes fadeIn {{ from {{ opacity: 0; }} to {{ opacity: 1; }} }}'
            f'@keyframes slideDown {{ from {{ transform: translateY(-20px); opacity: 0; }} to {{ transform: translateY(0); opacity: 1; }} }}'
            f'.dropdown-content a:hover {{background-color: #2A2F35;}}'
            f'</style>'
            
            f'</div>'
        )
        
        return html
    
    def _get_rank_color(self, tier):
        """Get the color for a rank tier."""
        tier = tier.upper() if tier else "UNRANKED"
        colors = {
            "IRON": "#5D5D5D",
            "BRONZE": "#CD7F32",
            "SILVER": "#A8A8A8",
            "GOLD": "#EEBA30",
            "PLATINUM": "#3A7D7B",
            "EMERALD": "#0A846E",
            "DIAMOND": "#076585",
            "MASTER": "#9D4DC5",
            "GRANDMASTER": "#E84057",
            "CHALLENGER": "#F4C874",
            "UNRANKED": "#5D5D5D"
        }
        return colors.get(tier, "#5D5D5D")
    
    def _generate_account_info_html(self, username, game_name_display, region, country, creation_region, created_at, last_checked, is_banned, ban_info, chat_restrictions):
        """Generate HTML for account info section."""
        # Debug logging to check values
        logger.debug(f"_generate_account_info_html for {username} - is_banned: {is_banned}, ban_info: {ban_info}, chat_restrictions: '{chat_restrictions}'")
        
        # Check for penalties
        penalties_html = ""
        if hasattr(self, 'db_manager'):
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                # Get account info for penalties
                account_info = loop.run_until_complete(self.db_manager.get_account_info(username))
                
                # Get penalty data
                penalty_minutes = account_info.get('penalty_minutes', 0)
                ranked_games_remaining = account_info.get('ranked_games_remaining', 0)
                
                # Format penalties HTML
                if penalty_minutes and int(penalty_minutes) > 0:
                    penalties_html = f'<p><strong>Low Priority Queue:</strong> {penalty_minutes} minutes</p>'
                
                if ranked_games_remaining and int(ranked_games_remaining) > 0:
                    penalties_html += f'<p><strong>Ranked Restriction:</strong> {ranked_games_remaining} games remaining</p>'
                
            except Exception as e:
                logger.error(f"Error getting penalties: {str(e)}")
            finally:
                loop.close()
        
        # Format chat restrictions to be more readable
        formatted_chat_restrictions = ''
        if chat_restrictions:
            try:
                # Extract the reason and expiration time
                if ' - ' in chat_restrictions and '(expires: ' in chat_restrictions:
                    # Split by " - " to get the main parts
                    parts = chat_restrictions.split(' - ')
                    if len(parts) > 1:
                        # Take just the reason part (second part)
                        reason = parts[1].split('(expires:')[0].strip()
                        
                        # Extract the timestamp
                        if '(expires: ' in chat_restrictions:
                            timestamp_str = chat_restrictions.split('(expires: ')[1].rstrip(')')
                            try:
                                # Convert milliseconds timestamp to datetime
                                timestamp_ms = int(timestamp_str)
                                expiry_date = datetime.fromtimestamp(timestamp_ms / 1000)  # Convert ms to seconds
                                formatted_date = expiry_date.strftime('%Y-%m-%d %H:%M:%S')
                                formatted_chat_restrictions = f"{reason} (expires: {formatted_date})"
                            except:
                                # If timestamp conversion fails, use the original expiration text
                                formatted_chat_restrictions = f"{reason} (expires: {timestamp_str})"
                        else:
                            formatted_chat_restrictions = reason
                    else:
                        formatted_chat_restrictions = chat_restrictions
                else:
                    formatted_chat_restrictions = chat_restrictions
            except Exception as e:
                logger.error(f"Error formatting chat restrictions: {str(e)}")
                formatted_chat_restrictions = chat_restrictions
        
        # Check for chat restrictions or ban information
        restriction_html = ""
        
        # Check if account is a glitch account
        is_glitch_account = account_data.get('is_glitch_account', False)
        
        # Add ban information if account is banned
        if is_banned and not is_glitch_account:
            logger.debug(f"Account {username} is banned, displaying ban info")
            restriction_html = f'''
            <div style="background-color: rgba(232, 64, 87, 0.1); padding: 10px; border-left: 3px solid #E84057; margin: 10px 0; border-radius: 4px;">
                <h3 style="color: #E84057; margin-top: 0;">Account Banned</h3>
                <p><strong>Ban Info:</strong> {ban_info}</p>
                {penalties_html}
            </div>
            '''
        # Add glitch account information if it's a glitch account
        elif is_glitch_account:
            logger.debug(f"Account {username} is a glitch account, displaying special status")
            restriction_html = f'''
            <div style="background-color: rgba(34, 139, 142, 0.1); padding: 10px; border-left: 3px solid #228B8E; margin: 10px 0; border-radius: 4px;">
                <h3 style="color: #228B8E; margin-top: 0;">Account Status</h3>
                <p><strong>Status:</strong> Unbanned/Glitched Account</p>
                {penalties_html}
            </div>
            '''
        # Add chat restriction information if available
        elif chat_restrictions:
            logger.debug(f"Account {username} has chat restrictions: {formatted_chat_restrictions}")
            restriction_html = f'''
            <div style="background-color: rgba(232, 64, 87, 0.1); padding: 10px; border-left: 3px solid #E84057; margin: 10px 0; border-radius: 4px;">
                <h3 style="color: #E84057; margin-top: 0;">Account Restrictions</h3>
                <p><strong>Chat Restrictions:</strong> {formatted_chat_restrictions}</p>
                {penalties_html}
            </div>
            '''
        # Add a message if there are no restrictions
        else:
            logger.debug(f"Account {username} has no restrictions")
            restriction_html = f'''
            <div style="background-color: rgba(45, 161, 48, 0.1); padding: 10px; border-left: 3px solid #2DA130; margin: 10px 0; border-radius: 4px;">
                <h3 style="color: #2DA130; margin-top: 0;">Account Status</h3>
                <p><strong>Restrictions:</strong> None</p>
                {penalties_html}
            </div>
            '''
        
        # Generate the account info HTML
        html = f'''
        <h3 style="color: #C89B3C; margin-bottom: 15px; border-bottom: 1px solid #785A28; padding-bottom: 5px;">Account Information</h3>
        {restriction_html}
        <div style="display: flex; flex-wrap: wrap; margin-bottom: 20px;">
            <div style="flex: 1; min-width: 250px;">
                <p><strong>Username:</strong> {username}</p>
                <p><strong>Game Name:</strong> {game_name_display}</p>
                <p><strong>Region:</strong> {region}</p>
                <p><strong>Country:</strong> {country}</p>
            </div>
            <div style="flex: 1; min-width: 250px;">
                <p><strong>Creation Region:</strong> {creation_region}</p>
                <p><strong>Created At:</strong> {created_at}</p>
                <p><strong>Last Checked:</strong> {last_checked}</p>
            </div>
        </div>
        '''
        return html
    
    def _generate_champions_html(self, champions):
        """Generate HTML for the champions list."""
        if not champions:
            return "<div style='padding: 15px; color: #A8A8A8; text-align: center; font-style: italic;'>No champions found</div>"
        
        # Sort champions by name
        sorted_champions = sorted(champions, key=lambda x: x.get('name', '') or self._get_champion_name(str(x.get('id', '0'))))
        
        html = ""
        for champion in sorted_champions:
            champion_id = str(champion.get('id', '0'))
            
            # If we have a name in the database, use it directly
            if 'name' in champion and champion['name']:
                name = champion['name']
            else:
                name = self._get_champion_name(champion_id)
                
            purchase_date = champion.get('purchase_date', 'Unknown')
            
            # Format purchase date if available
            if purchase_date and purchase_date != 'Unknown':
                try:
                    dt = datetime.strptime(purchase_date, "%Y%m%dT%H%M%S.%fZ")
                    purchase_date = dt.strftime("%Y-%m-%d")
                except (ValueError, TypeError):
                    pass  # Keep original format if parsing fails
            
            # Get champion image URL using the champion data
            champion_data = self.champion_data.get(champion_id, {})
            alias = champion_data.get('alias', '')
            
            # Special case for Fiddlesticks (using correct capitalization)
            if name.lower() == "fiddlesticks":
                champion_image = "https://ddragon.leagueoflegends.com/cdn/14.6.1/img/champion/Fiddlesticks.png"
            # Use the alias for the image URL if available, otherwise use the name
            elif alias:
                champion_image = f"https://ddragon.leagueoflegends.com/cdn/14.6.1/img/champion/{alias}.png"
            else:
                # Clean name for URL
                clean_name = name.replace(' ', '').replace('\'', '').replace('.', '').replace('&', '')
                champion_image = f"https://ddragon.leagueoflegends.com/cdn/14.6.1/img/champion/{clean_name}.png"
            
            # Generate HTML without newlines
            html += f"<div style=\"background-color: #091428; padding: 10px; border-radius: 4px; border: 1px solid #785A28; display: flex; align-items: center; transition: transform 0.2s ease, box-shadow 0.2s ease; cursor: pointer;\" onmouseover=\"this.style.transform='translateY(-3px)'; this.style.boxShadow='0 5px 15px rgba(0,0,0,0.3)';\" onmouseout=\"this.style.transform='translateY(0)'; this.style.boxShadow='none';\"><div style=\"width: 50px; height: 50px; margin-right: 12px; overflow: hidden; border-radius: 50%; flex-shrink: 0; border: 2px solid #C89B3C;\"><img src=\"{champion_image}\" onerror=\"this.onerror=null; this.src='https://ddragon.leagueoflegends.com/cdn/14.6.1/img/champion/Aatrox.png'\" style=\"width: 100%; height: 100%; object-fit: cover;\"></div><div><div style=\"font-weight: bold; font-size: 1.1em; color: #C89B3C;\">{name}</div><div style=\"color: #A8A8A8; font-size: 0.9em;\">Purchased: {purchase_date}</div></div></div>"
        
        return html
    
    def _get_champion_name(self, champion_id):
        """Get champion name from ID."""
        # If champion_id is 0 or empty, return a default value
        if not champion_id or champion_id == '0':
            logger.warning("Empty or zero champion ID provided")
            return "Unknown Champion"
        
        # Convert to string for lookup
        champion_id = str(champion_id)
        logger.debug(f"Looking up champion name for ID: {champion_id}")
            
        # First check our hardcoded mapping
        if champion_id in self.champion_id_mapping:
            name = self.champion_id_mapping[champion_id]
            logger.debug(f"Found champion name in mapping: {name}")
            return name
        
        # Then check the champion data from API
        if champion_id in self.champion_data:
            name = self.champion_data[champion_id]['name']
            logger.debug(f"Found champion name in API data: {name}")
            return name
        
        # Try to find a champion with a similar ID (some databases might store IDs differently)
        for cid, cdata in self.champion_data.items():
            if cid.lstrip('0') == champion_id.lstrip('0'):
                name = cdata['name']
                logger.debug(f"Found champion name by ID normalization: {name}")
                return name
                
        logger.warning(f"Champion ID not found in any source: {champion_id}")
        return f"Unknown ({champion_id})"
    
    def _get_skin_name(self, skin_id):
        """Get skin name from ID."""
        # If skin_id is 0 or empty, return a default value
        if not skin_id or skin_id == '0':
            return "Unknown Skin"
            
        if skin_id in self.skin_data:
            return self.skin_data[skin_id]['name']
            
        # Try to find a skin with a similar ID
        for sid, sdata in self.skin_data.items():
            if sid.lstrip('0') == skin_id.lstrip('0'):
                return sdata['name']
                
        logger.warning(f"Skin ID not found: {skin_id}")
        return f"Unknown Skin ({skin_id})"
    
    def _generate_skins_html(self, skins):
        """Generate HTML for the skins list."""
        if not skins:
            return "<div style='padding: 15px; color: #A8A8A8; text-align: center; font-style: italic;'>No skins found</div>"
        
        # Filter out chromas
        regular_skins, _ = self._filter_chromas(skins)
        
        if not regular_skins:
            return "<div style='padding: 15px; color: #A8A8A8; text-align: center; font-style: italic;'>No regular skins found (only chromas)</div>"
        
        # Sort skins by champion name and skin name
        sorted_skins = sorted(regular_skins, key=lambda x: (
            x.get('champion_name', '') or self._get_champion_name(str(x.get('champion_id', '0'))), 
            x.get('name', '') or self._get_skin_name(str(x.get('id', '0')))
        ))
        
        html = ""
        for skin in sorted_skins:
            skin_id = str(skin.get('id', '0'))
            champion_id = str(skin.get('champion_id', '0'))
            
            # Get names from our data or database
            if 'champion_name' in skin and skin['champion_name']:
                champion_name = skin['champion_name']
            else:
                champion_name = self._get_champion_name(champion_id)
                
            if 'name' in skin and skin['name']:
                skin_name = skin['name']
            else:
                skin_name = self._get_skin_name(skin_id)
            
            purchase_date = skin.get('purchase_date', 'Unknown')
            
            # Format purchase date if available
            if purchase_date and purchase_date != 'Unknown':
                try:
                    dt = datetime.strptime(purchase_date, "%Y%m%dT%H%M%S.%fZ")
                    purchase_date = dt.strftime("%Y-%m-%d")
                except (ValueError, TypeError):
                    pass  # Keep original format if parsing fails
            
            # Get champion data for alias
            champion_data = self.champion_data.get(champion_id, {})
            alias = champion_data.get('alias', '')
            
            # Special case for Fiddlesticks
            if champion_name.lower() == "fiddlesticks":
                clean_champion_name = "Fiddlesticks"
            # Use the alias for the image URL if available, otherwise use the name
            elif alias:
                clean_champion_name = alias
            else:
                # Clean name for URL
                clean_champion_name = champion_name.replace(' ', '').replace('\'', '').replace('.', '').replace('&', '')
            
            # Get skin image URL - use a splash art URL if possible
            skin_num = skin.get('skin_num', 0)
            if skin_num == 0 and skin_id:
                try:
                    skin_id_int = int(skin_id)
                    skin_num = skin_id_int % 1000  # Extract skin number (last 3 digits)
                except (ValueError, TypeError):
                    skin_num = 0
            
            # Try to construct a splash art URL
            splash_url = f"https://ddragon.leagueoflegends.com/cdn/img/champion/splash/{clean_champion_name}_{skin_num}.jpg"
            
            # Generate HTML without newlines
            html += f"<div style=\"background-color: #091428; padding: 15px; border-radius: 4px; border: 1px solid #785A28; margin-bottom: 15px; transition: transform 0.2s ease, box-shadow 0.2s ease; cursor: pointer; position: relative; overflow: hidden;\" onmouseover=\"this.style.transform='translateY(-3px)'; this.style.boxShadow='0 5px 15px rgba(0,0,0,0.3)';\" onmouseout=\"this.style.transform='translateY(0)'; this.style.boxShadow='none';\"><div style=\"position: relative; height: 120px; margin-bottom: 10px; border-radius: 4px; overflow: hidden; border: 1px solid #C89B3C;\"><img src=\"{splash_url}\" onerror=\"this.onerror=null; this.src='https://ddragon.leagueoflegends.com/cdn/img/champion/splash/{clean_champion_name}_0.jpg'\" style=\"width: 100%; height: 100%; object-fit: cover;\"><div style=\"position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(to top, rgba(0,0,0,0.8), transparent); padding: 10px; color: #C89B3C; font-weight: bold;\">{skin_name}</div></div><div style=\"font-weight: bold; font-size: 1.1em; color: #C89B3C;\">{champion_name} - {skin_name}</div><div style=\"color: #A8A8A8; font-size: 0.9em;\">Purchased: {purchase_date}</div></div>"
        
        return html
    
    def _is_chroma(self, skin_name, skin_id):
        """Determine if a skin is a chroma based on its name, ID, and database lookup."""
        if not skin_name or not skin_id:
            return False

        # Use the account pool's improved chroma detection if available
        if hasattr(self, 'account_pool') and self.account_pool:
            try:
                return self.account_pool._is_chroma(skin_name, int(skin_id))
            except (ValueError, AttributeError):
                pass

        # Fallback to local detection
        skin_name_lower = skin_name.lower()

        # Method 1: Check skin name for chroma indicators
        if "(chroma)" in skin_name_lower:
            return True

        # Method 2: Look for color names in parentheses - common for chromas
        color_names = ["pearl", "ruby", "sapphire", "emerald", "obsidian", "rose quartz", "tanzanite", "turquoise",
                      "bronze", "silver", "gold", "platinum", "diamond", "aquamarine", "citrine", "peridot", "amethyst",
                      "scorch", "catseye", "rainbow"]
        for color in color_names:
            if f"({color}" in skin_name_lower:
                return True

        # Method 3: Check for ranked chromas (Bronze, Silver, Gold, Platinum, Diamond)
        rank_names = ["bronze", "silver", "gold", "platinum", "diamond", "master", "grandmaster", "challenger"]
        for rank in rank_names:
            if f"({rank}" in skin_name_lower:
                return True

        # Method 4: Conservative ID-based check for high skin numbers (likely chromas)
        try:
            skin_id_int = int(skin_id)
            skin_num = skin_id_int % 1000
            # Only consider very high skin numbers as chromas to avoid false positives
            if skin_num > 500:
                return True
        except (ValueError, TypeError):
            pass

        return False

    def _filter_chromas(self, skins):
        """Filter skins to separate regular skins from chromas."""
        regular_skins = []
        chromas = []
        
        for skin in skins:
            skin_id = str(skin.get('id', '0'))
            
            # Get skin name
            if 'name' in skin and skin['name']:
                skin_name = skin['name']
            else:
                skin_name = self._get_skin_name(skin_id)
            
            # Check if it's a chroma
            if self._is_chroma(skin_name, skin_id):
                chromas.append(skin)
            else:
                regular_skins.append(skin)
        
        return regular_skins, chromas 

    def _generate_chromas_html(self, skins):
        """Generate HTML for the chromas list."""
        # Filter out chromas
        _, chromas = self._filter_chromas(skins)
        
        if not chromas:
            return "<div style='padding: 15px; color: #A8A8A8; text-align: center; font-style: italic;'>No chromas found</div>"
        
        # Sort chromas by champion name and skin name
        sorted_chromas = sorted(chromas, key=lambda x: (
            x.get('champion_name', '') or self._get_champion_name(str(x.get('champion_id', '0'))), 
            x.get('name', '') or self._get_skin_name(str(x.get('id', '0')))
        ))
        
        html = ""
        for chroma in sorted_chromas:
            skin_id = str(chroma.get('id', '0'))
            champion_id = str(chroma.get('champion_id', '0'))
            
            # Get names from our data or database
            if 'champion_name' in chroma and chroma['champion_name']:
                champion_name = chroma['champion_name']
            else:
                champion_name = self._get_champion_name(champion_id)
                
            if 'name' in chroma and chroma['name']:
                skin_name = chroma['name']
            else:
                skin_name = self._get_skin_name(skin_id)
            
            purchase_date = chroma.get('purchase_date', 'Unknown')
            
            # Format purchase date if available
            if purchase_date and purchase_date != 'Unknown':
                try:
                    dt = datetime.strptime(purchase_date, "%Y%m%dT%H%M%S.%fZ")
                    purchase_date = dt.strftime("%Y-%m-%d")
                except (ValueError, TypeError):
                    pass  # Keep original format if parsing fails
            
            # Get champion data for alias
            champion_data = self.champion_data.get(champion_id, {})
            alias = champion_data.get('alias', '')
            
            # Special case for Fiddlesticks
            if champion_name.lower() == "fiddlesticks":
                clean_champion_name = "Fiddlesticks"
            # Use the alias for the image URL if available, otherwise use the name
            elif alias:
                clean_champion_name = alias
            else:
                # Clean name for URL
                clean_champion_name = champion_name.replace(' ', '').replace('\'', '').replace('.', '').replace('&', '')
            
            # Get skin image URL - use a splash art URL if possible
            skin_num = chroma.get('skin_num', 0)
            if skin_num == 0 and skin_id:
                try:
                    skin_id_int = int(skin_id)
                    skin_num = skin_id_int % 1000  # Extract skin number (last 3 digits)
                except (ValueError, TypeError):
                    skin_num = 0
            
            # Try to construct a splash art URL
            splash_url = f"https://ddragon.leagueoflegends.com/cdn/img/champion/splash/{clean_champion_name}_{skin_num}.jpg"
            
            # Generate HTML with a different style for chromas
            html += f"<div style=\"background-color: #091428; padding: 15px; border-radius: 4px; border: 1px solid #785A28; margin-bottom: 15px; transition: transform 0.2s ease, box-shadow 0.2s ease; cursor: pointer; position: relative; overflow: hidden;\" onmouseover=\"this.style.transform='translateY(-3px)'; this.style.boxShadow='0 5px 15px rgba(0,0,0,0.3)';\" onmouseout=\"this.style.transform='translateY(0)'; this.style.boxShadow='none';\"><div style=\"position: relative; height: 120px; margin-bottom: 10px; border-radius: 4px; overflow: hidden; border: 1px solid #C89B3C;\"><img src=\"{splash_url}\" onerror=\"this.onerror=null; this.src='https://ddragon.leagueoflegends.com/cdn/img/champion/splash/{clean_champion_name}_0.jpg'\" style=\"width: 100%; height: 100%; object-fit: cover;\"><div style=\"position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(to top, rgba(0,0,0,0.8), transparent); padding: 10px; color: #C89B3C; font-weight: bold;\">{skin_name}</div><div style=\"position: absolute; top: 10px; right: 10px; background-color: rgba(200, 155, 60, 0.8); color: #0A1428; padding: 5px 10px; border-radius: 4px; font-size: 12px; font-weight: bold;\">CHROMA</div></div><div style=\"font-weight: bold; font-size: 1.1em; color: #C89B3C;\">{champion_name} - {skin_name}</div><div style=\"color: #A8A8A8; font-size: 0.9em;\">Purchased: {purchase_date}</div></div>"
        
        return html 

    def show_file_menu(self):
        """Show the file menu when the File button is clicked."""
        self.file_button_menu.popup(QCursor.pos())
    
    def show_export_menu(self):
        """Show the export menu when the Export button is clicked."""
        self.export_menu.popup(QCursor.pos())
    
    def refresh_account(self):
        """Refresh the current account data."""
        if self.current_account:
            self.display_account(self.current_account)
    
    def export_as_html(self):
        """Export the current account data as HTML."""
        if not self.current_account:
            return
            
        from PyQt6.QtWidgets import QFileDialog
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export as HTML", f"{self.current_account}_account.html", "HTML Files (*.html)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(self.text_display.toHtml())
                logger.info(f"Exported HTML to {file_path}")
            except Exception as e:
                logger.error(f"Error exporting HTML: {str(e)}")
    
    def export_as_json(self):
        """Export the current account data as JSON."""
        if not self.current_account:
            return
            
        from PyQt6.QtWidgets import QFileDialog
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export as JSON", f"{self.current_account}_account.json", "JSON Files (*.json)"
        )
        
        if file_path:
            try:
                # Create a new event loop for this operation
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                # Get account data
                account_data = loop.run_until_complete(self._get_account_data(self.current_account))
                
                # Close the loop properly
                loop.close()
                
                with open(file_path, 'w', encoding='utf-8') as file:
                    json.dump(account_data, file, indent=4)
                logger.info(f"Exported JSON to {file_path}")
            except Exception as e:
                logger.error(f"Error exporting JSON: {str(e)}")
    
    def export_as_text(self):
        """Export the current account data as plain text."""
        if not self.current_account:
            return
            
        from PyQt6.QtWidgets import QFileDialog
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export as Text", f"{self.current_account}_account.txt", "Text Files (*.txt)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(self.text_display.toPlainText())
                logger.info(f"Exported text to {file_path}")
            except Exception as e:
                logger.error(f"Error exporting text: {str(e)}")
    
    def select_txt_file(self):
        """Select a text file with username:password format to import accounts."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Text File", "", "Text Files (*.txt)"
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    lines = file.readlines()
                
                # Process lines in format username:password
                accounts = []
                for line in lines:
                    line = line.strip()
                    if ':' in line:
                        username, password = line.split(':', 1)
                        accounts.append((username.strip(), password.strip()))
                
                if accounts:
                    # Create a new event loop for this operation
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    # Import accounts to database
                    db_manager = DatabaseManager()
                    for username, password in accounts:
                        loop.run_until_complete(db_manager.add_account(username, password))
                    
                    # Close the loop properly
                    loop.close()
                    
                    logger.info(f"Imported {len(accounts)} accounts from {file_path}")
                    
                    # Show confirmation message
                    from PyQt6.QtWidgets import QMessageBox
                    QMessageBox.information(self, "Import Successful", f"Successfully imported {len(accounts)} accounts.")
            except Exception as e:
                logger.error(f"Error importing accounts from text file: {str(e)}")
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.critical(self, "Import Error", f"Error importing accounts: {str(e)}")
    
    def save_database(self):
        """Save the database to a file."""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Database", "accounts_database.json", "JSON Files (*.json)"
        )
        
        if file_path:
            try:
                # Create a new event loop for this operation
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                # Get all accounts from database
                db_manager = DatabaseManager()
                accounts = loop.run_until_complete(db_manager.get_all_accounts())
                
                # Close the loop properly
                loop.close()
                
                # Save to file
                with open(file_path, 'w', encoding='utf-8') as file:
                    json.dump(accounts, file, indent=4)
                
                logger.info(f"Saved database to {file_path}")
                
                # Show confirmation message
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.information(self, "Save Successful", f"Database saved to {file_path}")
            except Exception as e:
                logger.error(f"Error saving database: {str(e)}")
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.critical(self, "Save Error", f"Error saving database: {str(e)}")
    
    def load_database(self):
        """Load the database from a file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Load Database", "", "JSON Files (*.json)"
        )
        
        if file_path:
            try:
                # Load from file
                with open(file_path, 'r', encoding='utf-8') as file:
                    accounts = json.load(file)
                
                # Create a new event loop for this operation
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                # Import accounts to database
                db_manager = DatabaseManager()
                for account in accounts:
                    loop.run_until_complete(db_manager.add_account_data(account))
                
                # Close the loop properly
                loop.close()
                
                logger.info(f"Loaded {len(accounts)} accounts from {file_path}")
                
                # Show confirmation message
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.information(self, "Load Successful", f"Successfully loaded {len(accounts)} accounts.")
            except Exception as e:
                logger.error(f"Error loading database: {str(e)}")
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.critical(self, "Load Error", f"Error loading database: {str(e)}")
    
    def show_settings(self):
        """Show settings dialog."""
        # This is a placeholder for the settings dialog
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.information(self, "Settings", "Settings dialog will be implemented here.")
    
    def clear_all_accounts(self):
        """Clear all accounts from the database."""
        # Show confirmation dialog
        from PyQt6.QtWidgets import QMessageBox
        reply = QMessageBox.question(self, "Clear All Accounts", 
                                    "Are you sure you want to clear all accounts from the database?",
                                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                # Create a new event loop for this operation
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                # Clear all accounts
                db_manager = DatabaseManager()
                loop.run_until_complete(db_manager.clear_all_accounts())
                
                # Close the loop properly
                loop.close()
                
                logger.info("Cleared all accounts from database")
                
                # Show confirmation message
                QMessageBox.information(self, "Clear Successful", "All accounts have been cleared from the database.")
            except Exception as e:
                logger.error(f"Error clearing accounts: {str(e)}")
                QMessageBox.critical(self, "Clear Error", f"Error clearing accounts: {str(e)}")
    
    def copy_all_accounts_info(self):
        """Copy all accounts information to clipboard."""
        try:
            # Create a new event loop for this operation
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Get all accounts from database
            db_manager = DatabaseManager()
            accounts = loop.run_until_complete(db_manager.get_all_accounts())
            
            # Close the loop properly
            loop.close()
            
            # Format account information
            text = ""
            for account in accounts:
                username = account.get('username', 'Unknown')
                game_name = account.get('game_name', username)
                tag_line = account.get('tag_line', '')
                region = account.get('region', 'Unknown')
                level = account.get('summoner_level', 0)
                blue_essence = account.get('blue_essence', 0)
                riot_points = account.get('riot_points', 0)
                
                # Format game name with tag line
                game_name_display = f"{game_name}#{tag_line}" if tag_line else game_name
                
                # Get champions and skins
                champions = account.get('champions', [])
                skins = account.get('skins', [])
                
                # Format account info
                text += f"Account: {username}\n"
                text += f"In-Game Name: {game_name_display}\n"
                text += f"Region: {region}\n"
                text += f"Level: {level}\n"
                text += f"Blue Essence: {blue_essence}\n"
                text += f"Riot Points: {riot_points}\n"
                
                # Add champions
                text += f"Champions ({len(champions)}):\n"
                for champion in champions:
                    champion_name = champion.get('name', '') or self._get_champion_name(str(champion.get('id', '0')))
                    text += f"- {champion_name}\n"
                
                # Add skins
                text += f"Skins ({len(skins)}):\n"
                for skin in skins:
                    skin_name = skin.get('name', 'Unknown Skin')
                    champion_id = str(skin.get('champion_id', '0'))
                    champion_name = skin.get('champion_name', '') or self._get_champion_name(champion_id)
                    text += f"- {champion_name} - {skin_name}\n"
                
                text += "\n" + "-" * 50 + "\n\n"
            
            # Copy to clipboard
            from PyQt6.QtWidgets import QApplication
            QApplication.clipboard().setText(text)
            
            logger.info("Copied all accounts info to clipboard")
            
            # Show confirmation message
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(self, "Copy Successful", "All accounts information has been copied to clipboard.")
        except Exception as e:
            logger.error(f"Error copying accounts info: {str(e)}")
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Copy Error", f"Error copying accounts info: {str(e)}")
    
    def copy_accounts_no_chromas(self):
        """Copy all accounts information without chromas to clipboard."""
        try:
            # Create a new event loop for this operation
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Get all accounts from database
            db_manager = DatabaseManager()
            accounts = loop.run_until_complete(db_manager.get_all_accounts())
            
            # Close the loop properly
            loop.close()
            
            # Format account information
            text = ""
            for account in accounts:
                username = account.get('username', 'Unknown')
                game_name = account.get('game_name', username)
                tag_line = account.get('tag_line', '')
                region = account.get('region', 'Unknown')
                level = account.get('summoner_level', 0)
                blue_essence = account.get('blue_essence', 0)
                riot_points = account.get('riot_points', 0)
                
                # Format game name with tag line
                game_name_display = f"{game_name}#{tag_line}" if tag_line else game_name
                
                # Get champions and skins
                champions = account.get('champions', [])
                skins = account.get('skins', [])
                
                # Filter out chromas
                regular_skins, _ = self._filter_chromas(skins)
                
                # Format account info
                text += f"Account: {username}\n"
                text += f"In-Game Name: {game_name_display}\n"
                text += f"Region: {region}\n"
                text += f"Level: {level}\n"
                text += f"Blue Essence: {blue_essence}\n"
                text += f"Riot Points: {riot_points}\n"
                
                # Add champions
                text += f"Champions ({len(champions)}):\n"
                for champion in champions:
                    champion_name = champion.get('name', '') or self._get_champion_name(str(champion.get('id', '0')))
                    text += f"- {champion_name}\n"
                
                # Add skins (without chromas)
                text += f"Skins ({len(regular_skins)}):\n"
                for skin in regular_skins:
                    skin_name = skin.get('name', 'Unknown Skin')
                    champion_id = str(skin.get('champion_id', '0'))
                    champion_name = skin.get('champion_name', '') or self._get_champion_name(champion_id)
                    text += f"- {champion_name} - {skin_name}\n"
                
                text += "\n" + "-" * 50 + "\n\n"
            
            # Copy to clipboard
            from PyQt6.QtWidgets import QApplication
            QApplication.clipboard().setText(text)
            
            logger.info("Copied accounts info without chromas to clipboard")
            
            # Show confirmation message
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(self, "Copy Successful", "Accounts information (without chromas) has been copied to clipboard.")
        except Exception as e:
            logger.error(f"Error copying accounts info: {str(e)}")
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Copy Error", f"Error copying accounts info: {str(e)}")
    
    def copy_accounts_no_skins_chromas(self):
        """Copy all accounts information without skins and chromas to clipboard."""
        try:
            # Create a new event loop for this operation
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Get all accounts from database
            db_manager = DatabaseManager()
            accounts = loop.run_until_complete(db_manager.get_all_accounts())
            
            # Close the loop properly
            loop.close()
            
            # Format account information
            text = ""
            for account in accounts:
                username = account.get('username', 'Unknown')
                game_name = account.get('game_name', username)
                tag_line = account.get('tag_line', '')
                region = account.get('region', 'Unknown')
                level = account.get('summoner_level', 0)
                blue_essence = account.get('blue_essence', 0)
                riot_points = account.get('riot_points', 0)
                
                # Format game name with tag line
                game_name_display = f"{game_name}#{tag_line}" if tag_line else game_name
                
                # Get champions
                champions = account.get('champions', [])
                
                # Format account info
                text += f"Account: {username}\n"
                text += f"In-Game Name: {game_name_display}\n"
                text += f"Region: {region}\n"
                text += f"Level: {level}\n"
                text += f"Blue Essence: {blue_essence}\n"
                text += f"Riot Points: {riot_points}\n"
                
                # Add champions
                text += f"Champions ({len(champions)}):\n"
                for champion in champions:
                    champion_name = champion.get('name', '') or self._get_champion_name(str(champion.get('id', '0')))
                    text += f"- {champion_name}\n"
                
                text += "\n" + "-" * 50 + "\n\n"
            
            # Copy to clipboard
            from PyQt6.QtWidgets import QApplication
            QApplication.clipboard().setText(text)
            
            logger.info("Copied accounts info without skins and chromas to clipboard")
            
            # Show confirmation message
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(self, "Copy Successful", "Accounts information (without skins and chromas) has been copied to clipboard.")
        except Exception as e:
            logger.error(f"Error copying accounts info: {str(e)}")
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Copy Error", f"Error copying accounts info: {str(e)}")

    def loadAccountDetails(self, username, password, region, game_name, tag_line, level, be, rp, created_at, refresh_token, access_token, id_token, last_checked, is_banned, ban_info, chat_restrictions, country, creation_region, rank_info_str):
        """Load account details into the viewer."""
        logger.debug(f"Loading account details for {username}")
        
        try:
            if rank_info_str:
                # Parse the rank info JSON
                try:
                    rank_info = json.loads(rank_info_str)
                except:
                    logger.error(f"Error parsing rank info JSON: {rank_info_str}")
                    rank_info = {}
            else:
                rank_info = {}
                
            # Format in-game name for display
            game_name_display = f"{game_name}#{tag_line}" if tag_line and game_name else username
            
            # Format last checked date
            if last_checked:
                try:
                    # Try to parse the date
                    last_checked_date = datetime.strptime(last_checked, '%Y-%m-%d %H:%M:%S')
                    last_checked_formatted = last_checked_date.strftime('%Y-%m-%d %H:%M:%S')
                except (ValueError, TypeError):
                    last_checked_formatted = last_checked
            else:
                last_checked_formatted = 'Never'
                
            # Generate HTML sections
            account_info_html = self._generate_account_info_html(
                username, 
                game_name_display, 
                region, 
                country, 
                creation_region, 
                created_at, 
                last_checked_formatted,
                is_banned,
                ban_info,
                chat_restrictions
            )
        
            # Set the HTML in the web view
            self.html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Account Details</title>
                <style>
                    body {{
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        color: #F0E6D2;
                        background-color: #0A1428;
                        margin: 0;
                        padding: 20px;
                    }}
                    .account-card {{
                        background-color: #091428;
                        border-radius: 8px;
                        border: 1px solid #785A28;
                        padding: 20px;
                        margin-bottom: 20px;
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                    }}
                    h2 {{
                        color: #C89B3C;
                        margin-top: 0;
                        border-bottom: 1px solid #785A28;
                        padding-bottom: 10px;
                    }}
                    .section {{
                        margin-bottom: 20px;
                    }}
                    .flex-container {{
                        display: flex;
                        flex-wrap: wrap;
                        gap: 20px;
                    }}
                    .flex-item {{
                        flex: 1;
                        min-width: 300px;
                    }}
                    .champion-list, .skin-list, .chromas-list {{
                        display: grid;
                        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                        gap: 10px;
                        margin-top: 10px;
                    }}
                    @media (max-width: 768px) {{
                        .flex-container {{
                            flex-direction: column;
                        }}
                    }}
                </style>
            </head>
            <body>
                <div class="account-card">
                    <h2>{game_name_display}</h2>
                    <div class="section">
                        {account_info_html}
                    </div>
                    
                    <div class="section">
                        <div id="rank-info">
                            <h3 style="color: #C89B3C; margin-bottom: 15px; border-bottom: 1px solid #785A28; padding-bottom: 5px;">Rank Information</h3>
                            <div style="display: flex; flex-wrap: wrap; gap: 20px;">
                                <div id="solo-rank-info" style="flex: 1; min-width: 250px;">
                                </div>
                                <div id="flex-rank-info" style="flex: 1; min-width: 250px;">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="section">
                        <h3 style="color: #C89B3C; margin-bottom: 15px; border-bottom: 1px solid #785A28; padding-bottom: 5px;">Champions & Skins</h3>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                            <div>
                                <button id="toggleChampionsBtn" style="background-color: #1E2328; color: #C89B3C; border: 1px solid #785A28; padding: 5px 10px; cursor: pointer; border-radius: 4px;">Hide Champions</button>
                            </div>
                            <div>
                                <button id="toggleSkinsBtn" style="background-color: #1E2328; color: #C89B3C; border: 1px solid #785A28; padding: 5px 10px; cursor: pointer; border-radius: 4px;">Hide Skins</button>
                                <button id="toggleChromasBtn" style="background-color: #1E2328; color: #C89B3C; border: 1px solid #785A28; padding: 5px 10px; cursor: pointer; border-radius: 4px; margin-left: 5px;">Hide Chromas</button>
                            </div>
                        </div>
                        
                        <div id="champions-container" class="section">
                            <h4 style="color: #C89B3C;">Champions</h4>
                            <div id="champions-loading" style="text-align: center; color: #A8A8A8;">Loading champions...</div>
                            <div id="champions-list" class="champion-list" style="display: none;"></div>
                        </div>
                        
                        <div id="skins-container" class="section">
                            <h4 style="color: #C89B3C;">Skins</h4>
                            <div id="skins-loading" style="text-align: center; color: #A8A8A8;">Loading skins...</div>
                            <div id="skins-list" class="skin-list" style="display: none;"></div>
                        </div>
                        
                        <div id="chromas-container" class="section">
                            <h4 style="color: #C89B3C;">Chromas</h4>
                            <div id="chromas-loading" style="text-align: center; color: #A8A8A8;">Loading chromas...</div>
                            <div id="chromas-list" class="chromas-list" style="display: none;"></div>
                        </div>
                    </div>
                </div>
                
                <script>
                    // JavaScript for interactive features
                    document.getElementById('toggleChampionsBtn').addEventListener('click', function() {{
                        const container = document.getElementById('champions-container');
                        const button = document.getElementById('toggleChampionsBtn');
                        if (container.style.display === 'none') {{
                            container.style.display = 'block';
                            button.textContent = 'Hide Champions';
                        }} else {{
                            container.style.display = 'none';
                            button.textContent = 'Show Champions';
                        }}
                    }});
                    
                    document.getElementById('toggleSkinsBtn').addEventListener('click', function() {{
                        const container = document.getElementById('skins-container');
                        const button = document.getElementById('toggleSkinsBtn');
                        if (container.style.display === 'none') {{
                            container.style.display = 'block';
                            button.textContent = 'Hide Skins';
                        }} else {{
                            container.style.display = 'none';
                            button.textContent = 'Show Skins';
                        }}
                    }});
                    
                    document.getElementById('toggleChromasBtn').addEventListener('click', function() {{
                        const container = document.getElementById('chromas-container');
                        const button = document.getElementById('toggleChromasBtn');
                        if (container.style.display === 'none') {{
                            container.style.display = 'block';
                            button.textContent = 'Hide Chromas';
                        }} else {{
                            container.style.display = 'none';
                            button.textContent = 'Show Chromas';
                        }}
                    }});
                    
                    // Load rank info
                    function loadRankInfo() {{
                        const rankInfo = {json.dumps(rank_info)};
                        
                        // Solo queue rank
                        const soloRank = rankInfo.solo || {{}};
                        const soloTier = soloRank.tier || 'UNRANKED';
                        const soloDivision = soloRank.division || '';
                        const soloLp = soloRank.lp || 0;
                        const soloWins = soloRank.wins || 0;
                        const soloLosses = soloRank.losses || 0;
                        const soloWinrate = soloWins + soloLosses > 0 ? Math.round((soloWins / (soloWins + soloLosses)) * 100) : 0;
                        
                        // Previous season solo rank
                        const soloPrevTier = soloRank.previous_tier || 'UNRANKED';
                        const soloPrevDivision = soloRank.previous_division || '';
                        
                        // Flex queue rank
                        const flexRank = rankInfo.flex || {{}};
                        const flexTier = flexRank.tier || 'UNRANKED';
                        const flexDivision = flexRank.division || '';
                        const flexLp = flexRank.lp || 0;
                        const flexWins = flexRank.wins || 0;
                        const flexLosses = flexRank.losses || 0;
                        const flexWinrate = flexWins + flexLosses > 0 ? Math.round((flexWins / (flexWins + flexLosses)) * 100) : 0;
                        
                        // Previous season flex rank
                        const flexPrevTier = flexRank.previous_tier || 'UNRANKED';
                        const flexPrevDivision = flexRank.previous_division || '';
                        
                        // Get rank colors
                        const tierColors = {{
                            'IRON': '#5D5D5D',
                            'BRONZE': '#CD7F32',
                            'SILVER': '#C0C0C0',
                            'GOLD': '#FFD700',
                            'PLATINUM': '#36EAFF',
                            'EMERALD': '#50C878',
                            'DIAMOND': '#B9F2FF',
                            'MASTER': '#9D00FF',
                            'GRANDMASTER': '#E84057',
                            'CHALLENGER': '#F4C874',
                            'UNRANKED': '#5D5D5D'
                        }};
                        
                        // Generate HTML for Solo rank
                        let soloRankHtml = `
                            <h4 style="color: ${{tierColors[soloTier] || '#5D5D5D'}};">Solo/Duo Rank</h4>
                            <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                <div style="margin-right: 10px;">
                                    <img src="https://raw.communitydragon.org/pbe/plugins/rcp-fe-lol-static-assets/global/default/images/ranked-mini-crests/${{soloTier.toLowerCase()}}.png" 
                                         style="width: 50px; height: 50px; object-fit: contain;" 
                                         onerror="this.src='https://raw.communitydragon.org/pbe/plugins/rcp-fe-lol-static-assets/global/default/images/ranked-mini-crests/unranked.png'">
                                </div>
                                <div>
                                    <div style="font-weight: bold; color: ${{tierColors[soloTier] || '#5D5D5D'}};">
                                        ${{soloTier !== 'UNRANKED' ? `${{soloTier}} ${{soloDivision}}` : 'UNRANKED'}}
                                    </div>
                                    ${{soloTier !== 'UNRANKED' ? `<div>${{soloLp}} LP</div>` : ''}}
                                    ${{soloTier !== 'UNRANKED' ? `<div>${{soloWins}}W ${{soloLosses}}L (${{soloWinrate}}%)</div>` : ''}}
                                </div>
                            </div>
                            <div style="margin-top: 5px;">
                                <p style="margin: 5px 0;"><strong>Previous Season:</strong> <span style="color: ${{tierColors[soloPrevTier] || '#5D5D5D'}};">${{soloPrevTier !== 'UNRANKED' ? `${{soloPrevTier}} ${{soloPrevDivision}}` : 'UNRANKED'}}</span></p>
                            </div>
                        `;
                        
                        // Generate HTML for Flex rank
                        let flexRankHtml = `
                            <h4 style="color: ${{tierColors[flexTier] || '#5D5D5D'}};">Flex Rank</h4>
                            <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                <div style="margin-right: 10px;">
                                    <img src="https://raw.communitydragon.org/pbe/plugins/rcp-fe-lol-static-assets/global/default/images/ranked-mini-crests/${{flexTier.toLowerCase()}}.png" 
                                         style="width: 50px; height: 50px; object-fit: contain;" 
                                         onerror="this.src='https://raw.communitydragon.org/pbe/plugins/rcp-fe-lol-static-assets/global/default/images/ranked-mini-crests/unranked.png'">
                                </div>
                                <div>
                                    <div style="font-weight: bold; color: ${{tierColors[flexTier] || '#5D5D5D'}};">
                                        ${{flexTier !== 'UNRANKED' ? `${{flexTier}} ${{flexDivision}}` : 'UNRANKED'}}
                                    </div>
                                    ${{flexTier !== 'UNRANKED' ? `<div>${{flexLp}} LP</div>` : ''}}
                                    ${{flexTier !== 'UNRANKED' ? `<div>${{flexWins}}W ${{flexLosses}}L (${{flexWinrate}}%)</div>` : ''}}
                                </div>
                            </div>
                            <div style="margin-top: 5px;">
                                <p style="margin: 5px 0;"><strong>Previous Season:</strong> <span style="color: ${{tierColors[flexPrevTier] || '#5D5D5D'}};">${{flexPrevTier !== 'UNRANKED' ? `${{flexPrevTier}} ${{flexPrevDivision}}` : 'UNRANKED'}}</span></p>
                            </div>
                        `;
                        
                        // Update the HTML
                        document.getElementById('solo-rank-info').innerHTML = soloRankHtml;
                        document.getElementById('flex-rank-info').innerHTML = flexRankHtml;
                    }}
                    
                    // Load rank info when page loads
                    loadRankInfo();
                    
                    // Load champions and skins via JavaScript
                    function loadGameData() {{
                        // Signal that we need to load game data
                        window.pyqtBridge.loadGameData('{username}');
                    }}
                    
                    // Function to update champions list
                    function updateChampionsList(champions) {{
                        const championsList = document.getElementById('champions-list');
                        const loadingIndicator = document.getElementById('champions-loading');
                        
                        // Check if we have champions
                        if (champions && champions.length > 0) {{
                            // Clear loading indicator and show the list
                            loadingIndicator.style.display = 'none';
                            championsList.style.display = 'grid';
                            championsList.innerHTML = champions;
                        }} else {{
                            // Show no champions message
                            loadingIndicator.innerHTML = 'No champions found';
                        }}
                    }}
                    
                    // Function to update skins list
                    function updateSkinsList(skins) {{
                        const skinsList = document.getElementById('skins-list');
                        const loadingIndicator = document.getElementById('skins-loading');
                        
                        // Check if we have skins
                        if (skins && skins.length > 0) {{
                            // Clear loading indicator and show the list
                            loadingIndicator.style.display = 'none';
                            skinsList.style.display = 'grid';
                            skinsList.innerHTML = skins;
                        }} else {{
                            // Show no skins message
                            loadingIndicator.innerHTML = 'No skins found';
                        }}
                    }}
                    
                    // Function to update chromas list
                    function updateChromasList(chromas) {{
                        const chromasList = document.getElementById('chromas-list');
                        const loadingIndicator = document.getElementById('chromas-loading');
                        
                        // Check if we have chromas
                        if (chromas && chromas.length > 0) {{
                            // Clear loading indicator and show the list
                            loadingIndicator.style.display = 'none';
                            chromasList.style.display = 'grid';
                            chromasList.innerHTML = chromas;
                        }} else {{
                            // Show no chromas message
                            loadingIndicator.innerHTML = 'No chromas found';
                        }}
                    }}
                    
                    // Load game data when page is loaded
                    window.onload = loadGameData;
                </script>
            </body>
            </html>
            """
            
            # Update the web view with the new HTML
            self.web_view.setHtml(self.html_content)
            
            # Store the account information
            self.current_account = {
                'username': username,
                'password': password,
                'region': region,
                'game_name': game_name,
                'tag_line': tag_line
            }
            
            # Signal that account details have been loaded
            self.accountDetailsLoaded.emit(username)
            
        except Exception as e:
            logger.error(f"Error loading account details: {str(e)}")
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Error Loading Account", f"Error loading account details: {str(e)}") 