#!/usr/bin/env python3
"""
Script to reset a glitch account back to normal status
"""

import asyncio
import sys
import os

# Add the database directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'database'))

from manager import DatabaseManager

async def reset_glitch_account(username):
    """Reset a glitch account back to normal status"""
    print(f"Resetting glitch account status for: {username}")
    
    # Initialize database manager
    db_manager = DatabaseManager("accounts.db")
    
    try:
        # Get current account info
        account_info = await db_manager.get_account_info(username)
        if not account_info:
            print(f"Account {username} not found in database")
            return
        
        print(f"Current status:")
        print(f"  is_banned: {account_info.get('is_banned', False)}")
        print(f"  is_glitch_account: {account_info.get('is_glitch_account', False)}")
        print(f"  ban_info: {account_info.get('ban_info', '')}")
        
        # Check if it's a dodge timer account
        ban_info = account_info.get('ban_info', '')
        if "QUEUE_LOCKOUT - QUEUE_DODGE" in ban_info:
            print(f"Detected dodge timer in ban_info, resetting to normal account...")
            
            # Update account to normal status
            await db_manager.update_account(
                username=username,
                is_banned=False,
                is_glitch_account=False
                # Keep ban_info for dodge timer display
            )
            
            print(f"Successfully reset {username} to normal account status")
        else:
            print(f"Account does not appear to have dodge timer, no changes made")
            
    except Exception as e:
        print(f"Error resetting account: {e}")
    
    finally:
        await db_manager.close()

async def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python reset_glitch_account.py <username>")
        print("Example: python reset_glitch_account.py pascuine123")
        return
    
    username = sys.argv[1]
    await reset_glitch_account(username)

if __name__ == "__main__":
    asyncio.run(main())
