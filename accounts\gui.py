from PyQt6.QtWidgets import (QA<PERSON>lication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QPushButton, QProgressBar, QLabel, 
                            QListWidget, QSplitter, QTabWidget, QTextEdit,
                            QMenuBar, QMenu, QFileDialog, QMessageBox, QDialog,
                            QCheckBox, QLineEdit, QComboBox, QGroupBox, QScrollArea,
                            QDialogButtonBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QMetaObject, Q_ARG, QObject, QPoint, QTimer
import asyncio
import sys
import json
import os
from accounts.account_pool import AccountPool
from custom_logger.logger import logger
from suppliers.auth import get_auth_tokens, create_session, solve_captcha
from datetime import datetime
import socket

class AuthHandler(QObject):
    auth_completed = pyqtSignal(bool)
    
    def __init__(self):
        super().__init__()
        
    def authenticate(self, username, password):
        try:
            session = create_session(verify_ssl=False)
            
            # First try to get tokens
            tokens = solve_captcha(username, password, session)
            
            self.auth_completed.emit(bool(tokens))
            
        except Exception as e:
            logger.error(f"Auth error: {str(e)}")
            self.auth_completed.emit(False)

class AsyncWorker(QThread):
    finished = pyqtSignal()
    progress = pyqtSignal(int, int)
    error = pyqtSignal(str)
    auth_needed = pyqtSignal(str, str)
    auth_completed = pyqtSignal(bool)
    
    def __init__(self, account_pool):
        super().__init__()
        self.account_pool = account_pool
        self.running = True
        self.auth_done = False
        
    def run(self):
        try:
            accounts = self.account_pool.get_all_accounts()
            total = len(accounts)
            
            for i, account in enumerate(accounts, 1):
                if not self.running:
                    break
                    
                self.progress.emit(i, total)
                
                # Reset auth flag
                self.auth_done = False
                
                # Handle authentication in main thread
                self.auth_needed.emit(account.username, account.password)
                
                # Wait for auth to complete with timeout
                wait_count = 0
                while not self.auth_done and self.running and wait_count < 300:  # 30 second timeout
                    self.msleep(100)
                    wait_count += 1
                
                if not self.running:
                    break
                    
                if not self.auth_done:
                    logger.error(f"Authentication timeout for {account.username}")
                    continue
                
                # Check account using the standard method regardless of region
                logger.info(f"Starting check for account {account.username} with region {account.region}")
                
                # Create a new event loop for async operations
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    # Use the standard check_account method which retrieves all account information
                    # This will ensure that champions, blue essence, etc. are all saved
                    loop.run_until_complete(self.account_pool.check_account(account))
                    logger.info(f"Successfully checked account {account.username} using standard method")
                except Exception as e:
                    logger.error(f"Error handling account {account.username}: {str(e)}")
                    
                    # Fallback to basic info extraction if check_account fails
                    try:
                        # Get userinfo
                        userinfo = loop.run_until_complete(account.authenticator.get_userinfo())
                        if userinfo:
                            # Import decode_jwt_payload function
                            from accounts.account_pool import decode_jwt_payload
                            
                            # Decode JWT token
                            decoded_info = decode_jwt_payload(userinfo)
                            
                            # Extract basic account info
                            summoner_level = decoded_info.get('lol_account', {}).get('summoner_level', 0)
                            game_name = decoded_info.get('acct', {}).get('game_name', account.username)
                            tag_line = decoded_info.get('acct', {}).get('tag_line', '')
                            country = decoded_info.get('country', '')
                            
                            # Extract creation date and region
                            acct_info = decoded_info.get('acct', {})
                            created_at_timestamp = acct_info.get('created_at')
                            created_at = None
                            if created_at_timestamp:
                                try:
                                    created_at = datetime.fromtimestamp(created_at_timestamp / 1000).strftime("%Y-%m-%d %H:%M:%S")
                                    logger.info(f"Extracted creation date for {account.username}: {created_at} from timestamp {created_at_timestamp}")
                                except Exception as e:
                                    logger.error(f"Error converting creation timestamp: {str(e)}")
                            else:
                                # Try to get creation date from country_at as fallback
                                country_at = decoded_info.get('country_at')
                                if country_at:
                                    try:
                                        created_at = datetime.fromtimestamp(country_at / 1000).strftime("%Y-%m-%d %H:%M:%S")
                                        logger.info(f"Using country_at as fallback for creation date for {account.username}: {created_at}")
                                    except Exception as e:
                                        logger.error(f"Error converting country_at timestamp: {str(e)}")
                            
                            # Get creation region from region info
                            creation_region = "NA"  # Default for NA accounts
                            region_info = decoded_info.get('region', {})
                            if region_info and isinstance(region_info, dict):
                                region_tag = region_info.get('tag', '')
                                if region_tag:
                                    # Map region tag to region code
                                    region_tag_mapping = {
                                        'na': 'NA',
                                        'euw': 'EUW',
                                        'eune': 'EUNE',
                                        'br': 'BR',
                                        'lan': 'LAN',
                                        'las': 'LAS',
                                        'oce': 'OCE',
                                        'tr': 'TR',
                                        'ru': 'RU',
                                        'jp': 'JP',
                                        'kr': 'KR'
                                    }
                                    creation_region = region_tag_mapping.get(region_tag.lower(), creation_region)
                                    logger.info(f"Extracted creation region for {account.username}: {creation_region} from tag {region_tag}")
                            
                            # Update account object
                            account.summoner_level = summoner_level
                            account.game_name = game_name
                            account.tag_line = tag_line
                            account.country = country
                            account.last_checked = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            account.created_at = created_at
                            account.creation_region = creation_region
                            
                            # Try to get champion data
                            try:
                                # Get champion data
                                champion_data = loop.run_until_complete(account.authenticator.get_champion_data())
                                if champion_data and isinstance(champion_data, dict):
                                    # Update champion count
                                    champions = champion_data.get('champions', [])
                                    account.champions_owned = len(champions) if champions else 0
                                    logger.info(f"Updated champion count for {account.username}: {account.champions_owned}")
                                    
                                    # Save champions to database
                                    champions_to_save = []
                                    for champ in champions:
                                        if isinstance(champ, dict) and 'id' in champ and 'name' in champ:
                                            champions_to_save.append({
                                                'id': champ['id'],
                                                'name': champ['name'],
                                                'purchase_date': champ.get('purchase_date', '')
                                            })
                                    
                                    if champions_to_save:
                                        loop.run_until_complete(
                                            self.account_pool.db_manager.save_champions(
                                                account.username, champions_to_save
                                            )
                                        )
                                        logger.info(f"Saved {len(champions_to_save)} champions for {account.username}")
                            except Exception as e:
                                logger.error(f"Error getting champion data for {account.username}: {str(e)}")
                            
                            # Try to get wallet data
                            try:
                                wallet_data = loop.run_until_complete(account.authenticator.get_wallet())
                                if wallet_data and isinstance(wallet_data, dict):
                                    # Update blue essence and riot points
                                    account.blue_essence = wallet_data.get('lol_blue_essence', 0)
                                    account.riot_points = wallet_data.get('riot_points', 0)
                                    logger.info(f"Updated wallet for {account.username}: BE={account.blue_essence}, RP={account.riot_points}")
                            except Exception as e:
                                logger.error(f"Error getting wallet data for {account.username}: {str(e)}")
                            
                            # Extract userinfo data from decoded_info
                            game_name = decoded_info.get('acct', {}).get('game_name', account.username)
                            tag_line = decoded_info.get('acct', {}).get('tag_line', '')
                            summoner_level = decoded_info.get('lol_account', {}).get('summoner_level', 0)
                            country = decoded_info.get('country', '')
                            
                            # Save to database
                            loop.run_until_complete(
                                self.account_pool.db_manager.save_account_info(
                                    account.username,
                                    account.region,
                                    {
                                        'summoner_level': summoner_level,
                                        'game_name': game_name,
                                        'tag_line': tag_line,
                                        'country': country,
                                        'last_checked': account.last_checked,
                                        'region': account.region,
                                        'created_at': created_at,
                                        'creation_region': creation_region,
                                        'blue_essence': account.blue_essence,
                                        'riot_points': account.riot_points,
                                        'champion_count': account.champions_owned,
                                        'solo_tier': account.rank_info.get('solo', {}).get('tier', 'UNRANKED'),
                                        'solo_division': account.rank_info.get('solo', {}).get('division', ''),
                                        'solo_lp': account.rank_info.get('solo', {}).get('lp', 0),
                                        'solo_wins': account.rank_info.get('solo', {}).get('wins', 0),
                                        'solo_losses': account.rank_info.get('solo', {}).get('losses', 0),
                                        'solo_previous_tier': account.rank_info.get('solo', {}).get('previous_tier', 'UNRANKED'),
                                        'solo_previous_division': account.rank_info.get('solo', {}).get('previous_division', ''),
                                        'flex_tier': account.rank_info.get('flex', {}).get('tier', 'UNRANKED'),
                                        'flex_division': account.rank_info.get('flex', {}).get('division', ''),
                                        'flex_lp': account.rank_info.get('flex', {}).get('lp', 0),
                                        'flex_wins': account.rank_info.get('flex', {}).get('wins', 0),
                                        'flex_losses': account.rank_info.get('flex', {}).get('losses', 0),
                                        'flex_previous_tier': account.rank_info.get('flex', {}).get('previous_tier', 'UNRANKED'),
                                        'flex_previous_division': account.rank_info.get('flex', {}).get('previous_division', ''),
                                        'penalty_minutes': account.penalty_minutes,
                                        'ranked_games_remaining': account.ranked_games_remaining
                                    }
                                )
                            )
                            
                            logger.info(f"Successfully saved comprehensive info for NA account {account.username} (fallback method)")
                    except Exception as e:
                        logger.error(f"Error in fallback handling for NA account {account.username}: {str(e)}")
                finally:
                    loop.close()
            self.finished.emit()
        except Exception as e:
            self.error.emit(str(e))
            
    def auth_complete(self, success: bool):
        self.auth_done = success
        
    def stop(self):
        self.running = False

class AccountSelectionWorker(QThread):
    finished = pyqtSignal(str, list, list, list)  # Added list for skins
    
    def __init__(self, account, db_manager):
        super().__init__()
        self.account = account
        self.db_manager = db_manager
        
    def run(self):
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                # Get champion data
                first_champions = loop.run_until_complete(
                    self.db_manager.get_first_champions(self.account.username, 5)
                )
                all_champions = loop.run_until_complete(
                    self.db_manager.get_owned_champions(self.account.username)
                )
                
                # Get skin data
                owned_skins = loop.run_until_complete(
                    self.db_manager.get_owned_skins(self.account.username)
                )

                # Format ban info if account is banned
                if self.account.is_banned:
                    details = f"""Username: {self.account.username}
IGN: {self.account.game_name}#{self.account.tag_line if self.account.tag_line else ''}
Region: {self.account.region}
Country: {self.account.country.upper() if self.account.country else 'Unknown'}
Creation Region: {self.account.creation_region.upper() if self.account.creation_region else 'Unknown'}
Account Created: {self.account.created_at}
Status: BANNED
Ban Details:
{self.account.ban_info}
Last Checked: {self.account.last_checked}
"""
                    self.finished.emit(details, [], [], [])
                    return

                # Format champions text
                champions_text = "\nFirst Champions Purchased:"
                if first_champions:
                    for i, champ in enumerate(first_champions, 1):
                        purchase_date = self.format_date(champ['purchase_date'])
                        champions_text += f"\n{i}. {champ['champion_name']} ({purchase_date})"
                else:
                    champions_text += "\nNo champions owned"
                
                # Format penalty info
                penalty_text = ""
                if self.account.penalty_minutes > 0:
                    penalty_text = f"\nLow Priority Queue: {self.account.penalty_minutes} minutes"
                if self.account.ranked_games_remaining > 0:
                    penalty_text += f"\nRanked Restriction: {self.account.ranked_games_remaining} games remaining"
                if not penalty_text:
                    penalty_text = "\nNo Penalties"
                
                # Format rank info
                rank_text = "\nRanked Info:"
                
                # Solo/Duo Queue
                solo = self.account.rank_info.get('solo', {})
                if solo['tier'] != "UNRANKED":
                    rank_text += f"\nSolo/Duo: {solo['tier']} {solo['division']} ({solo['lp']} LP)"
                    rank_text += f"\nWin/Loss: {solo['wins']}/{solo['losses']}"
                    if solo['previous_tier'] != "UNRANKED":
                        rank_text += f"\nPrevious Season: {solo['previous_tier']} {solo['previous_division']}"
                else:
                    rank_text += "\nSolo/Duo: Unranked"
                    if solo['previous_tier'] != "UNRANKED":
                        rank_text += f" (Previous: {solo['previous_tier']} {solo['previous_division']})"
                
                # Flex Queue
                flex = self.account.rank_info.get('flex', {})
                if flex['tier'] != "UNRANKED":
                    rank_text += f"\n\nFlex: {flex['tier']} {flex['division']} ({flex['lp']} LP)"
                    rank_text += f"\nWin/Loss: {flex['wins']}/{flex['losses']}"
                    if flex['previous_tier'] != "UNRANKED":
                        rank_text += f"\nPrevious Season: {flex['previous_tier']} {flex['previous_division']}"
                else:
                    rank_text += "\nFlex: Unranked"
                    if flex['previous_tier'] != "UNRANKED":
                        rank_text += f" (Previous: {flex['previous_tier']} {flex['previous_division']})"
                
                # Season History
                season_history = self.account.rank_info.get('season_history', [])
                if season_history:
                    rank_text += "\n\nSeason History:"
                    for season in season_history:
                        season_num = season['season']
                        split_text = f" (Split {season['split']})" if season['split'] else ""
                        peak_tier = season['peak_tier']
                        peak_division = season['peak_division']
                        end_tier = season['end_tier']
                        end_division = season['end_division']
                        
                        rank_text += f"\nS{season_num}{split_text}: {peak_tier} {peak_division}"
                        if end_tier:
                            rank_text += f" → {end_tier} {end_division} (end of season)"
                
                # Create details text
                details = f"""Username: {self.account.username}
IGN: {self.account.game_name}#{self.account.tag_line if self.account.tag_line else ''}
Region: {self.account.region}
Country: {self.account.country.upper() if self.account.country else 'Unknown'}
Creation Region: {self.account.creation_region.upper() if self.account.creation_region else 'Unknown'}
Account Created: {self.account.created_at}
Summoner Level: {self.account.summoner_level}
Blue Essence: {self.account.blue_essence:,}
Riot Points: {self.account.riot_points}{rank_text}
Champions Owned: {self.account.champions_owned}{penalty_text}
Last Checked: {self.account.last_checked}
{champions_text}
"""
                self.finished.emit(details, first_champions, all_champions, owned_skins)
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"Error in account selection worker: {str(e)}")

    def update_account_details(self, details_text, first_champions, all_champions, owned_skins):
        """Update the account details display"""
        self.details_text.setText(details_text)
        
        # Update champions list (combined list instead of separate first/all champions)
        self.champions_list.clear()
        
        # Helper function to format champion display
        def format_champion(champ):
            if isinstance(champ, dict):
                if 'champion_name' in champ and 'purchase_date' in champ:
                    formatted_date = self.format_date(champ['purchase_date'])
                    return f"{champ['champion_name']} (purchased: {formatted_date})"
                elif 'name' in champ and 'id' in champ:
                    return f"{champ['name']} ({champ['id']})"
                elif 'championId' in champ:  # Alternative format from recheck
                    return f"Champion {champ['championId']}"
                else:
                    return str(champ)
            else:
                return str(champ)
                
        # Add first champions at the top if available
        if first_champions:
            for champ in first_champions:
                self.champions_list.addItem(format_champion(champ))
            
        # Add all champions
        if all_champions:
            for champ in all_champions:
                self.champions_list.addItem(format_champion(champ))
        
        # Update skins list and chromas list
        self.skins_list.clear()
        self.chromas_list.clear()
        
        # Group skins by base skin ID to avoid duplicates
        base_skins = {}
        chromas = []
        
        if owned_skins:
            for skin in owned_skins:
                if isinstance(skin, dict):
                    # Check if it's a chroma
                    is_chroma = False
                    if 'skin_name' in skin:
                        skin_name = skin.get('skin_name', '')
                        if '(' in skin_name and (')' in skin_name or 'Chroma' in skin_name):
                            # This is likely a chroma
                            is_chroma = True
                    
                    if is_chroma:
                        # Add to chromas list
                        chromas.append(skin)
                    else:
                        # Add to base skins, using skin_id as key to avoid duplicates
                        skin_id = skin.get('skin_id', skin.get('skinId', 'Unknown'))
                        base_skins[str(skin_id)] = skin
                else:
                    # If it's not a dict, just add it to the skins list
                    self.skins_list.addItem(f"Skin {skin}")
        
        # Display base skins
        for skin_id, skin in base_skins.items():
            if 'champion_name' in skin:
                # Get the skin name and purchase date
                skin_name = skin.get('skin_name', skin.get('name', ''))
                purchase_date = self.format_date(skin.get('purchase_date', 'Unknown'))
                if skin_name:
                    # Remove any chroma information from the skin name
                    if '(' in skin_name:
                        skin_name = skin_name.split('(')[0].strip()
                    self.skins_list.addItem(f"{skin['champion_name']} - {skin_name} (purchased: {purchase_date})")
                else:
                    # Fallback to skin ID if no name is found
                    self.skins_list.addItem(f"Skin {skin_id} (purchased: {purchase_date})")
        
        # Display chromas
        for chroma in chromas:
            if 'champion_name' in chroma:
                # Get the skin name and purchase date
                skin_name = chroma.get('skin_name', chroma.get('name', ''))
                purchase_date = self.format_date(chroma.get('purchase_date', 'Unknown'))
                if skin_name:
                    self.chromas_list.addItem(f"{chroma['champion_name']} - {skin_name} (purchased: {purchase_date})")
                else:
                    # Fallback to skin ID if no name is found
                    skin_id = chroma.get('skin_id', chroma.get('skinId', 'Unknown'))
                    self.chromas_list.addItem(f"Skin {skin_id} (purchased: {purchase_date})")

    def format_date(self, date_str):
        """Format date string from YYYYMMDDTHHMMSS.000Z to YYYY-MM-DD HH:MM:SS"""
        if not date_str or date_str == 'Unknown':
            return 'Unknown'
        try:
            if 'T' in date_str:
                # Handle format like "20211207T114524.000Z"
                date_str = date_str.split('.')[0]  # Remove milliseconds
                dt = datetime.strptime(date_str, '%Y%m%dT%H%M%S')
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            return date_str
        except:
            return date_str

class CopyProfileDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Copy Profile Settings")
        self.setMinimumWidth(400)
        self.profiles = {}
        self.load_settings()
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # Profile selection/creation
        profile_group = QGroupBox("Profile")
        profile_layout = QHBoxLayout()
        self.profile_combo = QComboBox()
        self.profile_combo.addItems(self.profiles.keys())
        self.profile_combo.currentTextChanged.connect(self.load_profile)
        self.profile_name = QLineEdit()
        profile_layout.addWidget(self.profile_combo)
        profile_layout.addWidget(self.profile_name)
        profile_group.setLayout(profile_layout)
        layout.addWidget(profile_group)

        # Scrollable area for checkboxes
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll_widget = QWidget()
        self.checkbox_layout = QVBoxLayout(scroll_widget)

        # Add checkboxes for each field
        self.checkboxes = {}
        fields = [
            "User:Pass", "Username", "IGN", "Region", "Country", "Creation Region",
            "Account Created", "Summoner Level", "Blue Essence", "Riot Points",
            "Ranked Info", "Champions Owned", "Penalties", "Last Checked",
            "First Champions"
        ]
        
        for field in fields:
            cb = QCheckBox(field)
            self.checkboxes[field] = cb
            self.checkbox_layout.addWidget(cb)

        scroll.setWidget(scroll_widget)
        layout.addWidget(scroll)

        # Buttons
        button_layout = QHBoxLayout()
        save_button = QPushButton("Save Profile")
        save_button.clicked.connect(self.save_profile)
        delete_button = QPushButton("Delete Profile")
        delete_button.clicked.connect(self.delete_profile)
        ok_button = QPushButton("OK")
        ok_button.clicked.connect(self.accept)
        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(save_button)
        button_layout.addWidget(delete_button)
        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)

    def load_settings(self):
        try:
            if os.path.exists('settings.json'):
                with open('settings.json', 'r') as f:
                    self.profiles = json.load(f)
            else:
                # Create default profile with all fields enabled
                self.profiles = {
                    "Default": {
                        "User:Pass": True,
                        "Username": True,
                        "IGN": True,
                        "Region": True,
                        "Country": True,
                        "Creation Region": True,
                        "Account Created": True,
                        "Summoner Level": True,
                        "Blue Essence": True,
                        "Riot Points": True,
                        "Ranked Info": True,
                        "Champions Owned": True,
                        "Penalties": True,
                        "Last Checked": True,
                        "First Champions": True
                    }
                }
                self.save_settings()
        except Exception as e:
            logger.error(f"Error loading settings: {str(e)}")
            self.profiles = {}

    def save_settings(self):
        try:
            with open('settings.json', 'w') as f:
                json.dump(self.profiles, f, indent=4)
        except Exception as e:
            logger.error(f"Error saving settings: {str(e)}")

    def load_profile(self, profile_name):
        if profile_name in self.profiles:
            profile = self.profiles[profile_name]
            self.profile_name.setText(profile_name)
            for field, checked in profile.items():
                if field in self.checkboxes:
                    self.checkboxes[field].setChecked(checked)

    def save_profile(self):
        name = self.profile_name.text().strip()
        if name:
            self.profiles[name] = {
                field: cb.isChecked()
                for field, cb in self.checkboxes.items()
            }
            if name not in [self.profile_combo.itemText(i) for i in range(self.profile_combo.count())]:
                self.profile_combo.addItem(name)
            self.save_settings()
            QMessageBox.information(self, "Success", f"Profile '{name}' saved successfully")

    def delete_profile(self):
        name = self.profile_combo.currentText()
        if name == "Default":
            QMessageBox.warning(self, "Warning", "Cannot delete default profile")
            return
        if name in self.profiles:
            del self.profiles[name]
            self.profile_combo.removeItem(self.profile_combo.currentIndex())
            self.save_settings()
            QMessageBox.information(self, "Success", f"Profile '{name}' deleted successfully")

class RegionSelectionDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Select Region")
        self.setMinimumWidth(200)
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Region selection combo box
        self.region_combo = QComboBox()
        self.region_combo.addItems(['EUW', 'EUNE', 'NA', 'OCE', 'LAN', 'LAS', 'BR', 'TR', 'RU', 'JP', 'KR'])
        layout.addWidget(self.region_combo)
        
        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
    def get_selected_region(self):
        return self.region_combo.currentText()

class AccountManagerGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("League Account Manager")
        self.setGeometry(100, 100, 800, 600)
        
        # Initialize account pool and auth handler
        self.account_pool = AccountPool()
        self.auth_handler = AuthHandler()
        
        # Create menu bar
        self.create_menu_bar()
        
        # Setup UI
        self.setup_ui()
        
        # Load accounts
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.initial_load())
        finally:
            loop.close()
            
    def create_menu_bar(self):
        menubar = self.menuBar()
        file_menu = menubar.addMenu('File')
        
        # Import accounts from text file
        import_action = file_menu.addAction('Select txt file user:pass')
        import_action.triggered.connect(self.import_accounts_wrapper)
        
        # Save database
        save_action = file_menu.addAction('Save Database')
        save_action.triggered.connect(self.save_database)
        
        # Load database
        load_action = file_menu.addAction('Load Database')
        load_action.triggered.connect(self.load_database)
        
        # Add separator
        file_menu.addSeparator()
        
        # Settings
        settings_action = file_menu.addAction('Settings')
        settings_action.triggered.connect(self.show_settings)
        
        file_menu.addSeparator()
        
        # Clear all accounts
        clear_action = file_menu.addAction('Clear All Accounts')
        clear_action.triggered.connect(self.clear_accounts_wrapper)
        
        # Delete selected account
        delete_action = file_menu.addAction('Delete Selected Account')
        delete_action.triggered.connect(self.delete_selected_account_wrapper)
        
        # Add Export menu
        export_menu = menubar.addMenu('Export')
        
        # Copy all accounts info
        copy_all_action = export_menu.addAction('Copy All Accs Info')
        copy_all_action.triggered.connect(lambda: self.copy_all_accounts_info(include_skins=True, include_chromas=True))
        
        # Copy all accounts info without chromas
        copy_no_chromas_action = export_menu.addAction('Copy All Accs Info (without Chromas)')
        copy_no_chromas_action.triggered.connect(lambda: self.copy_all_accounts_info(include_skins=True, include_chromas=False))
        
        # Copy all accounts info without skins and chromas
        copy_no_skins_action = export_menu.addAction('Copy All Accs Info (without Skins and Chromas)')
        copy_no_skins_action.triggered.connect(lambda: self.copy_all_accounts_info(include_skins=False, include_chromas=False))

    async def import_accounts_from_txt(self):
        try:
            file_name, _ = QFileDialog.getOpenFileName(self, "Select Accounts File", "", "Text Files (*.txt)")
            if file_name:
                with open(file_name, 'r') as file:
                    accounts = []
                    for line in file:
                        line = line.strip()
                        if ':' in line:
                            username, password = line.split(':', 1)
                            accounts.append({
                                'username': username.strip(),
                                'password': password.strip()
                            })
                    
                    # Save accounts to database without region (it will be detected during auth)
                    for acc in accounts:
                        await self.account_pool.db_manager.update_account(
                            username=acc['username'],
                            password=acc['password'],
                            refresh_token='',
                            region=''  # Leave region empty, it will be detected during auth
                        )
                    
                    # Reload accounts
                    await self.account_pool._load_accounts()
                    self.load_accounts()
                    
                    QMessageBox.information(self, "Success", f"Imported {len(accounts)} accounts. Regions will be detected during authentication.")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to import accounts: {str(e)}")

    def import_accounts_wrapper(self):
        """Wrapper to handle async import_accounts_from_txt"""
        try:
            # Create a new event loop for this specific operation
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Run the coroutine in the event loop
            loop.run_until_complete(self.import_accounts_from_txt())
            
            # Close the loop when done
            loop.close()
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to import accounts: {str(e)}")

    def save_database(self):
        try:
            file_name, _ = QFileDialog.getSaveFileName(self, "Save Database", "", "Database Files (*.db)")
            if file_name:
                import shutil
                shutil.copy2('accounts.db', file_name)
                QMessageBox.information(self, "Success", "Database saved successfully")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save database: {str(e)}")

    async def load_database_async(self):
        try:
            file_name, _ = QFileDialog.getOpenFileName(self, "Load Database", "", "Database Files (*.db)")
            if file_name:
                import shutil
                shutil.copy2(file_name, 'accounts.db')
                
                # Reload accounts
                await self.account_pool._load_accounts()
                self.load_accounts()
                QMessageBox.information(self, "Success", "Database loaded successfully")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load database: {str(e)}")

    def load_database(self):
        """Wrapper to handle async load_database"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.load_database_async())
        finally:
            loop.close()

    def show_settings(self):
        dialog = CopyProfileDialog(self)
        dialog.exec()

    def setup_ui(self):
        # Set dark theme colors
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
            }
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QPushButton {
                background-color: #6272a4;
                color: white;
                border: none;
                padding: 5px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #7282b4;
            }
            QPushButton:pressed {
                background-color: #526294;
            }
            QListWidget {
                background-color: #1e1e1e;
                color: white;
                border: 1px solid #404040;
            }
            QTextEdit {
                background-color: #1e1e1e;
                color: white;
                border: 1px solid #404040;
            }
            QTabWidget::pane {
                border: 1px solid #404040;
            }
            QTabBar::tab {
                background-color: #6272a4;
                color: white;
                padding: 8px 20px;
                border: none;
            }
            QTabBar::tab:hover {
                background-color: #7282b4;
            }
            QTabBar::tab:selected {
                background-color: #526294;
            }
            QProgressBar {
                border: 1px solid #404040;
                background-color: #1e1e1e;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #6272a4;
            }
        """)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Top section container
        top_container = QWidget()
        top_layout = QVBoxLayout(top_container)
        top_layout.setContentsMargins(0, 0, 0, 20)  # Add space below top section
        
        # Status label at the very top
        self.progress_label = QLabel("Ready")
        self.progress_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        top_layout.addWidget(self.progress_label)
        
        # Button and progress bar row
        controls_layout = QHBoxLayout()
        controls_layout.setContentsMargins(0, 0, 0, 0)
        
        # Recheck button
        self.recheck_button = QPushButton("Recheck All")
        self.recheck_button.setFixedSize(100, 25)
        self.recheck_button.clicked.connect(self.start_recheck)
        controls_layout.addWidget(self.recheck_button)
        
        # Add spacing between button and progress bar
        controls_layout.addSpacing(20)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(25)
        self.progress_bar.setTextVisible(False)
        controls_layout.addWidget(self.progress_bar, stretch=1)
        
        top_layout.addLayout(controls_layout)
        layout.addWidget(top_container)
        
        # Split view
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Account list
        self.account_list = QListWidget()
        self.account_list.currentRowChanged.connect(self.on_account_select)
        splitter.addWidget(self.account_list)
        
        # Details section with tabs
        self.tab_widget = QTabWidget()
        
        # General tab
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        self.tab_widget.addTab(self.details_text, "General")
        
        # Champions tab
        self.champions_list = QListWidget()
        self.tab_widget.addTab(self.champions_list, "Champions")

        # Skins tab
        self.skins_list = QListWidget()
        self.tab_widget.addTab(self.skins_list, "Skins")
        
        # Chromas tab
        self.chromas_list = QListWidget()
        self.tab_widget.addTab(self.chromas_list, "Chromas")
        
        splitter.addWidget(self.tab_widget)
        layout.addWidget(splitter)
        
        # Add context menu to account list
        self.account_list.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.account_list.customContextMenuRequested.connect(self.show_context_menu)

    async def initial_load(self):
        await self.account_pool.db_manager.init_db()
        await self.account_pool._load_accounts()
        self.load_accounts()
        
    def load_accounts(self):
        self.account_list.clear()
        accounts = self.account_pool.get_all_accounts()
        for account in accounts:
            # Add banned status to display if account is banned
            banned_text = " [BANNED]" if account.is_banned else ""
            self.account_list.addItem(f"{account.username} ({account.region}){banned_text}")
            
    def on_account_select(self, row):
        if row < 0:
            return
            
        accounts = self.account_pool.get_all_accounts()
        account = accounts[row]
        
        # Create and start worker
        self.selection_worker = AccountSelectionWorker(account, self.account_pool.db_manager)
        self.selection_worker.finished.connect(self.update_account_details)
        self.selection_worker.start()
    
    def update_account_details(self, details_text, first_champions, all_champions, owned_skins):
        """Update the account details display"""
        self.details_text.setText(details_text)
        
        # Update champions list (combined list instead of separate first/all champions)
        self.champions_list.clear()
        
        # Helper function to format champion display
        def format_champion(champ):
            if isinstance(champ, dict):
                if 'champion_name' in champ and 'purchase_date' in champ:
                    formatted_date = self.format_date(champ['purchase_date'])
                    return f"{champ['champion_name']} (purchased: {formatted_date})"
                elif 'name' in champ and 'id' in champ:
                    return f"{champ['name']} ({champ['id']})"
                elif 'championId' in champ:  # Alternative format from recheck
                    return f"Champion {champ['championId']}"
                else:
                    return str(champ)
            else:
                return str(champ)
                
        # Add first champions at the top if available
        if first_champions:
            for champ in first_champions:
                self.champions_list.addItem(format_champion(champ))
            
        # Add all champions
        if all_champions:
            for champ in all_champions:
                self.champions_list.addItem(format_champion(champ))
        
        # Update skins list and chromas list
        self.skins_list.clear()
        self.chromas_list.clear()
        
        # Group skins by base skin ID to avoid duplicates
        base_skins = {}
        chromas = []
        
        if owned_skins:
            for skin in owned_skins:
                if isinstance(skin, dict):
                    # Check if it's a chroma
                    is_chroma = False
                    if 'skin_name' in skin:
                        skin_name = skin.get('skin_name', '')
                        if '(' in skin_name and (')' in skin_name or 'Chroma' in skin_name):
                            # This is likely a chroma
                            is_chroma = True
                    
                    if is_chroma:
                        # Add to chromas list
                        chromas.append(skin)
                    else:
                        # Add to base skins, using skin_id as key to avoid duplicates
                        skin_id = skin.get('skin_id', skin.get('skinId', 'Unknown'))
                        base_skins[str(skin_id)] = skin
                else:
                    # If it's not a dict, just add it to the skins list
                    self.skins_list.addItem(f"Skin {skin}")
        
        # Display base skins
        for skin_id, skin in base_skins.items():
            if 'champion_name' in skin:
                # Get the skin name and purchase date
                skin_name = skin.get('skin_name', skin.get('name', ''))
                purchase_date = self.format_date(skin.get('purchase_date', 'Unknown'))
                if skin_name:
                    # Remove any chroma information from the skin name
                    if '(' in skin_name:
                        skin_name = skin_name.split('(')[0].strip()
                    self.skins_list.addItem(f"{skin['champion_name']} - {skin_name} (purchased: {purchase_date})")
                else:
                    # Fallback to skin ID if no name is found
                    self.skins_list.addItem(f"Skin {skin_id} (purchased: {purchase_date})")
        
        # Display chromas
        for chroma in chromas:
            if 'champion_name' in chroma:
                # Get the skin name and purchase date
                skin_name = chroma.get('skin_name', chroma.get('name', ''))
                purchase_date = self.format_date(chroma.get('purchase_date', 'Unknown'))
                if skin_name:
                    self.chromas_list.addItem(f"{chroma['champion_name']} - {skin_name} (purchased: {purchase_date})")
                else:
                    # Fallback to skin ID if no name is found
                    skin_id = chroma.get('skin_id', chroma.get('skinId', 'Unknown'))
                    self.chromas_list.addItem(f"Skin {skin_id} (purchased: {purchase_date})")

    def start_recheck(self):
        self.recheck_button.setEnabled(False)
        self.worker = AsyncWorker(self.account_pool)
        self.worker.progress.connect(self.update_progress)
        self.worker.finished.connect(self.recheck_finished)
        self.worker.error.connect(self.handle_error)
        self.worker.auth_needed.connect(self.handle_auth)
        self.worker.start()
        
    def update_progress(self, current, total):
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)
        self.progress_label.setText(f"Checking accounts... {current}/{total}")
        
    def recheck_finished(self):
        self.progress_label.setText("Done checking accounts")
        self.recheck_button.setEnabled(True)
        self.load_accounts()

    def handle_error(self, error_msg):
        self.progress_label.setText(f"Error: {error_msg}")
        self.recheck_button.setEnabled(True)

    def handle_auth(self, username, password):
        """Handle authentication in main thread"""
        try:
            # Create session and get tokens
            session = create_session(verify_ssl=False)
            
            # Process events to keep GUI responsive
            QApplication.instance().processEvents()
            
            # Get existing account info to check for refresh token
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                account_info = loop.run_until_complete(
                    self.account_pool.db_manager.get_account(username)
                )
                
                # Find account object
                target_account = None
                for account in self.account_pool._accounts:
                    if account.username == username:
                        target_account = account
                        break
                
                if not target_account:
                    raise Exception(f"Account {username} not found in memory")
                
                # Try to use refresh token first
                refresh_token = account_info.get('refresh_token') if account_info else None
                
                # Get tokens
                tokens = loop.run_until_complete(
                    get_auth_tokens(username, password, session, refresh_token)
                )
                
                # Process events again
                QApplication.instance().processEvents()
                
                if tokens and isinstance(tokens, tuple):
                    access_token, id_token, refresh_token = tokens
                    
                    # Try to get region from JWT token
                    region = 'EUW'  # Default region
                    try:
                        import jwt
                        decoded = jwt.decode(access_token, options={"verify_signature": False})
                        logger.debug(f"JWT token contents: {decoded}")
                        
                        # Extract region from dat.r field
                        if 'dat' in decoded and isinstance(decoded['dat'], dict):
                            dat = decoded['dat']
                            if 'r' in dat and dat['r']:
                                region_code = str(dat['r']).strip()
                                # Map region codes to regions
                                region_mapping = {
                                    'EUW1': 'EUW',
                                    'NA1': 'NA',
                                    'EUN1': 'EUNE',
                                    'LA1': 'LAN',
                                    'LA2': 'LAS',
                                    'BR1': 'BR',
                                    'TR1': 'TR',
                                    'RU': 'RU',
                                    'JP1': 'JP',
                                    'KR': 'KR',
                                    'OC1': 'OCE'
                                }
                                region = region_mapping.get(region_code, 'EUW')
                                logger.debug(f"Found region code {region_code}, mapped to {region}")
                            
                    except Exception as e:
                        logger.debug(f"Could not extract region from token: {str(e)}")
                        region = 'EUW'
                    
                    logger.debug(f"Final region for {username}: {region}")
                    
                    # Ensure region is uppercase
                    region = region.upper()
                    
                    # Update account with tokens and region
                    loop.run_until_complete(
                        self.account_pool.db_manager.update_account(
                            username=username,
                            password=password,
                            refresh_token=refresh_token,
                            access_token=access_token,
                            id_token=id_token,
                            region=region
                        )
                    )
                    
                    # Set region and initialize authenticator
                    target_account.region = region
                    if hasattr(target_account, '_region'):
                        target_account._region = region
                    if hasattr(target_account, 'set_region'):
                        target_account.set_region(region)
                        
                    # Clear and reinitialize authenticator
                    target_account.authenticator = None
                    
                    # Set region before initializing authenticator
                    if hasattr(target_account, 'get_region'):
                        logger.debug(f"Region before init_authenticator: {target_account.get_region()}")
                    else:
                        logger.debug(f"Region before init_authenticator: {target_account.region}")
                    
                    # Initialize authenticator
                    target_account.init_authenticator()
                    
                    # Double-check region is set after initialization
                    if not hasattr(target_account, 'region') or not target_account.region:
                        target_account.region = region
                    
                    # Ensure authenticator has the region set
                    if target_account.authenticator:
                        if hasattr(target_account.authenticator, 'set_region'):
                            target_account.authenticator.set_region(region)
                        target_account.authenticator.region = region
                        if hasattr(target_account.authenticator, '_region'):
                            target_account.authenticator._region = region
                        target_account.authenticator.set_tokens(access_token, id_token)
                        logger.debug(f"Successfully set tokens for {username} with region {region}")
                        
                        # Double-check authenticator region
                        if not hasattr(target_account.authenticator, 'region') or not target_account.authenticator.region:
                            target_account.authenticator.region = region
                    else:
                        raise Exception("Failed to initialize authenticator")
                    
                    # Log final state
                    logger.debug(f"Final account state - Account region: {target_account.region}")
                    logger.debug(f"Final account state - Account _region: {target_account._region if hasattr(target_account, '_region') else 'N/A'}")
                    logger.debug(f"Final account state - Authenticator region: {target_account.authenticator.region if target_account.authenticator else 'None'}")
                    
                    # Update all instances of this account in the pool to ensure region consistency
                    for account in self.account_pool._accounts:
                        if account.username == username:
                            account.region = region
                            if hasattr(account, '_region'):
                                account._region = region
                            if hasattr(account, 'authenticator') and account.authenticator:
                                account.authenticator.region = region
                                if hasattr(account.authenticator, '_region'):
                                    account.authenticator._region = region
                    
                    self.worker.auth_complete(True)
                else:
                    self.worker.auth_complete(False)
            finally:
                loop.close()
                
        except Exception as e:
            logger.error(f"Auth error for {username}: {str(e)}")
            self.worker.auth_complete(False)

    def closeEvent(self, event):
        if hasattr(self, 'worker') and self.worker.isRunning():
            self.worker.stop()
            self.worker.wait()
        event.accept()

    async def clear_accounts(self):
        """Clear all accounts from database and GUI"""
        try:
            # Confirm with user
            reply = QMessageBox.question(self, 'Confirm Clear',
                                       'Are you sure you want to clear all accounts? This cannot be undone.',
                                       QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            
            if reply == QMessageBox.StandardButton.Yes:
                # Clear database
                await self.account_pool.db_manager.clear_all_accounts()
                
                # Clear accounts from memory
                await self.account_pool._load_accounts()
                
                # Update GUI
                self.load_accounts()
                self.details_text.clear()
                self.champions_list.clear()
                self.skins_list.clear()
                self.chromas_list.clear()
                
                QMessageBox.information(self, "Success", "All accounts cleared successfully")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to clear accounts: {str(e)}")

    def clear_accounts_wrapper(self):
        """Wrapper to handle async clear_accounts"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.clear_accounts())
        finally:
            loop.close()

    async def delete_selected_account(self):
        """Delete the currently selected account"""
        try:
            current_row = self.account_list.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "No Selection", "Please select an account to delete.")
                return
            
            # Get the selected account
            accounts = self.account_pool.get_all_accounts()
            if current_row >= len(accounts):
                QMessageBox.warning(self, "Error", "Invalid account selection.")
                return
            
            selected_account = accounts[current_row]
            
            # Confirm with user
            reply = QMessageBox.question(self, 'Confirm Delete',
                                       f'Are you sure you want to delete account "{selected_account.username}"? This cannot be undone.',
                                       QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            
            if reply == QMessageBox.StandardButton.Yes:
                # Delete the account
                await self.account_pool.delete_account(selected_account.username)
                
                # Reload accounts and update GUI
                await self.account_pool._load_accounts()
                self.load_accounts()
                self.details_text.clear()
                self.champions_list.clear()
                self.skins_list.clear()
                self.chromas_list.clear()
                
                QMessageBox.information(self, "Success", f"Account '{selected_account.username}' deleted successfully")
                
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to delete account: {str(e)}")

    def delete_selected_account_wrapper(self):
        """Wrapper to handle async delete_selected_account"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.delete_selected_account())
        finally:
            loop.close()

    def show_context_menu(self, position):
        menu = QMenu()
        
        # Add the existing copy menu with profiles
        copy_menu = menu.addMenu("Copy")
        try:
            with open('settings.json', 'r') as f:
                profiles = json.load(f)
                
            for profile_name in profiles:
                action = copy_menu.addAction(profile_name)
                action.triggered.connect(lambda checked, p=profile_name: self.copy_account_info(p))
        except Exception as e:
            logger.error(f"Error loading profiles: {str(e)}")

        # Add separator
        menu.addSeparator()
        
        # Add new copy options
        champions_action = menu.addAction("Champions")
        champions_action.triggered.connect(lambda: self.copy_champions_info())
        
        skins_action = menu.addAction("Skins")
        skins_action.triggered.connect(lambda: self.copy_skins_info())
        
        both_action = menu.addAction("Champs and Skins")
        both_action.triggered.connect(lambda: self.copy_champions_and_skins_info())
        
        # Add separator
        menu.addSeparator()
        
        # Add delete option
        delete_action = menu.addAction("Delete Account")
        delete_action.triggered.connect(self.delete_selected_account_wrapper)
        
        menu.exec(self.account_list.mapToGlobal(position))

    def copy_champions_info(self):
        """Copy only champions information to clipboard"""
        current_row = self.account_list.currentRow()
        if current_row < 0:
            return
            
        account = self.account_pool.get_all_accounts()[current_row]
        
        # Get champions data
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            champions = loop.run_until_complete(
                self.account_pool.db_manager.get_owned_champions(account.username)
            )
            
            if champions:
                text_parts = ["Champions:"]
                for champ in sorted(champions, key=lambda x: x['champion_name']):
                    formatted_date = self.format_date(champ['purchase_date'])
                    text_parts.append(f"{champ['champion_name']} (purchased: {formatted_date})")
                
                clipboard = QApplication.clipboard()
                clipboard.setText("\n".join(text_parts))
        finally:
            loop.close()

    def copy_skins_info(self):
        """Copy only skins information to clipboard"""
        current_row = self.account_list.currentRow()
        if current_row < 0:
            return
            
        account = self.account_pool.get_all_accounts()[current_row]
        
        # Get skins data
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            skins = loop.run_until_complete(
                self.account_pool.db_manager.get_owned_skins(account.username)
            )
            
            if skins:
                text_parts = ["Skins:"]
                for skin in sorted(skins, key=lambda x: (x['champion_name'], x['skin_name'])):
                    purchase_date = self.format_date(skin.get('purchase_date', 'Unknown'))
                    text_parts.append(f"{skin['champion_name']} - {skin['skin_name']} (purchased: {purchase_date})")
                
                clipboard = QApplication.clipboard()
                clipboard.setText("\n".join(text_parts))
        finally:
            loop.close()

    def copy_champions_and_skins_info(self):
        """Copy both champions and skins information to clipboard"""
        current_row = self.account_list.currentRow()
        if current_row < 0:
            return
            
        account = self.account_pool.get_all_accounts()[current_row]
        
        # Get both champions and skins data
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            champions = loop.run_until_complete(
                self.account_pool.db_manager.get_owned_champions(account.username)
            )
            skins = loop.run_until_complete(
                self.account_pool.db_manager.get_owned_skins(account.username)
            )
            
            text_parts = []
            
            if champions:
                text_parts.append("Champions:")
                for champ in sorted(champions, key=lambda x: x['champion_name']):
                    formatted_date = self.format_date(champ['purchase_date'])
                    text_parts.append(f"{champ['champion_name']} (purchased: {formatted_date})")
            
            if skins:
                text_parts.append("\nSkins:")
                for skin in sorted(skins, key=lambda x: (x['champion_name'], x['skin_name'])):
                    purchase_date = self.format_date(skin.get('purchase_date', 'Unknown'))
                    text_parts.append(f"{skin['champion_name']} - {skin['skin_name']} (purchased: {purchase_date})")
            
            if text_parts:
                clipboard = QApplication.clipboard()
                clipboard.setText("\n".join(text_parts))
        finally:
            loop.close()

    def copy_account_info(self, profile_name):
        try:
            current_row = self.account_list.currentRow()
            if current_row < 0:
                return
                
            account = self.account_pool.get_all_accounts()[current_row]
            
            with open('settings.json', 'r') as f:
                profiles = json.load(f)
            
            if profile_name not in profiles:
                return
                
            profile = profiles[profile_name]
            text_parts = []
            
            field_mapping = {
                "User:Pass": lambda: f"{account.username}:{account.password}",
                "Username": lambda: f"Username: {account.username}",
                "IGN": lambda: f"IGN: {account.game_name}#{account.tag_line if account.tag_line else ''}",
                "Region": lambda: f"Region: {account.region}",
                "Country": lambda: f"Country: {account.country.upper() if account.country else 'Unknown'}",
                "Creation Region": lambda: f"Creation Region: {account.creation_region.upper() if account.creation_region else 'Unknown'}",
                "Account Created": lambda: f"Account Created: {account.created_at}",
                "Summoner Level": lambda: f"Summoner Level: {account.summoner_level}",
                "Blue Essence": lambda: f"Blue Essence: {account.blue_essence:,}",
                "Riot Points": lambda: f"Riot Points: {account.riot_points}",
                "Champions Owned": lambda: f"Champions Owned: {account.champions_owned}",
                "Last Checked": lambda: f"Last Checked: {account.last_checked}"
            }
            
            for field, checked in profile.items():
                if checked and field in field_mapping:
                    text_parts.append(field_mapping[field]())
            
            if profile.get("Ranked Info", False):
                rank_text = self.format_rank_info(account)
                if rank_text:
                    text_parts.append(rank_text)
            
            if profile.get("Penalties", False):
                penalties = self.format_penalties(account)
                if penalties:
                    text_parts.append(penalties)
            
            clipboard = QApplication.clipboard()
            clipboard.setText("\n".join(text_parts))
            
        except Exception as e:
            logger.error(f"Error copying account info: {str(e)}")

    def format_rank_info(self, account):
        parts = []
        solo = account.rank_info.get('solo', {})
        flex = account.rank_info.get('flex', {})
        
        if solo['tier'] != "UNRANKED" or solo['previous_tier'] != "UNRANKED":
            parts.append("Solo/Duo: " + (f"{solo['tier']} {solo['division']} ({solo['lp']} LP)" 
                        if solo['tier'] != "UNRANKED" else "Unranked"))
            if solo['previous_tier'] != "UNRANKED":
                parts.append(f"Previous: {solo['previous_tier']} {solo['previous_division']}")
        
        if flex['tier'] != "UNRANKED" or flex['previous_tier'] != "UNRANKED":
            parts.append("Flex: " + (f"{flex['tier']} {flex['division']} ({flex['lp']} LP)"
                        if flex['tier'] != "UNRANKED" else "Unranked"))
            if flex['previous_tier'] != "UNRANKED":
                parts.append(f"Previous: {flex['previous_tier']} {flex['previous_division']}")
        
        # Add season history
        season_history = account.rank_info.get('season_history', [])
        if season_history:
            parts.append("\nSeason History:")
            for season in season_history:
                season_num = season['season']
                split_text = f" (Split {season['split']})" if season['split'] else ""
                peak_tier = season['peak_tier']
                peak_division = season['peak_division']
                end_tier = season['end_tier']
                end_division = season['end_division']
                
                season_text = f"S{season_num}{split_text}: {peak_tier} {peak_division}"
                if end_tier:
                    season_text += f" → {end_tier} {end_division} (end of season)"
                parts.append(season_text)
        
        return "\n".join(parts) if parts else ""

    def format_penalties(self, account):
        parts = []
        if account.penalty_minutes > 0:
            parts.append(f"Low Priority Queue: {account.penalty_minutes} minutes")
        if account.ranked_games_remaining > 0:
            parts.append(f"Ranked Restriction: {account.ranked_games_remaining} games remaining")
        return "\n".join(parts) if parts else "No Penalties"

    def format_date(self, date_str):
        """Format date string from YYYYMMDDTHHMMSS.000Z to YYYY-MM-DD HH:MM:SS"""
        if not date_str or date_str == 'Unknown':
            return 'Unknown'
        try:
            if 'T' in date_str:
                # Handle format like "20211207T114524.000Z"
                date_str = date_str.split('.')[0]  # Remove milliseconds
                dt = datetime.strptime(date_str, '%Y%m%dT%H%M%S')
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            return date_str
        except:
            return date_str

    def copy_all_accounts_info(self, include_skins=True, include_chromas=True):
        """Copy information from all accounts to clipboard"""
        try:
            accounts = self.account_pool.get_all_accounts()
            if not accounts:
                self.progress_label.setText("No accounts to copy")
                return
                
            # Update status label
            self.progress_label.setText("Copying account information...")
                
            # Create a new event loop for async operations
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                all_text = []
                
                for account in accounts:
                    # Basic account info
                    account_text = [
                        f"Username: {account.username}",
                        f"Password: {account.password}",
                        f"IGN: {account.game_name}#{account.tag_line if account.tag_line else ''}",
                        f"Region: {account.region}",
                        f"Country: {account.country.upper() if account.country else 'Unknown'}",
                        f"Creation Region: {account.creation_region.upper() if account.creation_region else 'Unknown'}",
                        f"Account Created: {account.created_at}",
                        f"Summoner Level: {account.summoner_level}",
                        f"Blue Essence: {account.blue_essence:,}",
                        f"Riot Points: {account.riot_points}"
                    ]
                    
                    # Rank info
                    rank_text = self.format_rank_info(account)
                    if rank_text:
                        account_text.append(rank_text)
                    
                    # Champions info
                    account_text.append(f"Champions Owned: {account.champions_owned}")
                    
                    # Penalties
                    penalties = self.format_penalties(account)
                    if penalties and penalties != "No Penalties":
                        account_text.append(penalties)
                    
                    # Last checked
                    account_text.append(f"Last Checked: {account.last_checked}")
                    
                    # Get champions
                    champions = loop.run_until_complete(
                        self.account_pool.db_manager.get_owned_champions(account.username)
                    )
                    
                    if champions:
                        account_text.append("\nChampions:")
                        for champ in sorted(champions, key=lambda x: x['champion_name']):
                            formatted_date = self.format_date(champ['purchase_date'])
                            account_text.append(f"- {champ['champion_name']} (purchased: {formatted_date})")
                    
                    # Get skins if requested
                    if include_skins or include_chromas:
                        skins = loop.run_until_complete(
                            self.account_pool.db_manager.get_owned_skins(account.username)
                        )
                        
                        if skins:
                            # Group skins by base skin ID to avoid duplicates
                            base_skins = {}
                            chromas = []
                            
                            for skin in skins:
                                if isinstance(skin, dict):
                                    # Check if it's a chroma
                                    is_chroma = False
                                    if 'skin_name' in skin:
                                        skin_name = skin.get('skin_name', '')
                                        if '(' in skin_name and (')' in skin_name or 'Chroma' in skin_name):
                                            # This is likely a chroma
                                            is_chroma = True
                                    
                                    if is_chroma:
                                        # Add to chromas list
                                        chromas.append(skin)
                                    else:
                                        # Add to base skins, using skin_id as key to avoid duplicates
                                        skin_id = skin.get('skin_id', skin.get('skinId', 'Unknown'))
                                        base_skins[str(skin_id)] = skin
                            
                            # Add base skins if requested
                            if include_skins and base_skins:
                                account_text.append("\nSkins:")
                                for skin_id, skin in sorted(base_skins.items(), key=lambda x: x[1].get('champion_name', '')):
                                    if 'champion_name' in skin:
                                        # Get the skin name and purchase date
                                        skin_name = skin.get('skin_name', skin.get('name', ''))
                                        purchase_date = self.format_date(skin.get('purchase_date', 'Unknown'))
                                        if skin_name:
                                            # Remove any chroma information from the skin name
                                            if '(' in skin_name:
                                                skin_name = skin_name.split('(')[0].strip()
                                            account_text.append(f"- {skin['champion_name']} - {skin_name} (purchased: {purchase_date})")
                                        else:
                                            # Fallback to skin ID if no name is found
                                            account_text.append(f"- Skin {skin_id} (purchased: {purchase_date})")
                            
                            # Add chromas if requested
                            if include_chromas and chromas:
                                account_text.append("\nChromas:")
                                for chroma in sorted(chromas, key=lambda x: x.get('champion_name', '')):
                                    if 'champion_name' in chroma:
                                        # Get the skin name and purchase date
                                        skin_name = chroma.get('skin_name', chroma.get('name', ''))
                                        purchase_date = self.format_date(chroma.get('purchase_date', 'Unknown'))
                                        if skin_name:
                                            account_text.append(f"- {chroma['champion_name']} - {skin_name} (purchased: {purchase_date})")
                                        else:
                                            # Fallback to skin ID if no name is found
                                            skin_id = chroma.get('skin_id', chroma.get('skinId', 'Unknown'))
                                            account_text.append(f"- Skin {skin_id} (purchased: {purchase_date})")
                    
                    # Add separator between accounts
                    all_text.append("\n".join(account_text))
                    all_text.append("\n" + "-" * 50 + "\n")
                
                # Copy to clipboard
                clipboard = QApplication.clipboard()
                clipboard.setText("\n".join(all_text))
                
                # Update status label with success message
                self.progress_label.setText(f"Copied information for {len(accounts)} accounts to clipboard")
                
                # Schedule a timer to reset the label after 5 seconds
                QTimer.singleShot(5000, lambda: self.progress_label.setText("Ready"))
                
            finally:
                loop.close()
                
        except Exception as e:
            logger.error(f"Error copying all accounts info: {str(e)}")
            self.progress_label.setText(f"Error: Failed to copy accounts info")
            # Schedule a timer to reset the label after 5 seconds
            QTimer.singleShot(5000, lambda: self.progress_label.setText("Ready"))

def run_gui():
    try:
        # Create QApplication in main thread
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            
        # Set main thread as GUI thread
        app.thread().setPriority(QThread.Priority.HighestPriority)
        
        # Create and show window
        window = AccountManagerGUI()
        window.show()
        
        # Start event loop
        return app.exec()
    except Exception as e:
        logger.error(f"GUI Error: {str(e)}")
        return 1

if __name__ == "__main__":
    try:
        sys.exit(run_gui())
    except Exception as e:
        logger.error(f"Fatal Error: {str(e)}")
        sys.exit(1) 