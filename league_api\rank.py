from custom_logger.logger import logger
from config.regions import get_region
import aiohttp
from bs4 import BeautifulSoup
import re
import json
import jwt

async def scrape_previous_ranks(game_name: str, tag_line: str, region_code: str) -> dict:
    """Scrape previous season ranks from LeagueGraphs"""
    try:
        # Debug logging to track region_code
        logger.debug(f"scrape_previous_ranks called with region_code: {region_code}, type: {type(region_code)}")
        
        # Ensure region_code is not None
        if region_code is None:
            region_code = 'EUW'
            logger.debug(f"region_code was None, set to default: {region_code}")
            
        # Ensure game_name and tag_line are not None
        if game_name is None or tag_line is None:
            logger.error("Game name or tag line is None")
            return {}
            
        # Format the name and tag for the URL
        formatted_name = game_name.replace(' ', '%20')
        formatted_tag = tag_line
        
        # Ensure region_code is a string and not None before calling lower()
        region_code_lower = region_code.lower() if region_code else 'euw'
        url = f"https://www.leagueofgraphs.com/summoner/{region_code_lower}/{formatted_name}-{formatted_tag}"
        
        logger.debug(f"Scraping previous ranks from: {url}")
        
        # Browser-like headers
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.9",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1"
        }
        
        # Make request with headers
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers, ssl=False) as response:
                if response.status == 404:
                    logger.info(f"Account not found on LeagueOfGraphs (404) - likely no ranked history")
                    return {}
                elif response.status != 200:
                    logger.error(f"Failed to get LeagueOfGraphs data: {response.status}")
                    logger.error(f"Response headers: {response.headers}")
                    return {}
                    
                html = await response.text()
                logger.debug(f"Got HTML response length: {len(html)}")
                
        # Parse HTML
        soup = BeautifulSoup(html, 'html.parser')
        tags_box = soup.find('div', class_='tags-box')
        
        if not tags_box:
            # Check if it's really a "no ranked history" case
            error_box = soup.find('div', class_='error_box')
            if error_box:
                logger.info("Account found but no ranked history")
            else:
                logger.debug("No tags-box found - account may not exist or has no ranked history")
            return {}
            
        # Find all season tags
        season_tags = tags_box.find_all('div', class_='tag')
        logger.info(f"Found {len(season_tags)} season tags")
        
        # Process each season tag
        seasons = {}
        for tag in season_tags:
            tooltip = tag.get('tooltip', '')
            text = tag.text.strip()
            logger.info(f"Processing tag: {text} with tooltip: {tooltip}")
            
            if not tooltip:
                continue
                
            # Extract season number and rank
            season_match = re.search(r'S(\d+)\s+(\w+)', text)
            if not season_match:
                continue
                
            season_num = int(season_match.group(1))
            rank = season_match.group(2)
            
            # Extract division from tooltip
            division_match = re.search(r'reached\s+(\w+)\s+(\w+)', tooltip)
            if division_match:
                division = division_match.group(2)
            else:
                division = ''
                
            seasons[season_num] = {
                'tier': rank.upper(),
                'division': division
            }
            
        return seasons
        
    except Exception as e:
        logger.error(f"Error scraping previous ranks: {str(e)}")
        return {}

async def scrape_account_info(game_name: str, tag_line: str, region_code: str) -> dict:
    """Scrape account info from LeagueGraphs"""
    try:
        # Debug logging to track region_code
        logger.debug(f"scrape_account_info called with region_code: {region_code}, type: {type(region_code)}")
        
        # Ensure region_code is not None
        if region_code is None:
            region_code = 'EUW'
            logger.debug(f"region_code was None, set to default: {region_code}")
            
        # Ensure game_name and tag_line are not None
        if game_name is None or tag_line is None:
            logger.error("Game name or tag line is None")
            return {'seasons': {}, 'season_history': [], 'country': None}
            
        # Format the name and tag for the URL
        formatted_name = game_name.replace(' ', '%20')
        formatted_tag = tag_line
        
        # Ensure region_code is a string and not None before calling lower()
        region_code_lower = region_code.lower() if region_code else 'euw'
        url = f"https://www.leagueofgraphs.com/summoner/{region_code_lower}/{formatted_name}-{formatted_tag}"
        
        logger.debug(f"Scraping account info from: {url}")
        
        # Browser-like headers
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.9",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers, ssl=False) as response:
                if response.status == 404:
                    logger.info(f"Account not found on LeagueOfGraphs (404) - likely no ranked history")
                    return {'seasons': {}, 'season_history': [], 'country': None}
                elif response.status != 200:
                    logger.error(f"Failed to get LeagueOfGraphs data: {response.status}")
                    logger.error(f"Response headers: {response.headers}")
                    return {'seasons': {}, 'season_history': [], 'country': None}
                    
                html = await response.text()
                logger.debug(f"Got HTML response length: {len(html)}")
                
        # Parse HTML
        soup = BeautifulSoup(html, 'html.parser')
        
        # Get country info
        country = None
        country_img = soup.find('img', class_='country')
        if country_img:
            country = country_img.get('title')
            logger.info(f"Found country: {country}")
            
        # Get ranked history
        tags_box = soup.find('div', class_='tags-box')
        seasons = {}
        season_history = []
        
        if tags_box:
            season_tags = tags_box.find_all('div', class_='tag')
            logger.info(f"Found {len(season_tags)} season tags")
            
            for tag in season_tags:
                tooltip = tag.get('tooltip', '')
                text = tag.text.strip()
                logger.info(f"Processing tag: {text} with tooltip: {tooltip}")
                
                if not tooltip or "Cold Streak" in text or "Hot Streak" in text:
                    continue
                
                # Extract season info with improved regex to handle splits
                # Match patterns like "S14 (Split 1) Diamond" or "S12 Master"
                season_match = re.search(r'S(\d+)(?:\s+\(Split\s+(\d+)\))?\s+(\w+)', text)
                if not season_match:
                    continue
                
                season_num = int(season_match.group(1))
                split_num = season_match.group(2)  # Will be None if no split
                rank = season_match.group(3)
                
                # Extract division and end-of-season rank from tooltip
                division_match = re.search(r'reached\s+(\w+)\s+(\w+|\d+LP)', tooltip)
                end_season_match = re.search(r'end of the season.*?was\s+(\w+)\s+(\w+|\d+)', tooltip)
                
                division = division_match.group(2) if division_match else ''
                end_season_tier = end_season_match.group(1) if end_season_match else ''
                end_season_division = end_season_match.group(2) if end_season_match else ''
                
                # Store in seasons dict for backward compatibility
                seasons[season_num] = {
                    'tier': rank.upper(),
                    'division': division
                }
                
                # Store more detailed info in season_history list
                season_info = {
                    'season': season_num,
                    'split': split_num,
                    'queue_type': 'SOLO_DUO',  # Default, could be extracted from tooltip if needed
                    'peak_tier': rank.upper(),
                    'peak_division': division,
                    'end_tier': end_season_tier.upper() if end_season_tier else '',
                    'end_division': end_season_division
                }
                
                season_history.append(season_info)
        else:
            logger.debug("No ranked history found")
        
        # Sort season history by season (descending) and split (descending)
        season_history.sort(key=lambda x: (-x['season'], -int(x['split']) if x['split'] else 0))
            
        return {
            'seasons': seasons,
            'season_history': season_history,
            'country': country
        }
        
    except Exception as e:
        logger.error(f"Error scraping account info: {str(e)}")
        return {'seasons': {}, 'season_history': [], 'country': None}

async def get_rank_info(session_token: str, puuid: str, region_code: str, session, entitlements_token: str = None, 
                       game_name: str = None, tag_line: str = None, authenticator: object = None) -> dict:
    """Get rank information for a player"""
    try:
        # Debug logging to track region_code
        logger.debug(f"get_rank_info called with region_code: {region_code}, type: {type(region_code)}")
        
        # Ensure region_code is not None
        if region_code is None:
            region_code = 'EUW'
            logger.debug(f"region_code was None, set to default: {region_code}")
            
        logger.debug(f"Getting rank info for {game_name}#{tag_line} in region {region_code}")
        
        # Initialize default rank info
        rank_info = {
            'solo': {
                'tier': 'UNRANKED',
                'division': '',
                'lp': 0,
                'wins': 0,
                'losses': 0,
                'previous_tier': 'UNRANKED',
                'previous_division': ''
            },
            'flex': {
                'tier': 'UNRANKED',
                'division': '',
                'lp': 0,
                'wins': 0,
                'losses': 0,
                'previous_tier': 'UNRANKED',
                'previous_division': ''
            },
            'country': 'unknown',
            'creation_region': region_code
        }
        
        # Get userinfo from authenticator if available
        if authenticator and authenticator.userinfo:
            # Decode JWT if it's a string
            if isinstance(authenticator.userinfo, str):
                try:
                    # Use manual JWT decoding instead of jwt library
                    from accounts.account_pool import decode_jwt_payload
                    decoded_userinfo = decode_jwt_payload(authenticator.userinfo)
                    logger.info(f"Decoded userinfo: {json.dumps(decoded_userinfo, indent=2)}")
                    rank_info['country'] = decoded_userinfo.get('country', None)
                    # Extract creation region from decoded userinfo
                    rank_info['creation_region'] = decoded_userinfo.get('region', {}).get('tag', '').upper()
                except Exception as e:
                    logger.error(f"Error decoding JWT: {str(e)}")
            else:
                # If it's already decoded
                decoded_userinfo = authenticator.userinfo
                rank_info['country'] = decoded_userinfo.get('country', None)
                # Extract creation region from decoded userinfo
                rank_info['creation_region'] = decoded_userinfo.get('region', {}).get('tag', '').upper()

            if rank_info['country']:
                logger.info(f"Found country from userinfo: {rank_info['country']}")
            else:
                logger.info("No country found in userinfo")
        else:
            # Fallback to direct userinfo request
            request = session.build_request("GET", "https://auth.riotgames.com/userinfo", 
                headers={"Authorization": f"Bearer {session_token}"})
            userinfo_response = session.send(request)
            
            if userinfo_response.status_code == 200:
                userinfo = userinfo_response.json()
                if isinstance(userinfo, str):
                    try:
                        # Use manual JWT decoding instead of jwt library
                        from accounts.account_pool import decode_jwt_payload
                        decoded_userinfo = decode_jwt_payload(userinfo)
                        rank_info['country'] = decoded_userinfo.get('country', None)
                        # Extract creation region from decoded userinfo
                        rank_info['creation_region'] = decoded_userinfo.get('region', {}).get('tag', '').upper()
                    except Exception as e:
                        logger.error(f"Error decoding JWT from response: {str(e)}")
                else:
                    rank_info['country'] = userinfo.get('country', None)
                    # Extract creation region from decoded userinfo
                    rank_info['creation_region'] = userinfo.get('region', {}).get('tag', '').upper()
        
        region = get_region(region_code)
        if not region:
            logger.error(f"Invalid region code: {region_code}")
            return rank_info
            
        # URLs for different rank data
        current_url = f"{region.league_edge_url}/leagues-ledge/v2/signedRankedStats"
        history_url = f"{region.league_edge_url}/ranked/v1/seasons/RANKED_SOLO_5x5"  # Historical seasons endpoint
        
        headers = {
            "Authorization": f"Bearer {session_token}",
            "Accept": "application/json"
        }
        
        # Get current season data
        request = session.build_request("GET", current_url, headers=headers)
        current_response = session.send(request)
        
        # Get historical seasons data
        request = session.build_request("GET", history_url, headers=headers)
        history_response = session.send(request)
        
        # Process current season data
        if current_response.status_code == 200:
            current_data = current_response.json()
            logger.info(f"Current rank data response: {current_data}")
            
            queues = current_data.get('queues', [])
            for queue in queues:
                queue_type = queue.get('queueType')
                if queue_type in ['RANKED_SOLO_5x5', 'RANKED_FLEX_SR']:
                    tier = queue.get('tier') or 'UNRANKED'
                    rank = queue.get('rank') or ''
                    
                    queue_info = {
                        'tier': tier,
                        'division': rank,
                        'lp': queue.get('leaguePoints', 0),
                        'wins': queue.get('wins', 0),
                        'losses': queue.get('losses', 0),
                        'previous_tier': 'UNRANKED',
                        'previous_division': ''
                    }
                    
                    if queue_type == 'RANKED_SOLO_5x5':
                        rank_info['solo'].update(queue_info)
                    else:
                        rank_info['flex'].update(queue_info)
        
        # Process historical seasons data
        if history_response.status_code == 200:
            history_data = history_response.json()
            logger.info(f"Historical rank data response: {history_data}")
            
            # Find highest rank from any previous season
            highest_tier = 'UNRANKED'
            highest_division = ''
            
            for season in history_data:
                season_tier = season.get('TIER')
                season_division = season.get('DIVISION')
                
                if season_tier and season_tier != 'UNRANKED':
                    # Convert tier to numeric value for comparison
                    tier_values = {'IRON': 0, 'BRONZE': 1, 'SILVER': 2, 'GOLD': 3, 
                                 'PLATINUM': 4, 'DIAMOND': 5, 'MASTER': 6, 'GRANDMASTER': 7, 'CHALLENGER': 8}
                    
                    current_value = tier_values.get(highest_tier, -1)
                    new_value = tier_values.get(season_tier, -1)
                    
                    if new_value > current_value:
                        highest_tier = season_tier
                        highest_division = season_division
            
            if highest_tier != 'UNRANKED':
                rank_info['solo']['previous_tier'] = highest_tier
                rank_info['solo']['previous_division'] = highest_division
        
        # Try to get historical data and country from LeagueOfGraphs
        if game_name and tag_line:
            try:
                account_info = await scrape_account_info(game_name, tag_line, region_code)
                
                if account_info['seasons']:
                    # Get most recent previous season
                    latest_season = max(account_info['seasons'].keys())
                    latest_rank = account_info['seasons'][latest_season]
                    
                    rank_info['solo']['previous_tier'] = latest_rank['tier']
                    rank_info['solo']['previous_division'] = latest_rank['division']
                    
                    logger.info(f"Found previous season rank from LeagueOfGraphs: {latest_rank}")
                else:
                    logger.debug("No previous ranked seasons found")
                
                # Store the complete season history
                if account_info['season_history']:
                    rank_info['season_history'] = account_info['season_history']
                    logger.info(f"Stored complete season history with {len(account_info['season_history'])} entries")
                    
                if account_info['country']:
                    rank_info['country'] = account_info['country']
                    logger.info(f"Found account country: {account_info['country']}")
                
            except Exception as e:
                logger.error(f"Error scraping LeagueOfGraphs: {str(e)}")
                
        logger.info(f"Processed rank info: {rank_info}")
        return rank_info
                
    except Exception as e:
        logger.error(f"Error checking rank: {str(e)}")
        return rank_info 