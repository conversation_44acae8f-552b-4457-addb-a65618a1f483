import os
import json
import asyncio
import logging
from datetime import datetime
from pathlib import Path
from PyQt6.QtCore import QObject, pyqtSlot, pyqtSignal, QUrl
from PyQt6.QtWidgets import Q<PERSON>ainWindow, QApplication, QFileDialog, QMessageBox, QMenu
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtWebChannel import QWebChannel
from PyQt6.QtGui import QIcon
from bs4 import BeautifulSoup
import re

from accounts.account_viewer import AccountViewer
from database.manager import DatabaseManager

logger = logging.getLogger(__name__)

class PyQtBridge(QObject):
    """Bridge between PyQt and JavaScript"""
    
    # Signals to update the UI
    updateAccountListSignal = pyqtSignal(str)
    updateStatusSignal = pyqtSignal(str)
    updateProgressSignal = pyqtSignal(int, int)
    showAccountDetailsSignal = pyqtSignal(str)
    updateCopyProfilesSignal = pyqtSignal(str)
    
    def __init__(self, main_window, account_pool):
        super().__init__()
        self.main_window = main_window
        self.account_pool = account_pool
        self.db_manager = account_pool.db_manager
        self.web_view = None
        self.skin_database = {}
        self.load_skin_database()
        
    def set_web_view(self, web_view):
        """Set the web view reference"""
        self.web_view = web_view
        
    def load_skin_database(self):
        try:
            with open('data/skin_database.json', 'r', encoding='utf-8') as f:
                self.skin_database = json.load(f)
            print(f"Loaded {len(self.skin_database)} skin entries from JSON database")
        except Exception as e:
            print(f"Error loading skin database: {e}")
            self.skin_database = {}
    
    def _is_chroma(self, skin_id, skin_name):
        """Determine if a skin is a chroma based on its name or ID."""
        if not skin_name or not skin_id:
            return False
        
        # Color names that might appear in parentheses for chromas (only in parentheses!)
        chroma_keywords = ['chroma']  # Only explicit chroma keyword
        
        # Color names that might appear in parentheses for chromas
        color_in_parentheses = ['bronze', 'silver', 'gold', 'platinum', 'diamond', 'ruby', 'sapphire', 
                               'emerald', 'obsidian', 'pearl', 'aquamarine', 'rose', 'pink', 'red', 
                               'blue', 'green', 'yellow', 'purple', 'orange', 'black', 'white', 'gray', 'grey']
        
        # Method 1: Check if this skin ID is listed as a chroma in the database (MOST RELIABLE)
        skin_id_str = str(skin_id)
        for main_skin_id, skin_info in self.skin_database.items():
            if "chromas" in skin_info and skin_info["chromas"]:
                for chroma in skin_info["chromas"]:
                    if str(chroma.get("id")) == skin_id_str:
                        return True

        # Method 2: If it's a main skin in the database, it's NOT a chroma
        if skin_id_str in self.skin_database:
            return False

        # Method 3: Check if any chroma keyword is in the skin name (case insensitive)
        # But be more careful - only check for explicit chroma indicators
        skin_name_lower = skin_name.lower()
        if "(chroma)" in skin_name_lower:
            return True
        
        # Check for color names in parentheses pattern: "Skin Name (Color)"
        import re
        color_pattern = r'\(([^)]+)\)'
        matches = re.findall(color_pattern, skin_name_lower)
        for match in matches:
            match = match.strip().lower()
            if match in color_in_parentheses:
                return True
        
        # Method 1: Check if this skin ID is listed as a chroma in the database (MOST IMPORTANT)
        try:
            skin_id_str = str(skin_id)

            # Check all main skins to see if this ID is in their chromas list
            for main_skin_id, skin_info in self.skin_database.items():
                if "chromas" in skin_info and skin_info["chromas"]:
                    for chroma in skin_info["chromas"]:
                        if str(chroma.get("id")) == skin_id_str:
                            return True

            # Method 2: Check if the skin ID is in the database and has a name indicating it's a chroma
            if skin_id_str in self.skin_database:
                db_skin_name = self.skin_database[skin_id_str].get('name', '').lower()
                for keyword in chroma_keywords:
                    if keyword in db_skin_name:
                        return True

            # Method 3: Conservative ID-based check for high skin numbers (likely chromas)
            skin_id_int = int(skin_id)
            skin_num = skin_id_int % 1000
            # Only consider very high skin numbers as chromas to avoid false positives
            if skin_num > 500:
                return True

        except (ValueError, TypeError):
            pass
        
        return False

    def _extract_rank_from_victorious_chroma(self, skin_id, chroma_data):
        """Extract rank from Victorious skin chroma based on ID pattern and description"""
        try:
            # For Victorious skins, the rank can be determined by the skin number
            skin_num = skin_id % 1000
            rank_index = skin_num % 10

            # Standard rank mapping for Victorious chromas
            rank_map = {
                7: 'Bronze',
                8: 'Silver',
                9: 'Gold',
                0: 'Platinum',
                1: 'Diamond',
                2: 'Master',
                3: 'Grandmaster',
                4: 'Challenger'
            }

            # Try to get rank from ID pattern first
            if rank_index in rank_map:
                return rank_map[rank_index]

            # Fallback to description parsing if available
            if isinstance(chroma_data, dict) and "descriptions" in chroma_data:
                for desc in chroma_data["descriptions"]:
                    if "description" in desc:
                        description = desc["description"]
                        rank_keywords = ["Bronze", "Silver", "Gold", "Platinum", "Diamond", "Master", "Grandmaster", "Challenger"]
                        for rank in rank_keywords:
                            if rank.lower() in description.lower():
                                return rank

            return None
        except Exception:
            return None

    def _extract_short_chroma_description(self, description):
        """Extract a short description from a chroma description"""
        try:
            # For ranked rewards, extract the rank
            if "reached" in description and "rank" in description:
                # Try to extract the rank
                rank_keywords = ["Bronze", "Silver", "Gold", "Platinum", "Diamond", "Master", "Grandmaster", "Challenger"]
                for rank in rank_keywords:
                    if rank in description:
                        return rank

            # For bundle exclusives
            if "bundle exclusive" in description:
                return "Bundle"

            # For event exclusives
            if "Loot exclusive" in description and "event" in description:
                # Try to extract the event name
                event_start = description.find("in the ") + 7
                if event_start > 7:  # Found "in the "
                    event_end = description.find(" event", event_start)
                    if event_end > 0:
                        return description[event_start:event_end]
                return "Event"

            # For chromas with specific colors
            color_keywords = ["Ruby", "Sapphire", "Emerald", "Pearl", "Obsidian", "Rose Quartz", "Tanzanite", "Turquoise", "Scorch"]
            for color in color_keywords:
                if color in description:
                    return color

            # For ranked chromas
            rank_keywords = ["Bronze", "Silver", "Gold", "Platinum", "Diamond"]
            for rank in rank_keywords:
                if rank in description:
                    return rank

            # Default to a short version only if it's not an award message
            if len(description) > 20 and "award" not in description.lower():
                return description[:20] + "..."
            return description
        except Exception:
            return "Chroma"

    def _get_color_name_from_hex(self, hex_color):
        """Convert hex color to a readable color name"""
        try:
            # Remove # if present
            hex_color = hex_color.lstrip('#').upper()

            # Common color mappings based on hex values
            color_map = {
                # Reds
                'D33528': 'Red',
                'FF0000': 'Red',
                'DC143C': 'Crimson',
                'B22222': 'Fire Brick',

                # Blues
                '2756CE': 'Blue',
                '0000FF': 'Blue',
                '4169E1': 'Royal Blue',
                '1E90FF': 'Dodger Blue',
                '87CEEB': 'Sky Blue',
                '73BFBE': 'Teal',

                # Purples
                '54209B': 'Purple',
                '800080': 'Purple',
                '9932CC': 'Dark Orchid',
                '8A2BE2': 'Blue Violet',

                # Blacks/Grays
                '111112': 'Black',
                '000000': 'Black',
                '27211C': 'Dark Brown',
                '2F2F2F': 'Dark Gray',
                '808080': 'Gray',

                # Whites/Light colors
                'EAF1F4': 'White',
                'FFFFFF': 'White',
                'ECF9F8': 'Pearl',
                'F5F5F5': 'White Smoke',

                # Yellows/Golds
                'DF9117': 'Gold',
                'FFD700': 'Gold',
                'FFA500': 'Orange',
                'FF8C00': 'Dark Orange',

                # Greens
                '00FF00': 'Green',
                '32CD32': 'Lime Green',
                '228B22': 'Forest Green',
                '008000': 'Green',
            }

            # Check for exact match first
            if hex_color in color_map:
                return color_map[hex_color]

            # Convert hex to RGB for approximate matching
            r = int(hex_color[0:2], 16)
            g = int(hex_color[2:4], 16)
            b = int(hex_color[4:6], 16)

            # Determine dominant color based on RGB values
            if r > g and r > b:
                if r > 200:
                    return "Red"
                elif r > 150:
                    return "Dark Red"
                else:
                    return "Maroon"
            elif g > r and g > b:
                if g > 200:
                    return "Green"
                elif g > 150:
                    return "Dark Green"
                else:
                    return "Forest Green"
            elif b > r and b > g:
                if b > 200:
                    return "Blue"
                elif b > 150:
                    return "Dark Blue"
                else:
                    return "Navy"
            elif r > 200 and g > 200 and b < 100:
                return "Yellow"
            elif r > 200 and g < 100 and b > 200:
                return "Magenta"
            elif r < 100 and g > 200 and b > 200:
                return "Cyan"
            elif r > 200 and g > 200 and b > 200:
                return "White"
            elif r < 50 and g < 50 and b < 50:
                return "Black"
            else:
                return "Gray"

        except Exception:
            return "Chroma"

    def _get_chroma_image_url(self, skin_id, champion_id, skin_num):
        """Get the appropriate image URL for a chroma"""
        try:
            skin_id_str = str(skin_id)
            # Method 1: Check if this is a chroma and use the main skin's image
            for main_skin_id, skin_info in self.skin_database.items():
                if "chromas" in skin_info and skin_info["chromas"]:
                    for chroma in skin_info["chromas"]:
                        if str(chroma.get("id")) == skin_id_str:
                            # Found the chroma - use the main skin's image

                            # Get the main skin's skin number for Data Dragon
                            try:
                                main_skin_id_int = int(main_skin_id)
                                main_champion_id = main_skin_id_int // 1000
                                main_skin_num = main_skin_id_int % 1000

                                # Use Data Dragon URL for the main skin
                                return f"https://ddragon.leagueoflegends.com/cdn/img/champion/splash/{champion_id}_{main_skin_num}.jpg"
                            except (ValueError, TypeError):
                                pass

            # Method 2: If not found as chroma, check if it's a main skin
            if skin_id_str in self.skin_database:
                # Use the skin's own image
                return f"https://ddragon.leagueoflegends.com/cdn/img/champion/splash/{champion_id}_{skin_num}.jpg"

            # Method 3: Fallback to Data Dragon splash art
            return f"https://ddragon.leagueoflegends.com/cdn/img/champion/splash/{champion_id}_{skin_num}.jpg"

        except Exception as e:
            print(f"Error getting chroma image URL for {skin_id}: {e}")
            # Final fallback to Data Dragon
            return f"https://ddragon.leagueoflegends.com/cdn/img/champion/splash/{champion_id}_{skin_num}.jpg"

    def _get_skin_name(self, skin_id):
        """Get the skin name from the database."""
        if not skin_id or skin_id == '0':
            return "Default"
        
        skin_id_str = str(skin_id)
        
        # Extract champion ID and skin number
        try:
            skin_id_int = int(skin_id_str)
            champion_id = skin_id_int // 1000
            skin_num = skin_id_int % 1000
            
            # First check if this exact skin ID is in the database
            if skin_id_str in self.skin_database:
                skin_data = self.skin_database[skin_id_str]
                champion_name = skin_data.get('championName', '')
                skin_name = skin_data.get('name', '')
                
                # For Victorious skins with missing champion name, extract the champion from skin name
                if not champion_name and skin_name and 'Victorious' in skin_name:
                    parts = skin_name.split('Victorious ')
                    if len(parts) > 1:
                        champion_name = parts[1].strip()
                
                if skin_name:
                    # For base skins (skin_num = 0), return just the champion name
                    if skin_num == 0 or skin_name.lower() == 'default':
                        return champion_name if champion_name else f"Champion #{champion_id}"
                    
                    # For regular skins, return "Champion - Skin Name"
                    if champion_name:
                        return f"{champion_name} - {skin_name}"
                    return skin_name
            
            # Check if this is a chroma by looking in parent skin's chromas list
            # Use the improved database-based chroma detection
            for main_skin_id, skin_info in self.skin_database.items():
                if "chromas" in skin_info and skin_info["chromas"]:
                    for chroma in skin_info["chromas"]:
                        if str(chroma.get("id")) == skin_id_str:
                            # Found the chroma
                            base_skin_name = skin_info.get('name', f'Unknown Skin ({main_skin_id})')

                            # Special handling for Victorious skins with rank-based chromas
                            if "victorious" in base_skin_name.lower():
                                rank_name = self._extract_rank_from_victorious_chroma(skin_id_int, chroma)
                                if rank_name:
                                    return f"{base_skin_name} ({rank_name})"

                            # Try to get the chroma name from the chroma data itself
                            chroma_skin_name = chroma.get('name', '')
                            if chroma_skin_name and chroma_skin_name != base_skin_name:
                                # Extract the descriptive part from the chroma name
                                if chroma_skin_name.startswith(base_skin_name):
                                    # Remove base skin name and clean up
                                    desc_part = chroma_skin_name[len(base_skin_name):].strip()
                                    if desc_part:
                                        return f"{base_skin_name} ({desc_part})"
                                else:
                                    # Use the full chroma name if it's different
                                    return chroma_skin_name

                            # Try to get a descriptive name for the chroma from description
                            chroma_description = ""
                            if "descriptions" in chroma and chroma["descriptions"]:
                                for desc in chroma["descriptions"]:
                                    if "description" in desc:
                                        chroma_description = desc["description"]
                                        break

                            # Create a name that includes the base skin and chroma info
                            if chroma_description:
                                # Extract a short version of the description
                                short_desc = self._extract_short_chroma_description(chroma_description)
                                return f"{base_skin_name} ({short_desc})"
                            elif "colors" in chroma and chroma["colors"]:
                                # Use color information if no description is available
                                color_name = self._get_color_name_from_hex(chroma["colors"][0])
                                return f"{base_skin_name} ({color_name})"
                            else:
                                return f"{base_skin_name} (Chroma)"
                        
                        # Check if this parent skin has chromas
                        if 'chromas' in parent_skin and parent_skin['chromas']:
                            # Look for our skin ID in the chromas list
                            for chroma in parent_skin['chromas']:
                                if str(chroma.get('id')) == skin_id_str:
                                    # Found the chroma! Get its name and parent skin name
                                    chroma_name = chroma.get('name', '')
                                    parent_name = parent_skin.get('name', '')
                                    
                                    # If the chroma has a specific name different from parent, use it
                                    if chroma_name and chroma_name != parent_name:
                                        if champion_name:
                                            return f"{champion_name} - {chroma_name}"
                                        return chroma_name
                                    
                                    # Otherwise, try to determine the color variant
                                    # For Victorious skins, we can determine the rank from the ID
                                    if 'Victorious' in parent_name:
                                        ranks = {
                                            7: 'Bronze',
                                            8: 'Silver',
                                            9: 'Gold',
                                            0: 'Platinum',
                                            1: 'Diamond',
                                            2: 'Master',
                                            3: 'Grandmaster',
                                            4: 'Challenger'
                                        }
                                        # Extract the last digit to determine rank
                                        rank_index = skin_num % 10
                                        if rank_index in ranks:
                                            # If champion name is missing but it's a Victorious skin, extract champion from skin name
                                            if not champion_name and 'Victorious' in parent_name:
                                                # Try to extract champion name from "Victorious X"
                                                parts = parent_name.split('Victorious ')
                                                if len(parts) > 1:
                                                    champion_name = parts[1].strip()
                                            
                                            if champion_name:
                                                return f"{champion_name} - {parent_name} ({ranks[rank_index]})"
                                            return f"{parent_name} ({ranks[rank_index]})"
                                    
                                    # For other chromas, use a generic color name based on the ID
                                    chroma_colors = {
                                        1: 'Pearl', 2: 'Aquamarine', 3: 'Rose Quartz', 4: 'Catseye',
                                        5: 'Amethyst', 6: 'Ruby', 7: 'Emerald', 8: 'Sapphire',
                                        9: 'Obsidian', 10: 'Tanzanite', 11: 'Turquoise', 12: 'Citrine',
                                        13: 'Rainbow', 14: 'Peridot', 15: 'Meteorite', 16: 'Sandstone',
                                        17: 'Granite', 18: 'Jasper', 19: 'Blight', 20: 'Burn',
                                        21: 'Frostbite', 22: 'Smoke', 23: 'Sunbeam', 24: 'Twilight',
                                        25: 'Steel', 26: 'Gunmetal', 27: 'Rebel', 28: 'Curse Night',
                                        29: 'Curse Day', 30: 'Forge Black Iron', 31: 'Scorch Black', 32: 'Haunt Ebony',
                                        33: 'Toxic', 34: 'Sweet Bubblegum', 35: 'Orange', 36: 'Golden',
                                        37: 'Special Edition', 38: 'Exclusive', 39: 'Bundle', 40: 'Bronze',
                                        41: 'Silver', 42: 'Gold', 43: 'Platinum', 44: 'Diamond',
                                        45: 'Pink', 46: 'Red', 47: 'Blue', 48: 'Green',
                                        49: 'Yellow', 50: 'Purple', 51: 'Orange', 52: 'Black',
                                        53: 'White', 54: 'Gray'
                                    }
                                    
                                    # Calculate color index
                                    color_index = (skin_num - (skin_num // 10) * 10) % 54 + 1
                                    color_name = chroma_colors.get(color_index, 'Chroma')
                                    
                                    if champion_name:
                                        return f"{champion_name} - {parent_name} ({color_name})"
                                    return f"{parent_name} ({color_name})"
                        
                        # If we found a parent but no matching chroma, use the parent name with (Chroma)
                        base_skin_id = parent_id
                        break
            
            # If we found a base skin but couldn't determine the specific chroma name
            if base_skin_id and base_skin_id in self.skin_database:
                base_skin_name = self.skin_database[base_skin_id].get('name', '')
                champion_name = self.skin_database[base_skin_id].get('championName', '')
                
                # If the parent skin is "Default" (base skin), we should just call it a chroma of the champion
                if base_skin_name.lower() in ['default', '']:
                    if champion_name:
                        # Don't repeat champion name - just show it as a chroma
                        return f"{champion_name} (Chroma)"
                    return f"Skin #{skin_id_str} (Chroma)"
                
                # Check if the skin name already includes the champion name to avoid repetition
                if champion_name and champion_name.lower() in base_skin_name.lower():
                    return f"{base_skin_name} (Chroma)"
                    
                # Otherwise, show it as a chroma of the specific skin
                if champion_name:
                    # This format shows "Champion - Skin Name (Chroma)" which is what we want
                    return f"{champion_name} - {base_skin_name} (Chroma)"
                    
                return f"{base_skin_name} (Chroma)"
            
            # Try to find a skin with a similar ID (without leading zeros)
            for sid, sdata in self.skin_database.items():
                if sid.lstrip('0') == skin_id_str.lstrip('0'):
                    champion_name = sdata.get('championName', '')
                    skin_name = sdata.get('name', '')
                    if champion_name and skin_name:
                        return f"{champion_name} - {skin_name}"
                    return sdata.get('name', f"Skin #{skin_id_str}")
            
            # Try to find the champion name from the base skin
            base_skin_id = f"{champion_id}000"
            if base_skin_id in self.skin_database:
                champion_name = self.skin_database[base_skin_id].get('championName', '')
                if champion_name:
                    # Try to find the skin name from other similar skins of the same champion
                    similar_skins = [s for s_id, s in self.skin_database.items() 
                                    if s.get('championId') == champion_id or 
                                       s.get('championName') == champion_name]
                    
                    if similar_skins:
                        # Check if we can find a similar skin using the last 2 digits
                        skin_suffix = skin_id_int % 100
                        for s in similar_skins:
                            try:
                                s_id = int(s.get('id', 0))
                                if s_id % 100 == skin_suffix:
                                    skin_name = s.get('name', '')
                                    if skin_name:
                                        return f"{champion_name} - {skin_name}"
                            except (ValueError, TypeError):
                                pass
                    
                    # If no similar skin was found, return a generic name with champion
                    return f"{champion_name} - Skin #{skin_num}"
            
            return f"Champion #{champion_id} - Skin #{skin_id}"
        except (ValueError, TypeError):
            return f"Unknown Skin #{skin_id}"
    
    @pyqtSlot()
    def importAccounts(self):
        """Import accounts from a text file"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self.main_window, "Select Account List", "", "Text Files (*.txt)"
            )
            
            if file_path:
                self.updateStatus("Importing accounts...")
                
                # Create a new event loop for this operation
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    # Import accounts
                    loop.run_until_complete(self.account_pool.import_accounts_from_file(file_path))
                    
                    # Reload accounts
                    self.reload_accounts()
                    
                    self.updateStatus("Accounts imported successfully")
                finally:
                    # Close the loop properly
                    loop.close()
        except Exception as e:
            logger.error(f"Error importing accounts: {str(e)}")
            self.updateStatus(f"Error: {str(e)}")
    
    @pyqtSlot()
    def saveDatabase(self):
        """Save the database"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self.main_window, "Save Database", "", "Database Files (*.db)"
            )
            
            if file_path:
                self.updateStatus("Saving database...")
                
                # Create a new event loop for this operation
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    # Save database
                    loop.run_until_complete(self.db_manager.save_database(file_path))
                    
                    self.updateStatus("Database saved successfully")
                finally:
                    # Close the loop properly
                    loop.close()
        except Exception as e:
            logger.error(f"Error saving database: {str(e)}")
            self.updateStatus(f"Error: {str(e)}")
    
    @pyqtSlot()
    def loadDatabase(self):
        """Load the database"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self.main_window, "Load Database", "", "Database Files (*.db)"
            )
            
            if file_path:
                self.updateStatus("Loading database...")
                
                # Create a new event loop for this operation
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    # Load database
                    loop.run_until_complete(self.db_manager.load_database(file_path))
                    
                    # Reload accounts
                    self.reload_accounts()
                    
                    self.updateStatus("Database loaded successfully")
                finally:
                    # Close the loop properly
                    loop.close()
        except Exception as e:
            logger.error(f"Error loading database: {str(e)}")
            self.updateStatus(f"Error: {str(e)}")
    
    @pyqtSlot()
    def showSettings(self):
        """Show settings dialog"""
        try:
            # Import the settings dialog
            from accounts.settings import SettingsDialog
            
            # Create and show the settings dialog
            dialog = SettingsDialog(self.main_window)
            dialog.exec()
            
            self.updateStatus("Settings updated")
        except Exception as e:
            logger.error(f"Error showing settings: {str(e)}")
            self.updateStatus(f"Error: {str(e)}")
    
    @pyqtSlot()
    def clearAccounts(self):
        """Clear all accounts"""
        try:
            reply = QMessageBox.question(
                self.main_window, 
                "Clear Accounts", 
                "Are you sure you want to clear all accounts?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.updateStatus("Clearing accounts...")
                
                # Create a new event loop for this operation
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    # Clear accounts
                    loop.run_until_complete(self.account_pool.clear_accounts())
                    
                    # Reload accounts
                    self.reload_accounts()
                    
                    self.updateStatus("All accounts cleared successfully")
                finally:
                    # Close the loop properly
                    loop.close()
        except Exception as e:
            logger.error(f"Error clearing accounts: {str(e)}")
            self.updateStatus(f"Error: {str(e)}")
    
    @pyqtSlot(str)
    def deleteSelectedAccount(self, username):
        """Delete a specific account"""
        try:
            logger.info(f"Deleting account: {username}")
            
            # Create a new event loop for this operation
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # Delete the account
                loop.run_until_complete(self.account_pool.delete_account(username))
                
                # Reload accounts
                self.reload_accounts()
                
                self.updateStatus(f"Account '{username}' deleted successfully")
            finally:
                # Close the loop properly
                loop.close()
        except Exception as e:
            logger.error(f"Error deleting account {username}: {str(e)}")
            self.updateStatus(f"Error: {str(e)}")
    
    @pyqtSlot()
    def startRecheck(self):
        """Start rechecking all accounts"""
        try:
            self.updateStatus("Rechecking accounts...")
            
            # Get all accounts
            accounts = self.account_pool.get_all_accounts()
            total = len(accounts)
            
            if total == 0:
                self.updateStatus("No accounts to recheck")
                return
            
            # Create a new event loop for this operation
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # Recheck each account
                for i, account in enumerate(accounts):
                    self.updateProgress(i, total)
                    self.updateStatus(f"Rechecking {account.username}...")
                    
                    # Recheck account
                    loop.run_until_complete(self.account_pool.recheck_account(account.username))
                
                # Reload accounts
                self.reload_accounts()
                
                self.updateProgress(total, total)
                self.updateStatus("Recheck completed")
            finally:
                # Close the loop properly
                loop.close()
        except Exception as e:
            logger.error(f"Error rechecking accounts: {str(e)}")
            self.updateStatus(f"Error: {str(e)}")
    
    @pyqtSlot()
    def getCopyProfiles(self):
        """Get copy profiles from settings"""
        try:
            # Load profiles from settings.json
            try:
                with open('settings.json', 'r') as f:
                    profiles = json.load(f)
                
                # Update copy profiles in the UI
                self.updateCopyProfiles(profiles)
            except Exception as e:
                logger.error(f"Error loading profiles: {str(e)}")
                self.updateCopyProfiles({})
        except Exception as e:
            logger.error(f"Error getting copy profiles: {str(e)}")
            self.updateStatus(f"Error: {str(e)}")
    
    @pyqtSlot(str)
    def copyAccountInfo(self, profile_name):
        """Copy account info for the selected profile"""
        try:
            # Get the selected account
            if self.main_window.selected_account_index < 0:
                QMessageBox.warning(self.main_window, "No Account Selected", "Please select an account to copy.")
                return
            
            accounts = self.account_pool.get_all_accounts()
            account = accounts[self.main_window.selected_account_index]
            
            # Create a new event loop for this operation
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # Copy account info
                loop.run_until_complete(self.account_pool.copy_account_info(account.username, profile_name))
                
                self.updateStatus(f"Copied {account.username} info for profile {profile_name}")
            finally:
                # Close the loop properly
                loop.close()
        except Exception as e:
            logger.error(f"Error copying account info: {str(e)}")
            self.updateStatus(f"Error: {str(e)}")
    
    @pyqtSlot()
    def copyChampionsInfo(self):
        """Copy champions info for the selected account"""
        try:
            # Get the selected account
            if self.main_window.selected_account_index < 0:
                QMessageBox.warning(self.main_window, "No Account Selected", "Please select an account to copy.")
                return
            
            accounts = self.account_pool.get_all_accounts()
            account = accounts[self.main_window.selected_account_index]
            
            # Create a new event loop for this operation
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # Copy champions info
                loop.run_until_complete(self.account_pool.copy_champions_info(account.username))
                
                self.updateStatus(f"Copied champions info for {account.username}")
            finally:
                # Close the loop properly
                loop.close()
        except Exception as e:
            logger.error(f"Error copying champions info: {str(e)}")
            self.updateStatus(f"Error: {str(e)}")
    
    @pyqtSlot()
    def copySkinsInfo(self):
        """Copy skins info for the selected account"""
        try:
            # Get the selected account
            if self.main_window.selected_account_index < 0:
                QMessageBox.warning(self.main_window, "No Account Selected", "Please select an account to copy.")
                return
            
            accounts = self.account_pool.get_all_accounts()
            account = accounts[self.main_window.selected_account_index]
            
            # Create a new event loop for this operation
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # Copy skins info
                loop.run_until_complete(self.account_pool.copy_skins_info(account.username))
                
                self.updateStatus(f"Copied skins info for {account.username}")
            finally:
                # Close the loop properly
                loop.close()
        except Exception as e:
            logger.error(f"Error copying skins info: {str(e)}")
            self.updateStatus(f"Error: {str(e)}")
    
    @pyqtSlot()
    def copyChampionsAndSkinsInfo(self):
        """Copy champions and skins info for the selected account"""
        try:
            # Get the selected account
            if self.main_window.selected_account_index < 0:
                QMessageBox.warning(self.main_window, "No Account Selected", "Please select an account to copy.")
                return
            
            accounts = self.account_pool.get_all_accounts()
            account = accounts[self.main_window.selected_account_index]
            
            # Create a new event loop for this operation
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # Copy champions and skins info
                loop.run_until_complete(self.account_pool.copy_champions_and_skins_info(account.username))
                
                self.updateStatus(f"Copied champions and skins info for {account.username}")
            finally:
                # Close the loop properly
                loop.close()
        except Exception as e:
            logger.error(f"Error copying champions and skins info: {str(e)}")
            self.updateStatus(f"Error: {str(e)}")
    
    @pyqtSlot(bool, bool)
    def copyAllAccountsInfo(self, include_skins, include_chromas):
        """Copy info for all accounts"""
        try:
            # Create a new event loop for this operation
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # Copy all accounts info
                clipboard_content = loop.run_until_complete(self.account_pool.copy_all_accounts_info(include_skins, include_chromas))
                
                if clipboard_content:
                    # Copy to clipboard
                    clipboard = QApplication.clipboard()
                    clipboard.setText(clipboard_content)
                    self.updateStatus(f"Copied info for all accounts")
                else:
                    self.updateStatus("No account info to copy")
            finally:
                # Close the loop properly
                loop.close()
        except Exception as e:
            logger.error(f"Error copying all accounts info: {str(e)}")
            self.updateStatus(f"Error: {str(e)}")
    
    def reload_accounts(self):
        """Reload accounts from the database"""
        try:
            # Create a new event loop for this operation
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # Load accounts
                loop.run_until_complete(self.account_pool._load_accounts())
                
                # Convert accounts to a list of dictionaries
                accounts_data = []
                for account in self.account_pool.get_all_accounts():
                    accounts_data.append({
                        "username": account.username,
                        "region": account.region,
                        "is_banned": account.is_banned
                    })
                
                # Convert to JSON string and pass it to the JavaScript function
                accounts_json = json.dumps(accounts_data)
                logger.info(f"Sending accounts data to JavaScript: {len(accounts_data)} accounts")
                # In reload_accounts() and similar methods:
                self.web_view.page().runJavaScript(f"updateAccountList('{accounts_json}')")
                logger.info(f"Loaded {len(accounts_data)} accounts")
            finally:
                # Close the loop properly
                loop.close()
        except Exception as e:
            logger.error(f"Error reloading accounts: {str(e)}")
            self.updateStatus(f"Error: {str(e)}")
    
    @pyqtSlot(str)
    def loadAccountDetails(self, username):
        """Load account details for the specified username"""
        try:
            self.updateStatus(f"Loading details for {username}")
            
            # Create a new event loop for this operation
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # Get account data from database
                account_data = loop.run_until_complete(self.db_manager.get_account_info(username))
                if not account_data:
                    logger.error(f"Account not found: {username}")
                    self.updateStatus(f"Error: Account not found - {username}")
                    return
                
                # Get champions and skins
                champions = loop.run_until_complete(self.db_manager.get_champions(username))
                skins = loop.run_until_complete(self.db_manager.get_skins(username))
                
                # Add champions and skins to account data
                account_data['champions'] = champions
                account_data['skins'] = skins
                
                # Get season history from rank_info
                season_history = []
                if 'rank_info' in account_data and 'season_history' in account_data['rank_info']:
                    for season in account_data['rank_info']['season_history']:
                        season_history.append({
                            'season_id': f"S{season.get('season')}.{season.get('split')}",
                            'queue_type': season.get('queue_type', 'Unknown'),
                            'tier': season.get('end_tier', 'UNRANKED'),
                            'division': season.get('end_division', ''),
                            'league_points': 0  # Not stored in season history
                        })
                
                # Add current ranks
                if 'rank_info' in account_data:
                    # Solo/Duo queue
                    solo_rank = account_data['rank_info'].get('solo', {})
                    if solo_rank.get('tier', 'UNRANKED') != 'UNRANKED':
                        season_history.append({
                            'season_id': 'Current',
                            'queue_type': 'RANKED_SOLO_5x5',
                            'tier': solo_rank.get('tier', 'UNRANKED'),
                            'division': solo_rank.get('division', ''),
                            'league_points': solo_rank.get('lp', 0)
                        })
                    
                    # Flex queue
                    flex_rank = account_data['rank_info'].get('flex', {})
                    if flex_rank.get('tier', 'UNRANKED') != 'UNRANKED':
                        season_history.append({
                            'season_id': 'Current',
                            'queue_type': 'RANKED_FLEX_SR',
                            'tier': flex_rank.get('tier', 'UNRANKED'),
                            'division': flex_rank.get('division', ''),
                            'league_points': flex_rank.get('lp', 0)
                        })
                
                account_data['season_history'] = season_history
                
                # Generate HTML content directly
                html_content = self.generate_account_html(account_data)
                
                # Format the HTML with tabs
                formatted_html = self.format_account_details_html(html_content)
                
                # Emit signal to show account details
                self.showAccountDetailsSignal.emit(formatted_html)
                
                self.updateStatus(f"Loaded details for {username}")
            finally:
                # Close the loop properly
                loop.close()
        except Exception as e:
            logger.error(f"Error loading account details: {str(e)}")
            self.updateStatus(f"Error: {str(e)}")
    
    def format_date(self, date_str):
        """Format date string from YYYYMMDDTHHMMSS.000Z to YYYY-MM-DD HH:MM:SS"""
        if not date_str or date_str == 'Unknown':
            return 'Unknown'
        try:
            if 'T' in date_str:
                # Handle format like "20211207T114524.000Z"
                date_str = date_str.split('.')[0]  # Remove milliseconds
                dt = datetime.strptime(date_str, '%Y%m%dT%H%M%S')
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            return date_str
        except:
            return date_str

    def generate_account_html(self, account_data):
        """Generate HTML content for account details"""
        try:
            # Extract account data
            username = account_data.get('username', 'Unknown')
            game_name = account_data.get('game_name', '')
            tag_line = account_data.get('tag_line', '')
            region = account_data.get('region', 'Unknown')
            country = account_data.get('country', 'Unknown')
            creation_region = account_data.get('creation_region', 'Unknown')
            created_at = account_data.get('created_at', 'Unknown')
            last_checked = account_data.get('last_checked', 'Unknown')
            blue_essence = account_data.get('blue_essence', 0)
            riot_points = account_data.get('riot_points', 0)
            summoner_level = account_data.get('summoner_level', 0)
            
            # Format game name with tag line
            game_name_display = f"{game_name}#{tag_line}" if game_name and tag_line else username
            
            # Get champions, skins, and other data
            champions = account_data.get('champions', [])
            all_skins = account_data.get('skins', [])
            season_history = account_data.get('season_history', [])
            penalties = account_data.get('penalties', [])
            
            # Sort champions by name
            sorted_champions = sorted(champions, key=lambda x: x.get('name', '').lower())
            
            # First identify chromas
            chromas = []
            regular_skins = []
            
            for skin in all_skins:
                # Get skin name if not already present
                if not skin.get('name') or skin.get('name') == 'Unknown':
                    skin_id = skin.get('skin_id', '0')
                    skin['name'] = self._get_skin_name(skin_id)
                
                # Check if it's a chroma
                if self._is_chroma(skin.get('skin_id', '0'), skin.get('name', '')):
                    chromas.append(skin)
                else:
                    regular_skins.append(skin)
            
            # Sort skins by champion name and skin name
            sorted_skins = sorted(regular_skins, key=lambda x: (x.get('champion_name', '').lower(), x.get('name', '').lower()))
            sorted_chromas = sorted(chromas, key=lambda x: (x.get('champion_name', '').lower(), x.get('name', '').lower()))
            
            # Generate HTML content
            html = f"""
            <div style="font-family: Arial, sans-serif; color: #F0E6D2; background-color: #0A1428; padding: 20px;">
                <h1 style="color: #C89B3C; border-bottom: 2px solid #785A28; padding-bottom: 10px; margin-bottom: 20px;">
                    {username} ({game_name_display})
                </h1>
                
                <h2>Account Information</h2>
                <div style="background-color: rgba(9, 20, 40, 0.7); padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid rgba(120, 90, 40, 0.5);">
                    <p><strong>Restrictions:</strong> {account_data.get('ban_reason', 'None')} {account_data.get('ban_duration', '')}</p>
                    <p><strong>Penalties:</strong> {account_data.get('penalties', 'None')}</p>
                    {self._get_dodge_timer_html_simple(account_data.get('ban_info', ''))}
                    <p><strong>Username:</strong> {username}</p>
                    <p><strong>Game Name:</strong> {game_name_display}</p>
                    <p><strong>Region:</strong> {region}</p>
                    <p><strong>Country:</strong> {country}</p>
                    <p><strong>Creation Region:</strong> {creation_region}</p>
                    <p><strong>Created At:</strong> {created_at}</p>
                    <p><strong>Last Checked:</strong> {last_checked}</p>
                </div>
                
                <h2>Currency & Status</h2>
                <div style="background-color: rgba(9, 20, 40, 0.7); padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid rgba(120, 90, 40, 0.5);">
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <img src="https://static.wikia.nocookie.net/leagueoflegends/images/2/24/Hextech_Crafting_Blue_Essence.png/revision/latest?cb=20181204125617" alt="Blue Essence" style="width: 24px; height: 24px; margin-right: 10px;">
                        <p style="margin: 0;"><strong>Blue Essence:</strong> {blue_essence}</p>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <img src="https://static.wikia.nocookie.net/leagueoflegends/images/0/00/RP_icon.png/revision/latest?cb=20241201161019" alt="Riot Points" style="width: 24px; height: 24px; margin-right: 10px;">
                        <p style="margin: 0;"><strong>Riot Points:</strong> {riot_points}</p>
                    </div>
                    <p><strong>Summoner Level:</strong> {summoner_level}</p>
                </div>
                
                <h2>Ranked Information</h2>
                <div style="background-color: rgba(9, 20, 40, 0.7); padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid rgba(120, 90, 40, 0.5);">
                    {self.generate_ranked_html(season_history, account_data)}
                </div>
                
                <h2>Owned Champions</h2>
                <div style="background-color: rgba(9, 20, 40, 0.7); padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid rgba(120, 90, 40, 0.5);">
                    {self.generate_champions_html(sorted_champions)}
                </div>
                
                <h2>Owned Skins</h2>
                <div style="background-color: rgba(9, 20, 40, 0.7); padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid rgba(120, 90, 40, 0.5);">
                    {self.generate_skins_html(sorted_skins)}
                </div>
                
                <h2>Chromas</h2>
                <div style="background-color: rgba(9, 20, 40, 0.7); padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid rgba(120, 90, 40, 0.5);">
                    {self.generate_chromas_html(sorted_chromas)}
                </div>
            </div>
            """
            
            return html
        except Exception as e:
            logger.error(f"Error generating account HTML: {str(e)}")
            return f"<div>Error generating account details: {str(e)}</div>"
            
    def generate_ranked_html(self, season_history, account_data=None):
        """Generate HTML for ranked information including current elo"""
        html = ""

        # Show current elo if available
        if account_data and account_data.get('rank_info'):
            rank_info = account_data['rank_info']

            html += "<h3 style='color: #C89B3C; margin-top: 0; margin-bottom: 15px;'>Current Season</h3>"
            html += "<div style='margin-bottom: 20px;'>"

            # Solo/Duo Queue
            solo = rank_info.get('solo', {})
            solo_tier = solo.get('tier', 'UNRANKED')
            solo_division = solo.get('division', '')
            solo_lp = solo.get('lp', 0)
            solo_wins = solo.get('wins', 0)
            solo_losses = solo.get('losses', 0)
            solo_color = self.get_rank_color(solo_tier)

            # Calculate win rate
            total_games = solo_wins + solo_losses
            win_rate = (solo_wins / total_games * 100) if total_games > 0 else 0

            # Format rank display
            if solo_tier != 'UNRANKED':
                rank_display = f"{solo_tier} {solo_division}" if solo_division else solo_tier
                rank_display += f" ({solo_lp} LP)"
            else:
                rank_display = "UNRANKED"

            html += f"""
            <div style='background: linear-gradient(135deg, {solo_color}15, {solo_color}05); padding: 15px; border-radius: 8px; margin-bottom: 12px; border: 2px solid {solo_color}; box-shadow: 0 2px 8px rgba(0,0,0,0.3);'>
                <div style='display: flex; justify-content: space-between; align-items: center;'>
                    <div>
                        <h4 style='margin: 0; color: #F0E6D2; font-size: 16px; font-weight: 600;'>Solo/Duo Queue</h4>
                        <p style='margin: 8px 0 0 0; color: {solo_color}; font-weight: bold; font-size: 18px; text-shadow: 0 1px 2px rgba(0,0,0,0.5);'>{rank_display}</p>
                    </div>
                    <div style='text-align: right;'>
                        <p style='margin: 0; color: #C9AA71; font-size: 14px; font-weight: 500;'>W/L: {solo_wins}/{solo_losses}</p>
                        <p style='margin: 4px 0 0 0; color: #C9AA71; font-size: 14px; font-weight: 500;'>Win Rate: {win_rate:.1f}%</p>
                    </div>
                </div>
            </div>
            """

            # Flex Queue
            flex = rank_info.get('flex', {})
            flex_tier = flex.get('tier', 'UNRANKED')
            flex_division = flex.get('division', '')
            flex_lp = flex.get('lp', 0)
            flex_wins = flex.get('wins', 0)
            flex_losses = flex.get('losses', 0)
            flex_color = self.get_rank_color(flex_tier)

            # Calculate win rate for flex
            flex_total_games = flex_wins + flex_losses
            flex_win_rate = (flex_wins / flex_total_games * 100) if flex_total_games > 0 else 0

            # Format flex rank display
            if flex_tier != 'UNRANKED':
                flex_rank_display = f"{flex_tier} {flex_division}" if flex_division else flex_tier
                flex_rank_display += f" ({flex_lp} LP)"
            else:
                flex_rank_display = "UNRANKED"

            html += f"""
            <div style='background: linear-gradient(135deg, {flex_color}15, {flex_color}05); padding: 15px; border-radius: 8px; margin-bottom: 12px; border: 2px solid {flex_color}; box-shadow: 0 2px 8px rgba(0,0,0,0.3);'>
                <div style='display: flex; justify-content: space-between; align-items: center;'>
                    <div>
                        <h4 style='margin: 0; color: #F0E6D2; font-size: 16px; font-weight: 600;'>Flex Queue</h4>
                        <p style='margin: 8px 0 0 0; color: {flex_color}; font-weight: bold; font-size: 18px; text-shadow: 0 1px 2px rgba(0,0,0,0.5);'>{flex_rank_display}</p>
                    </div>
                    <div style='text-align: right;'>
                        <p style='margin: 0; color: #C9AA71; font-size: 14px; font-weight: 500;'>W/L: {flex_wins}/{flex_losses}</p>
                        <p style='margin: 4px 0 0 0; color: #C9AA71; font-size: 14px; font-weight: 500;'>Win Rate: {flex_win_rate:.1f}%</p>
                    </div>
                </div>
            </div>
            """

            html += "</div>"

        # Show historical data if available
        if season_history:
            html += "<h3 style='color: #C89B3C; margin-top: 20px; margin-bottom: 15px;'>Season History</h3>"
            html += "<table style='width: 100%; border-collapse: collapse;'>"
            html += "<tr style='border-bottom: 1px solid #785A28;'><th style='text-align: left; padding: 8px;'>Season</th><th style='text-align: left; padding: 8px;'>Queue</th><th style='text-align: left; padding: 8px;'>Tier</th><th style='text-align: left; padding: 8px;'>Division</th><th style='text-align: left; padding: 8px;'>LP</th></tr>"

            for season in season_history:
                season_id = season.get('season_id', 'Unknown')
                queue_type = season.get('queue_type', 'Unknown')
                tier = season.get('tier', 'UNRANKED')
                division = season.get('division', '')
                league_points = season.get('league_points', 0)

                # Get color based on tier
                color = self.get_rank_color(tier)

                html += f"<tr style='border-bottom: 1px solid rgba(120, 90, 40, 0.3);'>"
                html += f"<td style='padding: 8px;'>{season_id}</td>"
                html += f"<td style='padding: 8px;'>{queue_type}</td>"
                html += f"<td style='padding: 8px; color: {color};'>{tier}</td>"
                html += f"<td style='padding: 8px;'>{division}</td>"
                html += f"<td style='padding: 8px;'>{league_points}</td>"
                html += "</tr>"

            html += "</table>"
        elif not account_data or not account_data.get('rank_info'):
            html = "<p>No ranked data available</p>"

        return html
        
    def get_rank_color(self, tier):
        """Get color for rank tier - authentic League of Legends colors"""
        rank_colors = {
            "IRON": "#6B5B73",        # Dark iron gray
            "BRONZE": "#CD7F32",      # Bronze
            "SILVER": "#C0C0C0",      # Silver
            "GOLD": "#FFD700",        # Gold
            "PLATINUM": "#4ECDC4",    # Teal/Cyan
            "EMERALD": "#00C851",     # Emerald green
            "DIAMOND": "#B9F2FF",     # Light blue
            "MASTER": "#9C4AF7",      # Purple
            "GRANDMASTER": "#FF4444", # Red
            "CHALLENGER": "#F7C52D",  # Golden yellow
            "UNRANKED": "#8C8C8C"     # Gray
        }
        return rank_colors.get(tier.upper(), "#8C8C8C")

    def _get_dodge_timer_html_simple(self, ban_info):
        """Extract dodge timer information from ban_info and return simple HTML"""
        if not ban_info:
            return ""

        try:
            # Check if ban_info contains QUEUE_LOCKOUT - QUEUE_DODGE
            if "QUEUE_LOCKOUT - QUEUE_DODGE" in ban_info:
                # Try to extract expiration time from ban_info
                # Format: "Type: QUEUE_LOCKOUT, Reason: QUEUE_DODGE, Expires: 2025-07-02 05:40:48"
                import re

                # Look for expiration timestamp
                expires_match = re.search(r'Expires: (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', ban_info)
                if expires_match:
                    expires_str = expires_match.group(1)
                    try:
                        expires_time = datetime.strptime(expires_str, "%Y-%m-%d %H:%M:%S")
                        current_time = datetime.now()

                        if expires_time > current_time:
                            # Calculate remaining time
                            time_diff = expires_time - current_time
                            total_seconds = int(time_diff.total_seconds())

                            if total_seconds > 0:
                                # Format remaining time
                                minutes = total_seconds // 60
                                seconds = total_seconds % 60

                                if minutes > 0:
                                    time_remaining = f"{minutes}m {seconds}s"
                                else:
                                    time_remaining = f"{seconds}s"

                                return f'<p><strong>Dodge Timer:</strong> {time_remaining} remaining</p>'
                        else:
                            # Timer has expired
                            return f'<p><strong>Dodge Timer:</strong> Expired</p>'
                    except ValueError:
                        # If we can't parse the time, just show that there's a dodge timer
                        return f'<p><strong>Dodge Timer:</strong> Active</p>'
                else:
                    # No expiration time found, but we know it's a dodge timer
                    return f'<p><strong>Dodge Timer:</strong> Active</p>'
        except Exception as e:
            # If anything goes wrong, don't show dodge timer info
            pass

        return ""

    def generate_champions_html(self, champions):
        """Generate HTML for champions"""
        if not champions:
            return "<p>No champions owned</p>"
            
        html = f"<p>Total Champions: {len(champions)}</p>"
        html += "<div style='display: flex; flex-wrap: wrap; gap: 15px;'>"
        
        for champion in champions:
            name = champion.get('name', 'Unknown')
            champion_id = champion.get('champion_id', name)
            purchase_date = self.format_date(champion.get('purchase_date', 'Unknown'))
            
            html += f"""
            <div style='background-color: rgba(9, 20, 40, 0.8); padding: 12px; border-radius: 5px; border: 1px solid rgba(120, 90, 40, 0.5); width: 150px; text-align: center; transition: transform 0.2s ease, box-shadow 0.2s ease;' 
                 onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 8px 20px rgba(0, 0, 0, 0.4)';" 
                 onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                <img src='https://ddragon.leagueoflegends.com/cdn/15.6.1/img/champion/{name}.png' 
                     alt='{name}' 
                     style='width: 64px; height: 64px; border-radius: 50%; margin-bottom: 10px; border: 2px solid #C89B3C;'
                     onerror="this.onerror=null; this.src='https://ddragon.leagueoflegends.com/cdn/15.6.1/img/champion/Aatrox.png';">
                <div style='font-size: 14px; font-weight: bold; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; margin-bottom: 5px;'>{name}</div>
                <div style='font-size: 11px; color: #A8A8A8; white-space: normal; line-height: 1.3;'>Purchased:<br>{purchase_date}</div>
            </div>
            """
            
        html += "</div>"
        return html
        
    def generate_skins_html(self, skins):
        """Generate HTML for skins"""
        if not skins:
            return "<p>No skins owned</p>"
            
        html = f"<p>Total Skins: {len(skins)}</p>"
        html += "<div style='display: flex; flex-wrap: wrap; gap: 30px;'>"
        
        for skin in skins:
            champion_name = skin.get('champion_name', 'Unknown')
            champion_id = skin.get('champion_id', champion_name.replace(' ', ''))
            skin_id = skin.get('skin_id', '0')
            purchase_date = self.format_date(skin.get('purchase_date', 'Unknown'))
            
            # Get skin name from database
            skin_name = skin.get('name', '')
            if not skin_name or skin_name == 'Unknown':
                skin_name = self._get_skin_name(skin_id)
            
            # Clean up champion_id for URL
            if ' ' in champion_id:
                champion_id = champion_id.replace(' ', '')
            
            # Remove special characters from champion_id
            champion_id = ''.join(c for c in champion_id if c.isalnum())
            
            # Extract skin number (last 3 digits of skin_id)
            try:
                skin_id_int = int(skin_id)
                skin_num = skin_id_int % 1000  # Extract skin number (last 3 digits)
            except (ValueError, TypeError):
                skin_num = 0
            
            # Format display name
            if skin_name == "Default":
                display_name = f"{champion_name} (Default)"
            else:
                display_name = f"{champion_name} - {skin_name}"
            
            # Get the correct splash art URL using the skin number
            splash_url = f"https://ddragon.leagueoflegends.com/cdn/img/champion/splash/{champion_id}_{skin_num}.jpg"
            
            html += f"""
            <div style='background-color: rgba(9, 20, 40, 0.8); padding: 18px; border-radius: 6px; border: 1px solid rgba(120, 90, 40, 0.5); width: 300px; transition: transform 0.2s ease, box-shadow 0.2s ease;' 
                 onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 8px 20px rgba(0, 0, 0, 0.4)';" 
                 onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                <div style="position: relative;">
                    <img src='{splash_url}' 
                         alt='{display_name}' 
                         style='width: 100%; height: 180px; object-fit: cover; border-radius: 5px; margin-bottom: 12px; border: 1px solid #C89B3C;'
                         onerror="this.onerror=null; this.src='https://ddragon.leagueoflegends.com/cdn/img/champion/splash/{champion_id}_0.jpg';">
                    <div style="position: absolute; top: 10px; right: 10px; background-color: #9D4DC5; color: white; padding: 5px 10px; border-radius: 4px; font-size: 15px; font-weight: bold;">CHROMA</div>
                </div>
                <div style='font-size: 18px; font-weight: bold; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; margin-bottom: 8px;'>{display_name}</div>
                <div style='font-size: 15px; color: #A8A8A8; white-space: normal; line-height: 1.3;'>Purchased:<br>{purchase_date}</div>
            </div>
            """
            
        html += "</div>"
        return html
        
    def generate_chromas_html(self, chromas):
        """Generate HTML for chromas"""
        if not chromas:
            return "<p>No chromas owned</p>"
            
        html = f"<p>Total Chromas: {len(chromas)}</p>"
        html += "<div style='display: flex; flex-wrap: wrap; gap: 30px;'>"
        
        for chroma in chromas:
            champion_name = chroma.get('champion_name', 'Unknown')
            champion_id = chroma.get('champion_id', champion_name.replace(' ', ''))
            skin_id = chroma.get('skin_id', '0')
            purchase_date = self.format_date(chroma.get('purchase_date', 'Unknown'))
            
            # Get skin name from database
            skin_name = chroma.get('name', '')
            if not skin_name or skin_name == 'Unknown':
                skin_name = self._get_skin_name(skin_id)
            
            # Clean up champion_id for URL
            if ' ' in champion_id:
                champion_id = champion_id.replace(' ', '')
            
            # Remove special characters from champion_id
            champion_id = ''.join(c for c in champion_id if c.isalnum())
            
            # Extract skin number (last 3 digits of skin_id)
            try:
                skin_id_int = int(skin_id)
                skin_num = skin_id_int % 1000  # Extract skin number (last 3 digits)
            except (ValueError, TypeError):
                skin_num = 0
            
            # Format display name
            if skin_name == "Default":
                display_name = f"{champion_name} (Default Chroma)"
            else:
                display_name = f"{champion_name} - {skin_name}"
            
            # Get the correct image URL for chromas
            splash_url = self._get_chroma_image_url(skin_id, champion_id, skin_num)
            
            html += f"""
            <div style='background-color: rgba(9, 20, 40, 0.8); padding: 18px; border-radius: 6px; border: 1px solid rgba(120, 90, 40, 0.5); width: 300px; transition: transform 0.2s ease, box-shadow 0.2s ease;' 
                 onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 8px 20px rgba(0, 0, 0, 0.4)';" 
                 onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                <div style="position: relative;">
                    <img src='{splash_url}' 
                         alt='{display_name}' 
                         style='width: 100%; height: 180px; object-fit: cover; border-radius: 5px; margin-bottom: 12px; border: 1px solid #C89B3C;'
                         onerror="this.onerror=null; this.src='https://ddragon.leagueoflegends.com/cdn/img/champion/splash/{champion_id}_0.jpg';">
                    <div style="position: absolute; top: 10px; right: 10px; background-color: #9D4DC5; color: white; padding: 5px 10px; border-radius: 4px; font-size: 15px; font-weight: bold;">CHROMA</div>
                </div>
                <div style='font-size: 18px; font-weight: bold; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; margin-bottom: 8px;'>{display_name}</div>
                <div style='font-size: 15px; color: #A8A8A8; white-space: normal; line-height: 1.3;'>Purchased:<br>{purchase_date}</div>
            </div>
            """
            
        html += "</div>"
        return html
    
    # Helper methods to emit signals
    def updateAccountList(self, accounts_data):
        """Update the account list in the UI"""
        try:
            # Run the JavaScript to update the account list
            self.web_view.page().runJavaScript(f"updateAccountList('{accounts_data}')")
        except Exception as e:
            logger.error(f"Error updating account list: {str(e)}")
    
    def updateStatus(self, message):
        """Update the status bar in the UI"""
        self.updateStatusSignal.emit(message)
    
    def updateProgress(self, current, total):
        """Update the progress bar in the UI"""
        self.updateProgressSignal.emit(current, total)
    
    def showAccountDetails(self, html_content):
        """Show account details in the UI"""
        try:
            # Clean up the HTML content
            # Remove literal newlines and replace with spaces
            html_content = html_content.replace('\n', ' ')
            
            # Properly escape the HTML content for JavaScript
            # Use json.dumps to ensure proper escaping of quotes and special characters
            escaped_content = json.dumps(html_content)
            
            # Run the JavaScript to show the account details
            self.web_view.page().runJavaScript(f"""
                (function() {{
                    // Set the HTML content
                    showAccountDetails({escaped_content});
                    
                    // Add a small delay to ensure the DOM is updated
                    setTimeout(function() {{
                        // Set up tab functionality
                        const tabs = document.querySelectorAll(".tab");
                        tabs.forEach(tab => {{
                            tab.addEventListener("click", function() {{
                                // Remove active class from all tabs and content
                                document.querySelectorAll(".tab").forEach(t => {{
                                    t.classList.remove("active");
                                    t.style.color = "#A8A8A8";
                                    t.style.backgroundColor = "transparent";
                                }});
                                document.querySelectorAll(".tab-content").forEach(c => c.style.display = "none");
                                
                                // Add active class to clicked tab and corresponding content
                                this.classList.add("active");
                                this.style.color = "#C89B3C";
                                this.style.backgroundColor = "rgba(200, 155, 60, 0.15)";
                                document.getElementById(this.getAttribute("data-tab")).style.display = "block";
                            }});
                        }});
                        
                        // Connect export buttons to their functions
                        const fileBtn = document.querySelector("#fileBtn");
                        const exportBtn = document.querySelector("#exportBtn");
                        
                        if (fileBtn && exportBtn) {{
                            // File button dropdown
                            fileBtn.addEventListener("click", function() {{
                                const dropdown = document.querySelector("#fileDropdown");
                                if (dropdown) {{
                                    dropdown.style.display = dropdown.style.display === "block" ? "none" : "block";
                                    document.querySelector("#exportDropdown").style.display = "none";
                                }}
                            }});
                            
                            // Export button dropdown
                            exportBtn.addEventListener("click", function() {{
                                const dropdown = document.querySelector("#exportDropdown");
                                if (dropdown) {{
                                    dropdown.style.display = dropdown.style.display === "block" ? "none" : "block";
                                    document.querySelector("#fileDropdown").style.display = "none";
                                }}
                            }});
                            
                            // Close dropdowns when clicking outside
                            window.addEventListener("click", function(event) {{
                                if (!event.target.matches(".dropbtn")) {{
                                    document.querySelector("#fileDropdown").style.display = "none";
                                    document.querySelector("#exportDropdown").style.display = "none";
                                }}
                            }});
                        }}
                    }}, 200);
                }})();
            """)
            logger.info("Sent account details to JavaScript")
        except Exception as e:
            logger.error(f"Error showing account details: {str(e)}")
    
    def updateCopyProfiles(self, profiles_data):
        """Update the copy profiles in the UI"""
        self.updateCopyProfilesSignal.emit(json.dumps(profiles_data))
    
    @pyqtSlot(int)
    def exportAsHTML(self, account_index):
        """Export account as HTML"""
        try:
            accounts = self.account_pool.get_all_accounts()
            if 0 <= account_index < len(accounts):
                account = accounts[account_index]
                
                # Create a new event loop for this operation
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    # Get account data
                    db_manager = DatabaseManager()
                    account_data = loop.run_until_complete(db_manager.get_account_data(account.username))
                    
                    if account_data:
                        # Create HTML content
                        from accounts.account_viewer import AccountViewer
                        viewer = AccountViewer()
                        html_content = viewer._generate_text_content(account_data)
                        
                        # Save to file
                        file_path, _ = QFileDialog.getSaveFileName(
                            self.main_window, "Export as HTML", f"{account.username}_account.html", "HTML Files (*.html)"
                        )
                        
                        if file_path:
                            with open(file_path, 'w', encoding='utf-8') as file:
                                file.write(html_content)
                            
                            self.updateStatus(f"Exported {account.username} as HTML to {file_path}")
                    else:
                        self.updateStatus(f"No data found for {account.username}")
                finally:
                    # Close the loop properly
                    loop.close()
            else:
                self.updateStatus("Invalid account index")
        except Exception as e:
            logger.error(f"Error exporting HTML: {str(e)}")
            self.updateStatus(f"Error: {str(e)}")
    
    @pyqtSlot(int)
    def exportAsJSON(self, account_index):
        """Export account as JSON"""
        try:
            accounts = self.account_pool.get_all_accounts()
            if 0 <= account_index < len(accounts):
                account = accounts[account_index]
                
                # Create a new event loop for this operation
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    # Get account data
                    db_manager = DatabaseManager()
                    account_data = loop.run_until_complete(db_manager.get_account_data(account.username))
                    
                    if account_data:
                        # Save to file
                        file_path, _ = QFileDialog.getSaveFileName(
                            self.main_window, "Export as JSON", f"{account.username}_account.json", "JSON Files (*.json)"
                        )
                        
                        if file_path:
                            with open(file_path, 'w', encoding='utf-8') as file:
                                json.dump(account_data, file, indent=4)
                            
                            self.updateStatus(f"Exported {account.username} as JSON to {file_path}")
                    else:
                        self.updateStatus(f"No data found for {account.username}")
                finally:
                    # Close the loop properly
                    loop.close()
            else:
                self.updateStatus("Invalid account index")
        except Exception as e:
            logger.error(f"Error exporting JSON: {str(e)}")
            self.updateStatus(f"Error: {str(e)}")
    
    @pyqtSlot(int)
    def exportAsText(self, account_index):
        """Export account as text"""
        try:
            accounts = self.account_pool.get_all_accounts()
            if 0 <= account_index < len(accounts):
                account = accounts[account_index]
                
                # Create a new event loop for this operation
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    # Get account data
                    db_manager = DatabaseManager()
                    account_data = loop.run_until_complete(db_manager.get_account_data(account.username))
                    
                    if account_data:
                        # Format account info as text
                        text_content = self.format_account_as_text(account_data)
                        
                        # Save to file
                        file_path, _ = QFileDialog.getSaveFileName(
                            self.main_window, "Export as Text", f"{account.username}_account.txt", "Text Files (*.txt)"
                        )
                        
                        if file_path:
                            with open(file_path, 'w', encoding='utf-8') as file:
                                file.write(text_content)
                            
                            self.updateStatus(f"Exported {account.username} as text to {file_path}")
                    else:
                        self.updateStatus(f"No data found for {account.username}")
                finally:
                    # Close the loop properly
                    loop.close()
            else:
                self.updateStatus("Invalid account index")
        except Exception as e:
            logger.error(f"Error exporting text: {str(e)}")
            self.updateStatus(f"Error: {str(e)}")
    
    def format_account_as_text(self, account_data):
        """Format account data as text"""
        text = f"Account: {account_data.get('username', 'Unknown')}\n"
        text += f"Region: {account_data.get('region', 'Unknown')}\n"
        text += f"Level: {account_data.get('summoner_level', 0)}\n"
        text += f"Blue Essence: {account_data.get('blue_essence', 0)}\n"
        text += f"Riot Points: {account_data.get('riot_points', 0)}\n\n"
        
        # Add champions
        champions = account_data.get('champions', [])
        text += f"Champions ({len(champions)}):\n"
        for champion in champions:
            text += f"- {champion.get('name', 'Unknown Champion')}\n"
        
        text += "\n"
        
        # Add skins
        skins = account_data.get('skins', [])
        text += f"Skins ({len(skins)}):\n"
        for skin in skins:
            text += f"- {skin.get('champion_name', 'Unknown Champion')} - {skin.get('name', 'Unknown Skin')}\n"
        
        return text

    def format_account_details_html(self, html_content):
        """Format account details HTML with tabs"""
        try:
            # Parse the HTML content
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove the duplicate File and Export buttons if they exist
            for button in soup.find_all('button'):
                if button.text in ['File', 'Export']:
                    # Find the parent div that contains the button
                    parent = button.find_parent('div')
                    if parent:
                        parent.decompose()
            
            # Get the main content div
            main_div = soup.find('div', style=lambda s: s and 'font-family: Arial' in s)
            if not main_div:
                return html_content
            
            # Extract sections by looking for h2 elements
            sections = {}
            current_section = None
            current_content = []
            
            for element in main_div.children:
                if element.name == 'h2':
                    # If we were building a section, save it
                    if current_section:
                        sections[current_section] = ''.join(str(c) for c in current_content)
                    
                    # Start a new section
                    current_section = element.text.strip()
                    current_content = [element]
                elif current_section:
                    current_content.append(element)
            
            # Save the last section
            if current_section:
                sections[current_section] = ''.join(str(c) for c in current_content)
            
            # Get the header (username, game name, etc.)
            header = main_div.find('h1')
            header_text = header.text if header else "Unknown Account"
            
            # Create tabbed interface with improved design
            tabbed_html = f"""
            <div style="font-family: Arial, sans-serif; color: #F0E6D2; background-color: #0A1428; padding: 20px; position: relative;">
                <!-- Header with account name -->
                <div style="margin-bottom: 20px;">
                    <h1 style="color: #C89B3C; margin: 0; padding: 0;">{header_text}</h1>
                </div>
                
                <!-- Tabs Navigation -->
                <div class="tabs-container" style="width: 100%; margin-bottom: 20px;">
                    <div class="tabs-nav" style="display: flex; background-color: rgba(9, 20, 40, 0.8); border-bottom: 2px solid #785A28; overflow: hidden; border-top-left-radius: 8px; border-top-right-radius: 8px;">
                        <div class="tab active" data-tab="overview" style="padding: 15px 25px; cursor: pointer; color: #C89B3C; font-weight: bold; transition: all 0.3s ease; position: relative; text-align: center; flex: 1; background-color: rgba(200, 155, 60, 0.15);">Overview</div>
                        <div class="tab" data-tab="champions" style="padding: 15px 25px; cursor: pointer; color: #A8A8A8; font-weight: bold; transition: all 0.3s ease; position: relative; text-align: center; flex: 1;">Champions</div>
                        <div class="tab" data-tab="skins" style="padding: 15px 25px; cursor: pointer; color: #A8A8A8; font-weight: bold; transition: all 0.3s ease; position: relative; text-align: center; flex: 1;">Skins</div>
                        <div class="tab" data-tab="chromas" style="padding: 15px 25px; cursor: pointer; color: #A8A8A8; font-weight: bold; transition: all 0.3s ease; position: relative; text-align: center; flex: 1;">Chromas</div>
                    </div>
                    
                    <!-- Tab Content -->
                    <div id="overview" class="tab-content active" style="display: block; padding: 20px; animation: fadeIn 0.5s ease-out forwards; background-color: rgba(9, 20, 40, 0.7); border-bottom-left-radius: 8px; border-bottom-right-radius: 8px; border: 1px solid rgba(120, 90, 40, 0.5); border-top: none;">
                        <div class="section">
                            <h2 style="color: #C89B3C; border-bottom: 2px solid #785A28; padding-bottom: 5px; margin-bottom: 15px;">Account Information</h2>
                            <div style="background-color: rgba(9, 20, 40, 0.7); padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid rgba(120, 90, 40, 0.5);">
                                {sections.get('Account Information', '<p>No account information available</p>').replace('<h2>Account Information</h2>', '')}
                            </div>
                        </div>
                        
                        <div class="section">
                            <h2 style="color: #C89B3C; border-bottom: 2px solid #785A28; padding-bottom: 5px; margin-bottom: 15px;">Currency & Status</h2>
                            <div style="background-color: rgba(9, 20, 40, 0.7); padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid rgba(120, 90, 40, 0.5);">
                                {sections.get('Currency & Status', '<p>No currency information available</p>').replace('<h2>Currency & Status</h2>', '')}
                            </div>
                        </div>
                        
                        <div class="section">
                            <h2 style="color: #C89B3C; border-bottom: 2px solid #785A28; padding-bottom: 5px; margin-bottom: 15px;">Ranked Information</h2>
                            <div style="background-color: rgba(9, 20, 40, 0.7); padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid rgba(120, 90, 40, 0.5);">
                                {sections.get('Ranked Information', '<p>No ranked information available</p>').replace('<h2>Ranked Information</h2>', '')}
                            </div>
                        </div>
                    </div>
                    
                    <div id="champions" class="tab-content" style="display: none; padding: 20px; animation: fadeIn 0.5s ease-out forwards; background-color: rgba(9, 20, 40, 0.7); border-bottom-left-radius: 8px; border-bottom-right-radius: 8px; border: 1px solid rgba(120, 90, 40, 0.5); border-top: none;">
                        <div class="section">
                            <h2 style="color: #C89B3C; border-bottom: 2px solid #785A28; padding-bottom: 5px; margin-bottom: 15px;">Owned Champions</h2>
                            <div style="background-color: rgba(9, 20, 40, 0.7); padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid rgba(120, 90, 40, 0.5);">
                                {sections.get('Owned Champions', '<p>No champions owned</p>').replace('<h2>Owned Champions</h2>', '')}
                            </div>
                        </div>
                    </div>
                    
                    <div id="skins" class="tab-content" style="display: none; padding: 20px; animation: fadeIn 0.5s ease-out forwards; background-color: rgba(9, 20, 40, 0.7); border-bottom-left-radius: 8px; border-bottom-right-radius: 8px; border: 1px solid rgba(120, 90, 40, 0.5); border-top: none;">
                        <div class="section">
                            <h2 style="color: #C89B3C; border-bottom: 2px solid #785A28; padding-bottom: 5px; margin-bottom: 15px;">Owned Skins</h2>
                            <div style="background-color: rgba(9, 20, 40, 0.7); padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid rgba(120, 90, 40, 0.5);">
                                {sections.get('Owned Skins', '<p>No skins owned</p>').replace('<h2>Owned Skins</h2>', '')}
                            </div>
                        </div>
                    </div>
                    
                    <div id="chromas" class="tab-content" style="display: none; padding: 20px; animation: fadeIn 0.5s ease-out forwards; background-color: rgba(9, 20, 40, 0.7); border-bottom-left-radius: 8px; border-bottom-right-radius: 8px; border: 1px solid rgba(120, 90, 40, 0.5); border-top: none;">
                        <div class="section">
                            <h2 style="color: #C89B3C; border-bottom: 2px solid #785A28; padding-bottom: 5px; margin-bottom: 15px;">Chromas</h2>
                            <div style="background-color: rgba(9, 20, 40, 0.7); padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid rgba(120, 90, 40, 0.5);">
                                {sections.get('Chromas', '<p>No chromas owned</p>').replace('<h2>Chromas</h2>', '')}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- JavaScript for tab functionality -->
                <script>
                // Define selectedAccountIndex if it doesn't exist
                if (typeof selectedAccountIndex === 'undefined') {{
                    var selectedAccountIndex = 0;
                }}
                
                // Set up tab functionality
                const tabs = document.querySelectorAll(".tab");
                tabs.forEach(tab => {{
                    tab.addEventListener("click", function() {{
                        // Remove active class from all tabs and content
                        document.querySelectorAll(".tab").forEach(t => {{
                            t.classList.remove("active");
                            t.style.color = "#A8A8A8";
                            t.style.backgroundColor = "transparent";
                        }});
                        document.querySelectorAll(".tab-content").forEach(c => c.style.display = "none");
                        
                        // Add active class to clicked tab and corresponding content
                        this.classList.add("active");
                        this.style.color = "#C89B3C";
                        this.style.backgroundColor = "rgba(200, 155, 60, 0.15)";
                        document.getElementById(this.getAttribute("data-tab")).style.display = "block";
                    }});
                }});
                </script>
            </div>
            """
            
            return tabbed_html
        except Exception as e:
            logger.error(f"Error formatting account details HTML: {str(e)}")
            return html_content

    def _is_chroma(self, skin_id, skin_name):
        """Determine if a skin is a chroma based on its name or ID."""
        if not skin_name or not skin_id:
            return False
        
        # Common chroma indicators in skin names
        chroma_keywords = ['chroma', 'pearl', 'ruby', 'sapphire', 'emerald', 'obsidian', 
                           'rose quartz', 'aquamarine', 'catseye', 'tanzanite', 'turquoise',
                           'amethyst', 'citrine', 'rainbow', 'peridot', 'ruby', 'emerald',
                           'golden', 'special edition', 'exclusive', 'bundle']
        
        # Color names that might appear in parentheses for chromas
        color_in_parentheses = ['bronze', 'silver', 'gold', 'platinum', 'diamond', 'ruby', 'sapphire', 
                               'emerald', 'obsidian', 'pearl', 'aquamarine', 'rose', 'pink', 'red', 
                               'blue', 'green', 'yellow', 'purple', 'orange', 'black', 'white', 'gray', 'grey']
        
        # Check if any chroma keyword is in the skin name (case insensitive)
        skin_name_lower = skin_name.lower()
        for keyword in chroma_keywords:
            if keyword in skin_name_lower:
                return True
        
        # Check for color names in parentheses pattern: "Skin Name (Color)"
        import re
        color_pattern = r'\(([^)]+)\)'
        matches = re.findall(color_pattern, skin_name_lower)
        for match in matches:
            match = match.strip().lower()
            if match in color_in_parentheses:
                return True
        
        # Some chromas have specific patterns in their IDs
        # For example, chromas often have skin numbers above 100
        try:
            skin_id_int = int(skin_id)
            skin_num = skin_id_int % 1000
            if skin_num > 100:
                return True
            
            # Check if the skin ID is in the database and has a name indicating it's a chroma
            skin_id_str = str(skin_id)
            if skin_id_str in self.skin_database:
                db_skin_name = self.skin_database[skin_id_str].get('name', '').lower()
                for keyword in chroma_keywords:
                    if keyword in db_skin_name:
                        return True
        except (ValueError, TypeError):
            pass
        
        return False


class HtmlGui(QMainWindow):
    """Main window for the HTML-based GUI"""
    
    def __init__(self, account_pool):
        super().__init__()
        self.account_pool = account_pool
        self.selected_account_index = -1
        self.skin_database = self._load_skin_database()
        self.setup_ui()
    
    def _load_skin_database(self):
        """Load skin database from JSON file"""
        try:
            # Path to the skin database file
            json_db_path = 'data/skin_database.json'
            
            # Check if the database file exists
            if os.path.exists(json_db_path):
                # Load the existing database in chunks to avoid memory issues
                skin_db = {}
                with open(json_db_path, 'r', encoding='utf-8') as f:
                    skin_db = json.load(f)
                
                logger.info(f"Loaded skin database with {len(skin_db)} entries")
                return skin_db
            else:
                logger.warning(f"Skin database file not found: {json_db_path}")
                return {}
        except Exception as e:
            logger.error(f"Error loading skin database: {str(e)}")
            return {}
    
    def _get_skin_name(self, skin_id):
        """Get the skin name from the database."""
        if not skin_id or skin_id == '0':
            return "Default"
        
        skin_id_str = str(skin_id)
        
        # Extract champion ID and skin number
        try:
            skin_id_int = int(skin_id_str)
            champion_id = skin_id_int // 1000
            skin_num = skin_id_int % 1000
            
            # First check if this exact skin ID is in the database
            if skin_id_str in self.skin_database:
                skin_data = self.skin_database[skin_id_str]
                champion_name = skin_data.get('championName', '')
                skin_name = skin_data.get('name', '')
                
                # For Victorious skins with missing champion name, extract the champion from skin name
                if not champion_name and skin_name and 'Victorious' in skin_name:
                    parts = skin_name.split('Victorious ')
                    if len(parts) > 1:
                        champion_name = parts[1].strip()
                
                if skin_name:
                    # For base skins (skin_num = 0), return just the champion name
                    if skin_num == 0 or skin_name.lower() == 'default':
                        return champion_name if champion_name else f"Champion #{champion_id}"
                    
                    # For regular skins, return "Champion - Skin Name"
                    if champion_name:
                        return f"{champion_name} - {skin_name}"
                    return skin_name
            
            # Check if this is a chroma by looking in parent skin's chromas list
            # For example, skin ID 34047 is a chroma of skin ID 34046 (Victorious Anivia)
            base_skin_id = None
            
            # Try to find the parent skin ID
            if skin_num > 0:
                # For chromas, the parent skin ID is usually the skin ID with the last digit(s) changed to 0
                # Try different possible parent skin IDs
                possible_parents = [
                    # First try exact parent (e.g., 34047 -> 34046 for Victorious Anivia)
                    str(skin_id_int - 1),
                    # Then try base skin with same tens digit (e.g., 34047 -> 34040)
                    str(champion_id * 1000 + (skin_num // 10) * 10),
                    # Then try base skin (e.g., 34047 -> 34000)
                    str(champion_id * 1000)
                ]
                
                for parent_id in possible_parents:
                    if parent_id in self.skin_database:
                        parent_skin = self.skin_database[parent_id]
                        champion_name = parent_skin.get('championName', '')
                        
                        # Check if this parent skin has chromas
                        if 'chromas' in parent_skin and parent_skin['chromas']:
                            # Look for our skin ID in the chromas list
                            for chroma in parent_skin['chromas']:
                                if str(chroma.get('id')) == skin_id_str:
                                    # Found the chroma! Get its name and parent skin name
                                    chroma_name = chroma.get('name', '')
                                    parent_name = parent_skin.get('name', '')
                                    
                                    # If the chroma has a specific name different from parent, use it
                                    if chroma_name and chroma_name != parent_name:
                                        if champion_name:
                                            return f"{champion_name} - {chroma_name}"
                                        return chroma_name
                                    
                                    # Otherwise, try to determine the color variant
                                    # For Victorious skins, we can determine the rank from the ID
                                    if 'Victorious' in parent_name:
                                        ranks = {
                                            7: 'Bronze',
                                            8: 'Silver',
                                            9: 'Gold',
                                            0: 'Platinum',
                                            1: 'Diamond',
                                            2: 'Master',
                                            3: 'Grandmaster',
                                            4: 'Challenger'
                                        }
                                        # Extract the last digit to determine rank
                                        rank_index = skin_num % 10
                                        if rank_index in ranks:
                                            # If champion name is missing but it's a Victorious skin, extract champion from skin name
                                            if not champion_name and 'Victorious' in parent_name:
                                                # Try to extract champion name from "Victorious X"
                                                parts = parent_name.split('Victorious ')
                                                if len(parts) > 1:
                                                    champion_name = parts[1].strip()
                                            
                                            if champion_name:
                                                return f"{champion_name} - {parent_name} ({ranks[rank_index]})"
                                            return f"{parent_name} ({ranks[rank_index]})"
                                    
                                    # For other chromas, use a generic color name based on the ID
                                    chroma_colors = {
                                        1: 'Pearl', 2: 'Aquamarine', 3: 'Rose Quartz', 4: 'Catseye',
                                        5: 'Amethyst', 6: 'Ruby', 7: 'Emerald', 8: 'Sapphire',
                                        9: 'Obsidian', 10: 'Tanzanite', 11: 'Turquoise', 12: 'Citrine',
                                        13: 'Rainbow', 14: 'Peridot', 15: 'Meteorite', 16: 'Sandstone',
                                        17: 'Granite', 18: 'Jasper', 19: 'Blight', 20: 'Burn',
                                        21: 'Frostbite', 22: 'Smoke', 23: 'Sunbeam', 24: 'Twilight',
                                        25: 'Steel', 26: 'Gunmetal', 27: 'Rebel', 28: 'Curse Night',
                                        29: 'Curse Day', 30: 'Forge Black Iron', 31: 'Scorch Black', 32: 'Haunt Ebony',
                                        33: 'Toxic', 34: 'Sweet Bubblegum', 35: 'Orange', 36: 'Golden',
                                        37: 'Special Edition', 38: 'Exclusive', 39: 'Bundle', 40: 'Bronze',
                                        41: 'Silver', 42: 'Gold', 43: 'Platinum', 44: 'Diamond',
                                        45: 'Pink', 46: 'Red', 47: 'Blue', 48: 'Green',
                                        49: 'Yellow', 50: 'Purple', 51: 'Orange', 52: 'Black',
                                        53: 'White', 54: 'Gray'
                                    }
                                    
                                    # Calculate color index
                                    color_index = (skin_num - (skin_num // 10) * 10) % 54 + 1
                                    color_name = chroma_colors.get(color_index, 'Chroma')
                                    
                                    if champion_name:
                                        return f"{champion_name} - {parent_name} ({color_name})"
                                    return f"{parent_name} ({color_name})"
                        
                        # If we found a parent but no matching chroma, use the parent name with (Chroma)
                        base_skin_id = parent_id
                        break
            
            # If we found a base skin but couldn't determine the specific chroma name
            if base_skin_id and base_skin_id in self.skin_database:
                base_skin_name = self.skin_database[base_skin_id].get('name', '')
                champion_name = self.skin_database[base_skin_id].get('championName', '')
                
                # If the parent skin is "Default" (base skin), we should just call it a chroma of the champion
                if base_skin_name.lower() in ['default', '']:
                    if champion_name:
                        # Don't repeat champion name - just show it as a chroma
                        return f"{champion_name} (Chroma)"
                    return f"Skin #{skin_id_str} (Chroma)"
                
                # Check if the skin name already includes the champion name to avoid repetition
                if champion_name and champion_name.lower() in base_skin_name.lower():
                    return f"{base_skin_name} (Chroma)"
                    
                # Otherwise, show it as a chroma of the specific skin
                if champion_name:
                    # This format shows "Champion - Skin Name (Chroma)" which is what we want
                    return f"{champion_name} - {base_skin_name} (Chroma)"
                    
                return f"{base_skin_name} (Chroma)"
            
            # Try to find a skin with a similar ID (without leading zeros)
            for sid, sdata in self.skin_database.items():
                if sid.lstrip('0') == skin_id_str.lstrip('0'):
                    champion_name = sdata.get('championName', '')
                    skin_name = sdata.get('name', '')
                    if champion_name and skin_name:
                        return f"{champion_name} - {skin_name}"
                    return sdata.get('name', f"Skin #{skin_id_str}")
            
            # Try to find the champion name from the base skin
            base_skin_id = f"{champion_id}000"
            if base_skin_id in self.skin_database:
                champion_name = self.skin_database[base_skin_id].get('championName', '')
                if champion_name:
                    # Try to find the skin name from other similar skins of the same champion
                    similar_skins = [s for s_id, s in self.skin_database.items() 
                                    if s.get('championId') == champion_id or 
                                       s.get('championName') == champion_name]
                    
                    if similar_skins:
                        # Check if we can find a similar skin using the last 2 digits
                        skin_suffix = skin_id_int % 100
                        for s in similar_skins:
                            try:
                                s_id = int(s.get('id', 0))
                                if s_id % 100 == skin_suffix:
                                    skin_name = s.get('name', '')
                                    if skin_name:
                                        return f"{champion_name} - {skin_name}"
                            except (ValueError, TypeError):
                                pass
                    
                    # If no similar skin was found, return a generic name with champion
                    return f"{champion_name} - Skin #{skin_num}"
            
            return f"Champion #{champion_id} - Skin #{skin_id}"
        except (ValueError, TypeError):
            return f"Unknown Skin #{skin_id}"
    
    def setup_ui(self):
        """Set up the UI"""
        # Set window properties
        self.setWindowTitle("League Account Manager")
        self.setMinimumSize(1200, 800)
        
        # Create web view
        self.web_view = QWebEngineView()
        self.setCentralWidget(self.web_view)
        
        # Create web channel for communication between Python and JavaScript
        self.channel = QWebChannel()
        self.bridge = PyQtBridge(self, self.account_pool)
        self.channel.registerObject("pyQtBridge", self.bridge)
        self.web_view.page().setWebChannel(self.channel)
        
        # Set web view reference in bridge
        self.bridge.set_web_view(self.web_view)
        
        # Connect signals
        self.bridge.updateAccountListSignal.connect(self.update_account_list)
        self.bridge.updateStatusSignal.connect(self.update_status)
        self.bridge.updateProgressSignal.connect(self.update_progress)
        self.bridge.showAccountDetailsSignal.connect(self.show_account_details)
        self.bridge.updateCopyProfilesSignal.connect(self.update_copy_profiles)
        
        # Load HTML template
        self.load_html_template()
    
    def load_html_template(self):
        """Load the HTML template"""
        try:
            template_path = os.path.join(os.path.dirname(__file__), "simple_template.html")
            with open(template_path, "r", encoding="utf-8") as f:
                html_content = f.read()
            
            # Connect to loadFinished signal to load accounts after the page is loaded
            self.web_view.loadFinished.connect(self.on_page_loaded)
            
            # Load the HTML content
            self.web_view.setHtml(html_content)
            logger.info("Loaded simple HTML template")
        except Exception as e:
            logger.error(f"Error loading HTML template: {str(e)}")
    
    def on_page_loaded(self, success):
        """Called when the page has finished loading"""
        if success:
            # Load accounts after the page is loaded
            self.load_accounts()
        else:
            logger.error("Failed to load HTML template")
    
    def load_accounts(self):
        """Load accounts from the database"""
        try:
            # Create a new event loop for this operation
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # Load accounts
                loop.run_until_complete(self.account_pool._load_accounts())
                
                # Convert accounts to a list of dictionaries
                accounts_data = []
                for account in self.account_pool.get_all_accounts():
                    accounts_data.append({
                        "username": account.username,
                        "region": account.region,
                        "is_banned": account.is_banned
                    })
                
                # Convert to JSON string and pass it to the JavaScript function
                accounts_json = json.dumps(accounts_data)
                logger.info(f"Sending accounts data to JavaScript: {len(accounts_data)} accounts")
                # In reload_accounts() and similar methods:
                self.web_view.page().runJavaScript(f"updateAccountList('{accounts_json}')")
                logger.info(f"Loaded {len(accounts_data)} accounts")
            finally:
                # Close the loop properly
                loop.close()
        except Exception as e:
            logger.error(f"Error loading accounts: {str(e)}")
            self.updateStatus(f"Error: {str(e)}")
    
    # JavaScript callback methods
    def update_account_list(self, accounts_data):
        """Update the account list in the UI"""
        try:
            # Run the JavaScript to update the account list
            self.web_view.page().runJavaScript(f"updateAccountList('{accounts_data}')")
        except Exception as e:
            logger.error(f"Error updating account list: {str(e)}")
    
    def update_status(self, message):
        """Update the status bar in the UI"""
        # Escape message using JSON.stringify
        self.web_view.page().runJavaScript(f'updateStatus({json.dumps(message)})')
    
    def update_progress(self, current, total):
        """Update the progress bar in the UI"""
        self.web_view.page().runJavaScript(f"updateProgress({current}, {total})")
    
    def show_account_details(self, html_content):
        """Show account details in the UI"""
        try:
            # Clean up the HTML content
            # Remove literal newlines and replace with spaces
            html_content = html_content.replace('\n', ' ')
            
            # Properly escape the HTML content for JavaScript
            # Use json.dumps to ensure proper escaping of quotes and special characters
            escaped_content = json.dumps(html_content)
            
            # Run the JavaScript to show the account details
            self.web_view.page().runJavaScript(f"""
                (function() {{
                    // Set the HTML content
                    showAccountDetails({escaped_content});
                    
                    // Add a small delay to ensure the DOM is updated
                    setTimeout(function() {{
                        // Set up tab functionality
                        const tabs = document.querySelectorAll(".tab");
                        tabs.forEach(tab => {{
                            tab.addEventListener("click", function() {{
                                // Remove active class from all tabs and content
                                document.querySelectorAll(".tab").forEach(t => {{
                                    t.classList.remove("active");
                                    t.style.color = "#A8A8A8";
                                    t.style.backgroundColor = "transparent";
                                }});
                                document.querySelectorAll(".tab-content").forEach(c => c.style.display = "none");
                                
                                // Add active class to clicked tab and corresponding content
                                this.classList.add("active");
                                this.style.color = "#C89B3C";
                                this.style.backgroundColor = "rgba(200, 155, 60, 0.15)";
                                document.getElementById(this.getAttribute("data-tab")).style.display = "block";
                            }});
                        }});
                        
                        // Connect export buttons to their functions
                        const fileBtn = document.querySelector("#fileBtn");
                        const exportBtn = document.querySelector("#exportBtn");
                        
                        if (fileBtn && exportBtn) {{
                            // File button dropdown
                            fileBtn.addEventListener("click", function() {{
                                const dropdown = document.querySelector("#fileDropdown");
                                if (dropdown) {{
                                    dropdown.style.display = dropdown.style.display === "block" ? "none" : "block";
                                    document.querySelector("#exportDropdown").style.display = "none";
                                }}
                            }});
                            
                            // Export button dropdown
                            exportBtn.addEventListener("click", function() {{
                                const dropdown = document.querySelector("#exportDropdown");
                                if (dropdown) {{
                                    dropdown.style.display = dropdown.style.display === "block" ? "none" : "block";
                                    document.querySelector("#fileDropdown").style.display = "none";
                                }}
                            }});
                            
                            // Close dropdowns when clicking outside
                            window.addEventListener("click", function(event) {{
                                if (!event.target.matches(".dropbtn")) {{
                                    document.querySelector("#fileDropdown").style.display = "none";
                                    document.querySelector("#exportDropdown").style.display = "none";
                                }}
                            }});
                        }}
                    }}, 200);
                }})();
            """)
            logger.info("Sent account details to JavaScript")
        except Exception as e:
            logger.error(f"Error showing account details: {str(e)}")
    
    def update_copy_profiles(self, profiles_data):
        """Update the copy profiles in the UI"""
        try:
            # If profiles_data is a string, use it directly
            if isinstance(profiles_data, str):
                profiles_json = profiles_data
            else:
                # If it's not a string, it's an error
                logger.error(f"Profiles data is not a string: {type(profiles_data)}")
                return
            
            # Run the JavaScript to update the copy profiles
            self.web_view.page().runJavaScript(f"updateCopyProfiles({profiles_json})")
        except Exception as e:
            logger.error(f"Error updating copy profiles: {str(e)}")
    
    # Add a method to update the selected account index
    def set_selected_account_index(self, index):
        """Set the selected account index"""
        self.selected_account_index = index
        
    # Add a JavaScript handler to receive the selected account index from the UI
    @pyqtSlot(int)
    def handle_account_selection(self, index):
        """Handle account selection from the UI"""
        self.selected_account_index = index

    def show_context_menu(self, position):
        """Show context menu at the specified position"""
        try:
            print(f"DEBUG: show_context_menu called at position: {position}")
            
            # Check if we have a selected account
            if not hasattr(self, 'selected_account_index'):
                print("DEBUG: No selected_account_index attribute")
                return
                
            print(f"DEBUG: Has selected_account_index attribute: True")
            print(f"DEBUG: selected_account_index value: {self.selected_account_index}")
            
            # Create context menu
            menu = QMenu()
            print("DEBUG: Creating context menu")
            
            # Add menu items
            copy_champions_action = menu.addAction("Copy Champions")
            copy_skins_action = menu.addAction("Copy Skins")
            copy_champions_and_skins_action = menu.addAction("Copy Champions & Skins")
            copy_all_info_action = menu.addAction("Copy All Info")
            
            # Show menu at cursor position
            print(f"DEBUG: Showing context menu at position: {position}")
            action = menu.exec_(self.web_view.mapToGlobal(position))
            
            if action == copy_champions_action:
                self.bridge.copyChampionsInfo()
            elif action == copy_skins_action:
                self.bridge.copySkinsInfo()
            elif action == copy_champions_and_skins_action:
                self.bridge.copyChampionsAndSkinsInfo()
            elif action == copy_all_info_action:
                self.bridge.copyAllAccountsInfo(True, True)  # Include both skins and chromas
            
            print("DEBUG: Context menu execution completed")
        except Exception as e:
            logger.error(f"Error showing context menu: {str(e)}")
            self.updateStatus(f"Error: {str(e)}")


def run_html_gui(account_pool):
    """Run the HTML-based GUI"""
    app = QApplication.instance()
    if not app:
        import sys
        app = QApplication(sys.argv)
    
    window = HtmlGui(account_pool)
    window.show()
    
    return app.exec() 
