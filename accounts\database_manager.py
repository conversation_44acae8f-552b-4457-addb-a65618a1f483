"""
Database Manager for account data
"""
import os
import sqlite3
import asyncio
import logging
from datetime import datetime

# Set up logging
logger = logging.getLogger(__name__)

class DatabaseManager:
    """
    Manager for interacting with the SQLite database for account data
    """
    
    def __init__(self, db_path=None):
        """
        Initialize the database manager
        
        Args:
            db_path: Path to the database file (default: accounts.db in current directory)
        """
        self.db_path = db_path or os.path.join(os.getcwd(), 'accounts.db')
        self.connection = None
        
    async def init_db(self):
        """
        Initialize the database connection and create tables if needed
        """
        try:
            # Create connection
            self.connection = sqlite3.connect(self.db_path)
            
            # Create tables if they don't exist
            cursor = self.connection.cursor()
            
            # Create accounts table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS accounts (
                    username TEXT PRIMARY KEY,
                    password TEXT,
                    region TEXT,
                    last_checked TEXT,
                    summoner_level INTEGER DEFAULT 0,
                    blue_essence INTEGER DEFAULT 0,
                    riot_points INTEGER DEFAULT 0,
                    is_banned BOOLEAN DEFAULT 0,
                    ban_info TEXT,
                    game_name TEXT,
                    tag_line TEXT,
                    creation_region TEXT,
                    created_at TEXT,
                    country TEXT,
                    penalty_minutes INTEGER DEFAULT 0
                )
            ''')
            
            # Create champions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS champions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT,
                    champion_id TEXT,
                    name TEXT,
                    purchase_date TEXT,
                    FOREIGN KEY (username) REFERENCES accounts(username)
                )
            ''')
            
            # Create skins table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS skins (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT,
                    skin_id TEXT,
                    champion_id TEXT,
                    name TEXT,
                    champion_name TEXT,
                    purchase_date TEXT,
                    FOREIGN KEY (username) REFERENCES accounts(username)
                )
            ''')
            
            # Create season history table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS season_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT,
                    season TEXT,
                    split TEXT,
                    queue_type TEXT,
                    peak_tier TEXT,
                    peak_division TEXT,
                    end_tier TEXT,
                    end_division TEXT,
                    FOREIGN KEY (username) REFERENCES accounts(username)
                )
            ''')
            
            self.connection.commit()
            logger.info("Database initialized")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")
            return False
    
    async def get_accounts(self):
        """
        Get all accounts from the database
        
        Returns:
            List of account data dictionaries
        """
        try:
            if not self.connection:
                await self.init_db()
                
            cursor = self.connection.cursor()
            cursor.execute("SELECT * FROM accounts")
            
            columns = [col[0] for col in cursor.description]
            accounts = []
            
            for row in cursor.fetchall():
                account_data = dict(zip(columns, row))
                
                # Add champions
                cursor.execute("SELECT * FROM champions WHERE username = ?", (account_data['username'],))
                champ_columns = [col[0] for col in cursor.description]
                champions = [dict(zip(champ_columns, row)) for row in cursor.fetchall()]
                account_data['champions'] = champions
                
                # Add skins
                cursor.execute("SELECT * FROM skins WHERE username = ?", (account_data['username'],))
                skin_columns = [col[0] for col in cursor.description]
                skins = [dict(zip(skin_columns, row)) for row in cursor.fetchall()]
                account_data['skins'] = skins
                
                # Add season history
                cursor.execute("SELECT * FROM season_history WHERE username = ?", (account_data['username'],))
                history_columns = [col[0] for col in cursor.description]
                season_history = [dict(zip(history_columns, row)) for row in cursor.fetchall()]
                account_data['season_history'] = season_history
                
                accounts.append(account_data)
                
            return accounts
            
        except Exception as e:
            logger.error(f"Error getting accounts: {str(e)}")
            return []
    
    async def get_account_data(self, username):
        """
        Get data for a specific account
        
        Args:
            username: The username of the account
            
        Returns:
            Account data dictionary or None if not found
        """
        try:
            if not self.connection:
                await self.init_db()
                
            cursor = self.connection.cursor()
            cursor.execute("SELECT * FROM accounts WHERE username = ?", (username,))
            
            row = cursor.fetchone()
            if not row:
                return None
                
            columns = [col[0] for col in cursor.description]
            account_data = dict(zip(columns, row))
            
            # Add champions
            cursor.execute("SELECT * FROM champions WHERE username = ?", (username,))
            champ_columns = [col[0] for col in cursor.description]
            champions = [dict(zip(champ_columns, row)) for row in cursor.fetchall()]
            account_data['champions'] = champions
            
            # Add skins
            cursor.execute("SELECT * FROM skins WHERE username = ?", (username,))
            skin_columns = [col[0] for col in cursor.description]
            skins = [dict(zip(skin_columns, row)) for row in cursor.fetchall()]
            account_data['skins'] = skins
            
            # Add season history
            cursor.execute("SELECT * FROM season_history WHERE username = ?", (username,))
            history_columns = [col[0] for col in cursor.description]
            season_history = [dict(zip(history_columns, row)) for row in cursor.fetchall()]
            account_data['season_history'] = season_history
            
            return account_data
            
        except Exception as e:
            logger.error(f"Error getting account data: {str(e)}")
            return None
    
    async def delete_account(self, username):
        """
        Delete an account and all its related data
        
        Args:
            username: The username of the account to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.connection:
                await self.init_db()
                
            cursor = self.connection.cursor()
            
            # Delete related data first
            cursor.execute("DELETE FROM champions WHERE username = ?", (username,))
            cursor.execute("DELETE FROM skins WHERE username = ?", (username,))
            cursor.execute("DELETE FROM season_history WHERE username = ?", (username,))
            
            # Then delete the account
            cursor.execute("DELETE FROM accounts WHERE username = ?", (username,))
            
            self.connection.commit()
            
            return cursor.rowcount > 0
            
        except Exception as e:
            logger.error(f"Error deleting account: {str(e)}")
            return False 