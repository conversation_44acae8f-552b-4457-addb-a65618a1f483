from custom_logger.logger import logger
from suppliers.auth import create_session, get_auth_tokens, get_auth_tokens_sync, solve_captcha
from suppliers.userinfo import get_userinfo
from suppliers.entitlement import get_entitlement_token
from suppliers.geopas import get_geopas_token
from suppliers.queue import get_queue_token
from suppliers.session_token import get_session_token
from suppliers.sipt import get_sipt_token
from config.regions import get_region
from database.manager import DatabaseManager
import json
import asyncio
import httpx

class Authenticator:
    def __init__(self, username: str, password: str, region_code: str = None, proxy: dict = None, verify_ssl: bool = True):
        self.username = username
        self.password = password
        
        # Handle case where region_code is None
        if not region_code:
            logger.warning(f"No region code provided for {username}, authentication may fail")
            region_code = ""  # Empty string instead of None
        else:
            # Standardize region code for EUNE accounts
            if region_code.upper() in ['EUN1', 'EUNE']:
                region_code = 'EUNE'
                logger.info(f"Standardizing region code to EUNE for {username}")
            # Also make sure all region codes are in uppercase
            region_code = region_code.upper()
            
            # Ensure consistent region formatting
            region_standardization = {
                'EUW1': 'EUW',
                'NA1': 'NA',
                'EUN1': 'EUNE',
                'LA1': 'LAN',
                'LA2': 'LAS',
                'BR1': 'BR',
                'TR1': 'TR',
                'OCE1': 'OCE',
                'OC1': 'OCE',
                'JP1': 'JP'
            }
            
            if region_code in region_standardization:
                logger.info(f"Standardizing region from {region_code} to {region_standardization[region_code]} for {username}")
                region_code = region_standardization[region_code]
        
        self.region_code = region_code
        self.proxy = proxy
        self.verify_ssl = verify_ssl
        self.session = create_session(proxy, verify_ssl)
        self.access_token = None
        self.id_token = None
        self.entitlements_token = None
        self.userinfo = None
        self.refresh_token = None
        self.db_manager = DatabaseManager()
        self.last_error = None
        logger.info(f"Authenticator initialized for {username} in region {region_code}")

    def set_tokens(self, access_token: str, id_token: str, refresh_token: str = None):
        """Set tokens directly from GUI auth"""
        self.access_token = access_token
        self.id_token = id_token
        if refresh_token:
            self.refresh_token = refresh_token
        return True

    async def authenticate(self) -> bool:
        try:
            # If we already have tokens from GUI auth, use those
            if self.access_token and self.id_token:
                logger.info(f"Using existing tokens for {self.username}")
                # Check if we should extract region from existing tokens
                await self._extract_region_from_tokens()
                return True
                
            account_info = await self.db_manager.get_account(self.username)
            logger.info(f"Got account info for {self.username}: {account_info}")
            
            refresh_token = account_info.get("refresh_token") if account_info and account_info.get("password") == self.password else None
            logger.info(f"Using refresh token for {self.username}: {'yes' if refresh_token else 'no'}")

            # Try refresh token first if available
            if refresh_token:
                logger.info(f"Attempting to use refresh token for {self.username}")
                try:
                    access_token, id_token, refresh_token = await self.refresh_auth_tokens(refresh_token)
                    if access_token:  # Changed from checking both tokens to just access_token
                        # Set the tokens
                        self.access_token = access_token
                        self.id_token = id_token
                        self.refresh_token = refresh_token
                        
                        # Extract region from the new tokens 
                        await self._extract_region_from_tokens()
                        
                        # Update tokens in database
                        await self.db_manager.update_account(
                            username=self.username,
                            password=self.password,
                            refresh_token=refresh_token,
                            access_token=access_token,
                            id_token=id_token,
                            region=self.region_code
                        )
                        
                        # Validate the refreshed token with a test request
                        try:
                            logger.info(f"Validating refreshed token for {self.username}")
                            test_session = create_session(self.proxy, self.verify_ssl)
                            test_url = "https://auth.riotgames.com/userinfo"
                            test_headers = {
                                "Authorization": f"Bearer {access_token}",
                                "Accept": "application/json",
                                "Content-Type": "application/json"
                            }
                            
                            test_request = test_session.build_request("GET", test_url, headers=test_headers)
                            test_response = test_session.send(test_request)
                            
                            if test_response.status_code == 200:
                                logger.info(f"Refreshed token validated successfully for {self.username}")
                            else:
                                logger.warning(f"Refreshed token validation failed with status {test_response.status_code}: {test_response.text}")
                        except Exception as e:
                            logger.warning(f"Error validating refreshed token: {str(e)}")
                        
                        logger.info(f"Successfully refreshed token for {self.username}")
                        return True
                    else:
                        logger.error(f"Refresh token failed - no access token returned")
                        self.last_error = "refresh_token_failed"
                except Exception as e:
                    logger.error(f"Error refreshing token: {str(e)}")
                    self.last_error = f"refresh_error: {str(e)}"

            # Fall back to password auth with captcha if needed
            try:
                logger.info(f"Falling back to password auth for {self.username}")
                # Create a new session for the auth
                
                # Get tokens from captcha solver
                tokens_result = solve_captcha(self.username, self.password, self.session)
                logger.info(f"Solve captcha result: {bool(tokens_result)}")
                
                if tokens_result and tokens_result.get('access_token'):
                    self.access_token = tokens_result.get('access_token')
                    self.id_token = tokens_result.get('id_token')
                    # Store refresh token if available
                    if tokens_result.get('refresh_token'):
                        self.refresh_token = tokens_result.get('refresh_token')
                    
                    # Extract region from the tokens as early as possible
                    await self._extract_region_from_tokens()
                    
                    # Try to get userinfo, but handle case where account might be banned
                    try:
                        self.userinfo = await self.get_userinfo()
                        # For banned accounts, userinfo might be empty or invalid
                        if not self.userinfo:
                            logger.warning(f"Empty userinfo for {self.username}, account may be banned")
                            self.userinfo = "{}"  # Set to empty JSON object
                    except Exception as e:
                        logger.error(f"Failed to get userinfo for {self.username}: {str(e)}")
                        self.userinfo = "{}"  # Set to empty JSON object on exception
                    
                    # Update database with new tokens and correct region
                    await self.db_manager.update_account(
                        username=self.username,
                        password=self.password,
                        refresh_token=self.refresh_token or "",
                        access_token=self.access_token,
                        id_token=self.id_token,
                        region=self.region_code
                    )
                    
                    logger.info(f"Successfully authenticated {self.username} with password")
                    return True
                else:
                    logger.error(f"Password auth failed - no tokens returned")
                    self.last_error = "password_auth_failed"
                    return False
            except Exception as e:
                logger.error(f"Error in password auth: {str(e)}")
                self.last_error = f"password_auth_error: {str(e)}"
                return False
        
        except Exception as e:
            logger.error(f"Authentication failed: {str(e)}")
            self.last_error = f"authentication_error: {str(e)}"
            return False

    async def _extract_region_from_tokens(self):
        """Extract region information from JWT tokens as early as possible"""
        if not self.id_token and not self.access_token:
            logger.warning(f"No tokens available to extract region for {self.username}")
            return
            
        # Try to extract from id_token first
        if self.id_token:
            try:
                import base64
                import json
                
                # Check if it looks like a JWT token
                if not isinstance(self.id_token, str) or not self.id_token.startswith('eyJ') or self.id_token.count('.') < 2:
                    logger.warning(f"ID token doesn't appear to be a valid JWT for {self.username}")
                else:
                    # Split the token and get the payload
                    parts = self.id_token.split('.')
                    payload = parts[1]
                    # Add padding if needed
                    padding = len(payload) % 4
                    if padding:
                        payload += '=' * (4 - padding)
                    # Decode payload
                    decoded = base64.urlsafe_b64decode(payload)
                    payload_json = json.loads(decoded)
                    
                    # Map region codes to regions
                    region_mapping = {
                        'EUW1': 'EUW',
                        'NA1': 'NA',
                        'EUN1': 'EUNE',
                        'LA1': 'LAN',
                        'LA2': 'LAS',
                        'BR1': 'BR',
                        'TR1': 'TR',
                        'RU': 'RU',
                        'JP1': 'JP',
                        'KR': 'KR',
                        'OC1': 'OCE'
                    }
                    
                    # Check lol_region array first (most reliable for EUNE accounts)
                    if 'lol_region' in payload_json and payload_json['lol_region'] and isinstance(payload_json['lol_region'], list):
                        for region_entry in payload_json['lol_region']:
                            if isinstance(region_entry, dict) and 'cpid' in region_entry:
                                new_region = region_mapping.get(region_entry['cpid'], region_entry['cpid'][:2])
                                logger.info(f"Extracted region from JWT lol_region[].cpid: {new_region}")
                                if new_region != self.region_code:
                                    logger.info(f"Updating region from {self.region_code} to {new_region} based on JWT token")
                                    self.region_code = new_region
                                return
                    
                    # Then check lol field
                    if 'lol' in payload_json:
                        lol_info = payload_json.get('lol', {})
                        if isinstance(lol_info, list) and lol_info and isinstance(lol_info[0], dict) and 'cpid' in lol_info[0]:
                            jwt_region = lol_info[0]['cpid']
                            new_region = region_mapping.get(jwt_region, jwt_region[:2])
                            logger.info(f"Extracted region from JWT lol[0].cpid: {new_region}")
                            if new_region != self.region_code:
                                logger.info(f"Updating region from {self.region_code} to {new_region} based on JWT token")
                                self.region_code = new_region
                            return
                        elif isinstance(lol_info, dict) and 'cpid' in lol_info:
                            jwt_region = lol_info['cpid']
                            new_region = region_mapping.get(jwt_region, jwt_region[:2])
                            logger.info(f"Extracted region from JWT lol.cpid: {new_region}")
                            if new_region != self.region_code:
                                logger.info(f"Updating region from {self.region_code} to {new_region} based on JWT token")
                                self.region_code = new_region
                            return
                    
                    # Then check original_platform_id
                    if 'original_platform_id' in payload_json and payload_json['original_platform_id']:
                        platform_id = payload_json['original_platform_id']
                        new_region = region_mapping.get(platform_id, platform_id[:2])
                        logger.info(f"Extracted region from JWT original_platform_id: {new_region}")
                        if new_region != self.region_code:
                            logger.info(f"Updating region from {self.region_code} to {new_region} based on JWT token")
                            self.region_code = new_region
                        return
                    
                    # Check dat.r field in access_token if available
                    if 'dat' in payload_json and isinstance(payload_json['dat'], dict) and 'r' in payload_json['dat']:
                        jwt_region = payload_json['dat']['r']
                        new_region = region_mapping.get(jwt_region, jwt_region[:2])
                        logger.info(f"Extracted region from JWT dat.r: {new_region}")
                        if new_region != self.region_code:
                            logger.info(f"Updating region from {self.region_code} to {new_region} based on JWT token")
                            self.region_code = new_region
                        return
            except Exception as e:
                logger.error(f"Error extracting region from ID token for {self.username}: {str(e)}")
                
        # If we couldn't extract from id_token, try access_token
        if self.access_token:
            try:
                import base64
                import json
                
                # Check if it looks like a JWT token
                if not isinstance(self.access_token, str) or not self.access_token.startswith('eyJ') or self.access_token.count('.') < 2:
                    logger.warning(f"Access token doesn't appear to be a valid JWT for {self.username}")
                    return
                    
                # Split the token and get the payload
                parts = self.access_token.split('.')
                payload = parts[1]
                # Add padding if needed
                padding = len(payload) % 4
                if padding:
                    payload += '=' * (4 - padding)
                # Decode payload
                decoded = base64.urlsafe_b64decode(payload)
                payload_json = json.loads(decoded)
                
                # Map region codes to regions
                region_mapping = {
                    'EUW1': 'EUW',
                    'NA1': 'NA',
                    'EUN1': 'EUNE',
                    'LA1': 'LAN',
                    'LA2': 'LAS',
                    'BR1': 'BR',
                    'TR1': 'TR',
                    'RU': 'RU',
                    'JP1': 'JP',
                    'KR': 'KR',
                    'OC1': 'OCE'
                }
                
                # Check dat.r field in access_token if available
                if 'dat' in payload_json and isinstance(payload_json['dat'], dict) and 'r' in payload_json['dat']:
                    jwt_region = payload_json['dat']['r']
                    new_region = region_mapping.get(jwt_region, jwt_region[:2])
                    logger.info(f"Extracted region from access token dat.r: {new_region}")
                    if new_region != self.region_code:
                        logger.info(f"Updating region from {self.region_code} to {new_region} based on access token")
                        self.region_code = new_region
            except Exception as e:
                logger.error(f"Error extracting region from access token for {self.username}: {str(e)}")

    async def ensure_authenticated(self) -> bool:
        if not self.access_token:
            return await self.authenticate()
        return True

    async def get_userinfo(self):
        if not await self.ensure_authenticated():
            return None
        
        # If we already have userinfo, return it
        if self.userinfo:
            # If userinfo is a string, try to parse it as JSON
            if isinstance(self.userinfo, str):
                # Handle empty string or "{}" case
                if not self.userinfo.strip() or self.userinfo == "{}":
                    logger.info(f"Empty userinfo for {self.username}, likely a banned account")
                    return {}
                
                # If it looks like a JWT token, try to extract ban info directly
                if self.userinfo.startswith('eyJ') and self.userinfo.count('.') >= 2:
                    try:
                        import base64
                        import json
                        # Split the token and get the payload
                        parts = self.userinfo.split('.')
                        if len(parts) >= 2:
                            payload = parts[1]
                            # Add padding if needed
                            padding = len(payload) % 4
                            if padding:
                                payload += '=' * (4 - padding)
                            # Decode payload
                            decoded = base64.urlsafe_b64decode(payload)
                            payload_json = json.loads(decoded)
                            
                            # Check for ban info
                            if 'ban' in payload_json and 'restrictions' in payload_json['ban']:
                                restrictions = payload_json['ban']['restrictions']
                                if restrictions:
                                    ban_strings = []
                                    is_permanent_ban = False
                                    has_game_restriction = False
                                    
                                    for restriction in restrictions:
                                        ban_type = restriction.get('type', 'unknown')
                                        reason = restriction.get('reason', 'unknown')
                                        expires_at = restriction.get('dat', {}).get('expirationMillis', 0)
                                        
                                        # Check if it's a permanent ban (no expiration) or a chat restriction
                                        if expires_at and expires_at > 0:
                                            # Not a permanent ban since it has expiration
                                            logger.info(f"Restriction {ban_type} for {self.username} is temporary, expires at {expires_at}")
                                        else:
                                            # No expiration date indicates a permanent ban
                                            is_permanent_ban = True
                                            logger.warning(f"Restriction {ban_type} for {self.username} is permanent")
                                        
                                        # Check if it's a game restriction or just a chat restriction
                                        if ban_type != "TEXT_CHAT_RESTRICTION":
                                            has_game_restriction = True
                                            logger.warning(f"Restriction {ban_type} for {self.username} is a game restriction")
                                        
                                        logger.warning(f"Found ban info: {ban_type} - {reason}")
                                        ban_strings.append(f"{ban_type} - {reason}")
                                    
                                    # Only log as banned if it's permanent or a game restriction
                                    if is_permanent_ban or has_game_restriction:
                                        logger.warning(f"Account {self.username} is banned: {', '.join(ban_strings)}")
                                    else:
                                        logger.info(f"Account {self.username} has temporary chat restrictions: {', '.join(ban_strings)}")
                        
                        # Return the original JWT token
                        return self.userinfo
                    except Exception as e:
                        logger.error(f"Error decoding JWT directly in get_userinfo: {str(e)}")
                
                try:
                    import json
                    parsed = json.loads(self.userinfo)
                    self.userinfo = parsed
                    return parsed
                except Exception as e:
                    logger.error(f"Error parsing userinfo string for {self.username}: {str(e)}")
                    # Return empty dict if parsing fails
                    return {}
            return self.userinfo
        
        # Get userinfo from the supplier
        try:
            userinfo_data = get_userinfo(self.access_token, self.session)
            
            # If we got an empty response or error (likely 401), try re-authenticating
            if not userinfo_data or userinfo_data == "{}":
                logger.warning(f"Empty userinfo response for {self.username}, attempting re-auth")
                # Clear tokens to force re-auth
                self.access_token = None
                self.id_token = None
                self.entitlements_token = None
                
                # Try to authenticate again
                if await self.authenticate():
                    # Retry getting userinfo with new tokens
                    userinfo_data = get_userinfo(self.access_token, self.session)
                    if not userinfo_data or userinfo_data == "{}":
                        logger.warning(f"Still empty userinfo after re-auth for {self.username}, account might be banned")
                        self.userinfo = {}
                        return {}
                else:
                    logger.error(f"Re-authentication failed for {self.username}")
                    self.userinfo = {}
                    return {}
            
            # If userinfo is a string, try to parse it as JSON or JWT
            if isinstance(userinfo_data, str):
                # Skip parsing if string is empty
                if not userinfo_data.strip():
                    logger.info(f"Empty userinfo string from supplier for {self.username}")
                    self.userinfo = {}
                    return {}
                
                # If it looks like a JWT token, try to extract ban info directly
                if userinfo_data.startswith('eyJ') and userinfo_data.count('.') >= 2:
                    try:
                        import base64
                        import json
                        # Split the token and get the payload
                        parts = userinfo_data.split('.')
                        if len(parts) >= 2:
                            payload = parts[1]
                            # Add padding if needed
                            padding = len(payload) % 4
                            if padding:
                                payload += '=' * (4 - padding)
                            # Decode payload
                            decoded = base64.urlsafe_b64decode(payload)
                            payload_json = json.loads(decoded)
                            logger.info(f"Successfully decoded JWT payload with {len(payload_json)} keys")
                            
                            # Check for ban info
                            if 'ban' in payload_json and 'restrictions' in payload_json['ban']:
                                restrictions = payload_json['ban']['restrictions']
                                if restrictions:
                                    ban_strings = []
                                    is_permanent_ban = False
                                    has_game_restriction = False
                                    
                                    for restriction in restrictions:
                                        ban_type = restriction.get('type', 'unknown')
                                        reason = restriction.get('reason', 'unknown')
                                        expires_at = restriction.get('dat', {}).get('expirationMillis', 0)
                                        
                                        # Check if it's a permanent ban (no expiration) or a chat restriction
                                        if expires_at and expires_at > 0:
                                            # Not a permanent ban since it has expiration
                                            logger.info(f"Restriction {ban_type} for {self.username} is temporary, expires at {expires_at}")
                                        else:
                                            # No expiration date indicates a permanent ban
                                            is_permanent_ban = True
                                            logger.warning(f"Restriction {ban_type} for {self.username} is permanent")
                                        
                                        # Check if it's a game restriction or just a chat restriction
                                        if ban_type != "TEXT_CHAT_RESTRICTION":
                                            has_game_restriction = True
                                            logger.warning(f"Restriction {ban_type} for {self.username} is a game restriction")
                                        
                                        logger.warning(f"Found ban info: {ban_type} - {reason}")
                                        ban_strings.append(f"{ban_type} - {reason}")
                                    
                                    # Only log as banned if it's permanent or a game restriction
                                    if is_permanent_ban or has_game_restriction:
                                        logger.warning(f"Account {self.username} is banned: {', '.join(ban_strings)}")
                                    else:
                                        logger.info(f"Account {self.username} has temporary chat restrictions: {', '.join(ban_strings)}")
                        
                        # Store the original JWT token
                        self.userinfo = userinfo_data
                        return userinfo_data
                    except Exception as e:
                        logger.error(f"Error decoding JWT directly in get_userinfo: {str(e)}")
                    
                try:
                    import json
                    parsed = json.loads(userinfo_data)
                    self.userinfo = parsed
                    
                    # Extract region from userinfo if available
                    try:
                        region_data = parsed.get("region", {})
                        region_id = region_data.get("id", "")
                        if region_id and region_id != self.region_code:
                            logger.info(f"Updating region from userinfo: {region_id} (was {self.region_code})")
                            self.region_code = region_id
                    except Exception as e:
                        logger.error(f"Error extracting region from userinfo: {str(e)}")
                        
                    return parsed
                except Exception as e:
                    logger.error(f"Error parsing userinfo string for {self.username}: {str(e)}")
                    # Store but return empty dict if parsing fails
                    self.userinfo = userinfo_data
                    return userinfo_data
            else:
                self.userinfo = userinfo_data
                
                # Extract region from userinfo if available
                try:
                    region_data = userinfo_data.get("region", {})
                    region_id = region_data.get("id", "")
                    if region_id and region_id != self.region_code:
                        logger.info(f"Updating region from userinfo: {region_id} (was {self.region_code})")
                        self.region_code = region_id
                except Exception as e:
                    logger.error(f"Error extracting region from userinfo: {str(e)}")
                
                return userinfo_data
        except Exception as e:
            logger.error(f"Failed to get userinfo for {self.username}: {str(e)}")
            self.userinfo = {}
            return {}

    async def get_entitlements(self):
        if not await self.ensure_authenticated():
            return None
        self.entitlements_token = get_entitlement_token(self.access_token, self.session)
        return self.entitlements_token

    async def get_pas_token(self):
        if not await self.ensure_authenticated():
            return None
        return get_geopas_token(self.access_token, self.session)

    async def get_queue_token(self):
        if not await self.ensure_authenticated():
            return None
        if not self.entitlements_token:
            self.entitlements_token = await self.get_entitlements()
        if not self.userinfo:
            self.userinfo = await self.get_userinfo()
            
        # Extract region from userinfo if available
        try:
            # Handle case where userinfo is a string
            userinfo_obj = self.userinfo
            
            # If userinfo is empty or None, use an empty dict
            if not userinfo_obj:
                userinfo_obj = {}
                
            if isinstance(userinfo_obj, str):
                # Skip parsing if string is empty
                if not userinfo_obj.strip():
                    userinfo_obj = {}
                else:
                    try:
                        import json
                        userinfo_obj = json.loads(userinfo_obj)
                    except json.JSONDecodeError as e:
                        logger.warning(f"Invalid JSON in userinfo for get_queue_token: {str(e)}")
                        userinfo_obj = {}
                    except Exception as e:
                        logger.error(f"Error parsing userinfo string in get_queue_token: {str(e)}")
                        userinfo_obj = {}
                
            region_data = userinfo_obj.get("region", {})
            region_id = region_data.get("id", "")
            if region_id and region_id != self.region_code:
                logger.info(f"Using region from userinfo for queue token: {region_id} (was {self.region_code})")
                current_region_code = region_id
            else:
                current_region_code = self.region_code
        except Exception as e:
            logger.error(f"Error extracting region from userinfo for queue token: {str(e)}")
            current_region_code = self.region_code
            
        return get_queue_token(self.access_token, self.entitlements_token, self.userinfo, self.session, current_region_code)

    async def get_session_token(self, queue_token):
        if not await self.ensure_authenticated():
            return None
            
        # Extract region from userinfo if available
        try:
            # Handle case where userinfo is a string
            userinfo_obj = self.userinfo
            
            # If userinfo is empty or None, use an empty dict
            if not userinfo_obj:
                userinfo_obj = {}
                
            if isinstance(userinfo_obj, str):
                # Skip parsing if string is empty
                if not userinfo_obj.strip():
                    userinfo_obj = {}
                else:
                    try:
                        import json
                        userinfo_obj = json.loads(userinfo_obj)
                    except json.JSONDecodeError as e:
                        logger.warning(f"Invalid JSON in userinfo for get_session_token: {str(e)}")
                        userinfo_obj = {}
                    except Exception as e:
                        logger.error(f"Error parsing userinfo string in get_session_token: {str(e)}")
                        userinfo_obj = {}
                    
            region_data = userinfo_obj.get("region", {})
            region_id = region_data.get("id", "")
            if region_id and region_id != self.region_code:
                logger.info(f"Using region from userinfo for session token: {region_id} (was {self.region_code})")
                current_region_code = region_id
            else:
                current_region_code = self.region_code
        except Exception as e:
            logger.error(f"Error extracting region from userinfo for session token: {str(e)}")
            current_region_code = self.region_code
            
        return get_session_token(queue_token, self.access_token, current_region_code, self.session)

    async def get_sipt_token(self):
        session_token = await self.get_session_token()
        if not session_token:
            return None
        return get_sipt_token(session_token, self.session, self.region_code)

    async def get_region_data(self):
        return get_region(self.region_code)

    async def get_account_id(self):
        if not await self.ensure_authenticated():
            return None
        from results.access_token import AccessToken
        parsed = AccessToken.parse(self.access_token)
        return parsed.account_id if parsed else None

    async def refresh_auth_tokens(self, refresh_token: str, session: httpx.Client = None) -> tuple:
        if session is None:
            session = create_session()

        refresh_payload = {
            "client_id": "lol",
            "grant_type": "refresh_token",
            "refresh_token": refresh_token,
            "scope": "openid offline_access lol ban lol_region"
        }
        
        refresh_headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "User-Agent": "RiotClient/63.0.9.4909983.4789131 rso-auth (Windows;10;;Professional, x64)",
            "Accept": "application/json"
        }
        
        # Debug logging
        logger.info(f"REFRESH DEBUG - Payload: {refresh_payload}")
        logger.info(f"REFRESH DEBUG - Headers: {refresh_headers}")
        logger.info(f"REFRESH DEBUG - URL: https://auth.riotgames.com/token")
        logger.info(f"REFRESH DEBUG - Method: POST")
        
        try:
            refresh_request = session.build_request(
                "POST",
                "https://auth.riotgames.com/token",
                data=refresh_payload,
                headers=refresh_headers
            )
            refresh_response = session.send(refresh_request)
            
            # Log full response details
            logger.info(f"REFRESH DEBUG - Response Status: {refresh_response.status_code}")
            logger.info(f"REFRESH DEBUG - Response Body: {refresh_response.text}")
            logger.info(f"REFRESH DEBUG - Response Headers: {refresh_response.headers}")
            
            if refresh_response.status_code == 200:
                tokens = refresh_response.json()
                logger.info(f"Successfully refreshed tokens")
                return (
                    tokens.get('access_token'),
                    tokens.get('id_token'),
                    tokens.get('refresh_token')
                )
            else:
                logger.error(f"Refresh token failed with status {refresh_response.status_code}")
                logger.error(f"Response: {refresh_response.text}")
                return (None, None, None)
            
        except Exception as e:
            logger.error(f"Error refreshing token: {str(e)}")
            return (None, None, None)
