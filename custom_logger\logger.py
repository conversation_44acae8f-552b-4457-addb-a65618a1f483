import sys
from enum import Enum
from datetime import datetime

LOG_LEVEL_ENABLED = {
    'SUCCESS': True,
    'WARNING': True,
    'ERROR': True,
    'DEBUG': True,
    'INFO': True
}

class LogLevel(Enum):
    SUCCESS = ('+', 'cyan')
    WARNING = ('!', 'yellow')
    ERROR = ('-', 'red')
    DEBUG = ('@', 'gray')
    INFO = ('$', 'green')

class ConsoleLogger:
    COLORS = {
        'cyan': '\033[96m',
        'yellow': '\033[93m',
        'red': '\033[91m',
        'gray': '\033[90m',
        'green': '\033[92m',
        'white': '\033[97m',
        'reset': '\033[0m'
    }

    def _write(self, message, color):
        sys.stdout.write(f"{self.COLORS[color]}{message}{self.COLORS['reset']}")

    def _log(self, level: LogLevel, message):
        if LOG_LEVEL_ENABLED.get(level.name, False):
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            prefix, color = level.value
            self._write(f"[{timestamp}] [", "white")
            self._write(prefix, color)
            self._write(f"] {message}\n", "white")

    def success(self, message):
        self._log(LogLevel.SUCCESS, message)

    def warning(self, message):
        self._log(LogLevel.WARNING, message)

    def error(self, message):
        self._log(LogLevel.ERROR, message)

    def debug(self, message):
        self._log(LogLevel.DEBUG, message)

    def info(self, message):
        self._log(LogLevel.INFO, message)

def set_log_level_enabled(enabled: bool, level: str = None):
    """
    Enables or disables logging levels.
    Examples:
        set_log_level_enabled(True)   # Enable all logging
        set_log_level_enabled(False)  # Disable all logging
        set_log_level_enabled(False, 'DEBUG')  # Disable only DEBUG logging
    """
    if level is None:
        for key in LOG_LEVEL_ENABLED:
            LOG_LEVEL_ENABLED[key] = enabled
    else:
        level = level.upper()
        if level in LOG_LEVEL_ENABLED:
            LOG_LEVEL_ENABLED[level] = enabled
        else:
            raise ValueError(f"Invalid log level: {level}. Valid levels are: {list(LOG_LEVEL_ENABLED.keys())}")

logger = ConsoleLogger()
