import json
import httpx
from custom_logger.logger import logger
from config.regions import get_region
from typing import Tuple

async def check_leaver_penalty(session_token: str, region_code: str, session=None) -> Tuple[int, int]:
    """Check for leaver penalties (low priority queue and ranked restrictions)"""
    try:
        # Get region data and build correct URL
        region = get_region(region_code)
        if not region:
            logger.error(f"Invalid region code: {region_code}")
            return (0, 0)  # (penalty_minutes, ranked_games_remaining)
        
        # Special handling for NA region - try a different URL format
        if region_code == 'NA':
            # Try multiple URL formats for NA region
            urls_to_try = [
                "https://na-red.lol.sgp.pvp.net/lol-leaver-buster/v1/notifications",  # Format from logs
                "https://usw2-red.pp.sgp.pvp.net/leaverbuster-ledge/restrictionInfo",  # Current format
                f"{region.league_edge_url}/leaverbuster-ledge/restrictionInfo",       # Standard format
                "https://na-red.lol.sgp.pvp.net/leaverbuster-ledge/restrictionInfo"   # Alternative format
            ]
            
            logger.info(f"Trying multiple URLs for NA region penalty check")
            
            # Create a new httpx client if session is not provided
            close_client = False
            if session is None:
                session = httpx.Client(verify=False)
                close_client = True
            
            # Try each URL until one works
            for url in urls_to_try:
                try:
                    logger.info(f"Checking penalties for NA using URL: {url}")
                    
                    headers = {
                        "Authorization": f"Bearer {session_token}",
                        "Content-Type": "application/x-www-form-urlencoded",
                        "Accept": "application/json"
                    }
                    
                    # Use httpx client's request method
                    request = session.build_request(
                        "GET",
                        url,
                        headers=headers
                    )
                    response = session.send(request)
                    
                    if response.status_code == 200:
                        data = response.json()
                        logger.info(f"Penalty check response: {data}")
                        
                        # Get penalty time in milliseconds
                        if 'leaverBusterEntryDto' in data:
                            penalty_ms = data.get('leaverBusterEntryDto', {}).get('leaverPenalty', {}).get('delayTime', 0)
                            # Convert to minutes
                            penalty_minutes = penalty_ms / 60000
                            
                            # Get ranked restriction info
                            ranked_games = data.get('rankedRestrictionEntryDto', {}).get('restrictedGamesRemaining', 0)
                            
                            if penalty_minutes > 0:
                                logger.info(f"Found penalty: {penalty_minutes} minutes")
                            else:
                                logger.info("No active penalty")
                                
                            if ranked_games > 0:
                                logger.info(f"Found ranked restriction: {ranked_games} games remaining")
                                
                            # Close the client if we created it
                            if close_client:
                                session.close()
                                
                            return (int(penalty_minutes), ranked_games)
                    else:
                        logger.error(f"Failed to get penalty info from {url}: {response.status_code}")
                except Exception as e:
                    logger.error(f"Error checking penalties with URL {url}: {str(e)}")
            
            # If all URLs failed, return zeros
            logger.info(f"All penalty check URLs failed for NA region, returning zeros")
            
            # Close the client if we created it
            if close_client:
                session.close()
                
            return (0, 0)
        else:
            # Standard approach for non-NA regions
            url = f"{region.league_edge_url}/leaverbuster-ledge/restrictionInfo"
            logger.info(f"Checking penalties for {region_code} using URL: {url}")
            
            headers = {
                "Authorization": f"Bearer {session_token}",
                "Content-Type": "application/x-www-form-urlencoded",
                "Accept": "application/json"
            }
            
            # Create a new httpx client if session is not provided
            close_client = False
            if session is None:
                session = httpx.Client(verify=False)
                close_client = True
            
            # Use httpx client's request method
            request = session.build_request(
                "GET",
                url,
                headers=headers
            )
            response = session.send(request)
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"Penalty check response: {data}")
                
                # Get penalty time in milliseconds
                penalty_ms = data.get('leaverBusterEntryDto', {}).get('leaverPenalty', {}).get('delayTime', 0)
                # Convert to minutes
                penalty_minutes = penalty_ms / 60000
                
                # Get ranked restriction info
                ranked_games = data.get('rankedRestrictionEntryDto', {}).get('restrictedGamesRemaining', 0)
                
                if penalty_minutes > 0:
                    logger.info(f"Found penalty: {penalty_minutes} minutes")
                else:
                    logger.info("No active penalty")
                    
                if ranked_games > 0:
                    logger.info(f"Found ranked restriction: {ranked_games} games remaining")
                    
                # Close the client if we created it
                if close_client:
                    session.close()
                    
                return (int(penalty_minutes), ranked_games)
            else:
                logger.error(f"Failed to get penalty info: {response.status_code}")
                
                # Close the client if we created it
                if close_client:
                    session.close()
                    
                return (0, 0)
                    
    except Exception as e:
        logger.error(f"Error checking penalties: {str(e)}")
        
        # Close the client if we created it
        if 'close_client' in locals() and close_client and 'session' in locals() and session:
            try:
                session.close()
            except Exception as e:
                logger.error(f"Error closing httpx client: {str(e)}")
                
        return (0, 0) 