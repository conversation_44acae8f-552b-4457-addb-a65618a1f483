import logging
import json
from typing import Dict, List, Optional, Any

from shared.utils import format_time

logger = logging.getLogger(__name__)

class Account:
    def __init__(self, username, password, region=None, region_code=None, db_manager=None, **kwargs):
        """Initialize account"""
        self.username = username
        self.password = password
        self.region = region  # No default region
        self.region_code = region_code or region  # No default region_code
        self.id_token = kwargs.get('id_token')
        self.access_token = kwargs.get('access_token')
        self.refresh_token = kwargs.get('refresh_token')
        self.entitlements_token = kwargs.get('entitlements_token')
        self.userinfo = kwargs.get('userinfo')
        self.timestamp = kwargs.get('timestamp', 0)
        self.puuid = kwargs.get('puuid')
        self.account_id = kwargs.get('account_id')
        self.game_name = kwargs.get('game_name')
        self.tag_line = kwargs.get('tag_line')
        self.champion_count = kwargs.get('champion_count', 0)
        self.blue_essence = kwargs.get('blue_essence', 0)
        self.riot_points = kwargs.get('riot_points', 0)
        self.summoner_level = kwargs.get('summoner_level', 0)
        self.created_at = kwargs.get('created_at')
        self.last_checked = kwargs.get('last_checked')
        self.penalty_minutes = kwargs.get('penalty_minutes', 0)
        self.ranked_games_remaining = kwargs.get('ranked_games_remaining', 0)
        self.is_banned = kwargs.get('is_banned', False)
        self.ban_info = kwargs.get('ban_info')
        self.country = kwargs.get('country')
        self.creation_region = kwargs.get('creation_region')
        self.solo_tier = kwargs.get('solo_tier', 'UNRANKED')
        self.solo_division = kwargs.get('solo_division', '')
        self.solo_lp = kwargs.get('solo_lp', 0)
        self.solo_wins = kwargs.get('solo_wins', 0)
        self.solo_losses = kwargs.get('solo_losses', 0)
        self.solo_previous_tier = kwargs.get('solo_previous_tier', 'UNRANKED')
        self.solo_previous_division = kwargs.get('solo_previous_division', '')
        self.flex_tier = kwargs.get('flex_tier', 'UNRANKED')
        self.flex_division = kwargs.get('flex_division', '')
        self.flex_lp = kwargs.get('flex_lp', 0)
        self.flex_wins = kwargs.get('flex_wins', 0)
        self.flex_losses = kwargs.get('flex_losses', 0)
        self.flex_previous_tier = kwargs.get('flex_previous_tier', 'UNRANKED')
        self.flex_previous_division = kwargs.get('flex_previous_division', '')
        self.skins = kwargs.get('skins', [])
        self.authenticator = None
        self.season_history = kwargs.get('season_history', [])
        self.db_manager = db_manager
        self.champions = {}
        
        # Check tokens for region information right at initialization
        if self.id_token and (not region or region == 'NA') and 'decode_jwt_payload' in globals():
            try:
                from accounts.account_pool import decode_jwt_payload
                jwt_payload = decode_jwt_payload(self.id_token)
                
                # Region mapping
                region_mapping = {
                    'EUW1': 'EUW',
                    'NA1': 'NA',
                    'EUN1': 'EUNE',
                    'LA1': 'LAN',
                    'LA2': 'LAS',
                    'BR1': 'BR',
                    'TR1': 'TR',
                    'RU': 'RU',
                    'JP1': 'JP',
                    'KR': 'KR',
                    'OC1': 'OCE'
                }
                
                # Extract region from JWT payload
                if 'lol_region' in jwt_payload and jwt_payload['lol_region'] and isinstance(jwt_payload['lol_region'], list):
                    for region_entry in jwt_payload['lol_region']:
                        if isinstance(region_entry, dict) and 'cpid' in region_entry:
                            jwt_region = region_entry['cpid']
                            actual_region = region_mapping.get(jwt_region, jwt_region[:2])
                            if actual_region != self.region:
                                logger.info(f"Updating region from {self.region} to {actual_region} during Account initialization for {self.username}")
                                self.region = actual_region
                                self.region_code = actual_region
                            break
                
                # Standardize EUNE region
                if self.region == 'EUN1':
                    self.region = 'EUNE'
                    self.region_code = 'EUNE'
                    
            except Exception as e:
                logger.error(f"Error checking JWT token for region during Account initialization: {str(e)}")

    def init_authenticator(self):
        """Initialize authenticator if not already done"""
        # Just moved to account_pool.py for centralized implementation
        pass 