import asyncio
import sys
import threading
from PyQt6.QtCore import Q<PERSON>vent<PERSON>oop, QTimer, QUrl
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout

from custom_logger.logger import logger


async def get_solved_captcha_token(rqdata: str, sitekey: str, timeout: int = 300) -> str:
    logger.info("Solving captcha challenge using GUI...")
    loop = asyncio.get_running_loop()
    captcha_token = await loop.run_in_executor(None, solve_captcha_with_gui, sitekey, rqdata)
    if not captcha_token:
        #logger.error("Captcha solving failed. No token returned.")
        raise ValueError("Failed to receive the solved captcha token.")
    #logger.info(f"Captcha token received: {captcha_token}")
    return captcha_token

class CaptchaWindow:
    def __init__(self, sitekey: str, rqdata: str):
        self.sitekey = sitekey
        self.rqdata = rqdata
        self.token = None
        
        # Check if we're in the main thread
        self.is_main_thread = threading.current_thread() is threading.main_thread()
        
        # Create a new QApplication if needed
        if self.is_main_thread:
            self.app = QApplication.instance() or QApplication(sys.argv)
        else:
            # For non-main thread, we need to use a different approach
            # We'll create a new process to handle the captcha
            import subprocess
            import tempfile
            import os
            import json
            
            # Create a temporary script to handle the captcha
            fd, script_path = tempfile.mkstemp(suffix='.py', prefix='captcha_solver_')
            os.close(fd)
            
            with open(script_path, 'w') as f:
                f.write(f'''
import sys
from PyQt6.QtCore import QEventLoop, QTimer, QUrl
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout
import json

def solve_captcha():
    app = QApplication(sys.argv)
    window = QMainWindow()
    window.setWindowTitle("hCaptcha Verification")
    window.setGeometry(100, 100, 800, 800)
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    web_view = QWebEngineView()
    layout.addWidget(web_view)
    
    html_content = """
    <html>
    <head>
        <title>hCaptcha Verification</title>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <script src="https://js.hcaptcha.com/1/api.js?onload=hcaptchaOnLoad&render=explicit" async defer></script>
        <style>
            body {{
                margin: 0;
                padding: 20px;
                height: 100vh;
                display: flex;
                justify-content: center;
                align-items: center;
                background-color: #F9F9FA;
                font-family: Arial, sans-serif;
            }}
            .container {{
                background-color: white;
                padding: 30px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                text-align: center;
                max-width: 600px;
                width: 100%;
            }}
            h2 {{
                margin-top: 0;
                color: #333;
                margin-bottom: 20px;
            }}
            p {{
                color: #666;
                margin-bottom: 25px;
            }}
            #hcaptcha-container {{
                min-height: 100px;
                display: flex;
                justify-content: center;
            }}
            .instructions {{
                margin-top: 20px;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 4px;
                font-size: 14px;
                color: #555;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <h2>Captcha Verification Required</h2>
            <p>Please complete the captcha challenge to continue.</p>
            <div id="hcaptcha-container"></div>
            <div class="instructions">
                If the captcha doesn't appear, please click in the area above.
            </div>
        </div>
        
        <script>
            function hcaptchaOnLoad() {{
                const widgetId = hcaptcha.render('hcaptcha-container', {{
                    sitekey: "{self.sitekey}",
                    theme: 'light',
                    callback: onCaptchaSolved
                }});
                hcaptcha.setData(widgetId, {{ rqdata: "{self.rqdata}" }});
            }}

            function onCaptchaSolved(token) {{
                window.captchaToken = token;
                document.title = "CAPTCHA_SOLVED";
            }}
        </script>
    </body>
    </html>
    """
    
    token = [None]
    
    def check_title(title):
        if title == "CAPTCHA_SOLVED":
            web_view.page().runJavaScript(
                "window.captchaToken",
                lambda result: token.__setitem__(0, result)
            )
            QTimer.singleShot(500, loop.quit)
    
    web_view.titleChanged.connect(check_title)
    web_view.setHtml(html_content, QUrl("https://authenticate.riotgames.com"))
    window.show()
    
    loop = QEventLoop()
    QTimer.singleShot(300000, loop.quit)  # 5 minute timeout
    loop.exec()
    
    return token[0]

if __name__ == "__main__":
    token = solve_captcha()
    print(json.dumps({{"token": token}}))
''')
            
            # Run the script
            try:
                result = subprocess.run(
                    [sys.executable, script_path],
                    capture_output=True,
                    text=True,
                    timeout=300  # 5 minute timeout
                )
                
                # Parse the result
                if result.returncode == 0:
                    try:
                        output = json.loads(result.stdout)
                        self.token = output.get("token")
                    except json.JSONDecodeError:
                        logger.error(f"Failed to parse captcha solver output: {result.stdout}")
                else:
                    logger.error(f"Captcha solver process failed with code {result.returncode}")
                    logger.error(f"Stderr: {result.stderr}")
            except subprocess.TimeoutExpired:
                logger.error("Captcha solving timed out")
            except Exception as e:
                logger.error(f"Error solving captcha: {e}")
            
            # Clean up
            try:
                os.remove(script_path)
            except:
                pass
            
            return
        
        self.window = QMainWindow()
        self.window.setWindowTitle("hCaptcha Verification")
        self.window.setGeometry(100, 100, 800, 800)
        central_widget = QWidget()
        self.window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)

    def create_html_content(self) -> str:
        return f"""
        <html>
        <head>
            <title>hCaptcha Verification</title>
            <meta charset="UTF-8" />
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <script src="https://js.hcaptcha.com/1/api.js?onload=hcaptchaOnLoad&render=explicit" async defer></script>
            <style>
                body {{
                    margin: 0;
                    padding: 20px;
                    height: 100vh;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background-color: #F9F9FA;
                    font-family: Arial, sans-serif;
                }}
                .container {{
                    background-color: white;
                    padding: 30px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    text-align: center;
                    max-width: 600px;
                    width: 100%;
                }}
                h2 {{
                    margin-top: 0;
                    color: #333;
                    margin-bottom: 20px;
                }}
                p {{
                    color: #666;
                    margin-bottom: 25px;
                }}
                #hcaptcha-container {{
                    min-height: 100px;
                    display: flex;
                    justify-content: center;
                }}
                .instructions {{
                    margin-top: 20px;
                    padding: 15px;
                    background-color: #f8f9fa;
                    border-radius: 4px;
                    font-size: 14px;
                    color: #555;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <h2>Captcha Verification Required</h2>
                <p>Please complete the captcha challenge to continue.</p>
                <div id="hcaptcha-container"></div>
                <div class="instructions">
                    If the captcha doesn't appear, please click in the area above.
                </div>
            </div>
            
            <script>
                function hcaptchaOnLoad() {{
                    const widgetId = hcaptcha.render('hcaptcha-container', {{
                        sitekey: "{self.sitekey}",
                        theme: 'light',
                        callback: onCaptchaSolved
                    }});
                    hcaptcha.setData(widgetId, {{ rqdata: "{self.rqdata}" }});
                }}

                function onCaptchaSolved(token) {{
                    window.captchaToken = token;
                    document.title = "CAPTCHA_SOLVED";
                }}
            </script>
        </body>
        </html>
        """

    def get_captcha_token(self) -> str:
        # If we're not in the main thread, the token should already be set
        if not self.is_main_thread:
            return self.token
            
        html_content = self.create_html_content()
        self.web_view.setHtml(html_content, QUrl("https://authenticate.riotgames.com"))
        
        loop = QEventLoop()
        
        def check_token():
            self.web_view.page().runJavaScript(
                "window.captchaToken",
                lambda result: self.token_callback(result, loop)
            )
        
        def check_title(title):
            if title == "CAPTCHA_SOLVED":
                check_token()
        
        self.web_view.titleChanged.connect(check_title)
        self.window.show()
        
        # Set a timeout
        QTimer.singleShot(300000, loop.quit)  # 5 minute timeout
        
        # Start the event loop
        loop.exec()
        
        return self.token
    
    def token_callback(self, token, loop):
        if token:
            self.token = token
            QTimer.singleShot(500, loop.quit)

def solve_captcha_with_gui(sitekey: str, rqdata: str) -> str:
    captcha_window = CaptchaWindow(sitekey, rqdata)
    return captcha_window.get_captcha_token()