from typing import Dict


class Region:
    def __init__(self, name: str, region_upper: str, region_lower: str, league_edge_url: str,
                 player_platform_edge_url: str, xmpp_url: str, rms_url: str, chat_tag: str, rtmp_host: str, rtmp_port: str):
        self.name = name
        self.region_upper = region_upper
        self.region_lower = region_lower
        self.league_edge_url = league_edge_url
        self.player_platform_edge_url = player_platform_edge_url
        self.xmpp_url = xmpp_url
        self.rms_url = rms_url
        self.chat_tag = chat_tag
        self.rtmp_host = rtmp_host
        self.rtmp_port = rtmp_port


def get_region(region_code: str) -> Region:
    """Get region object by region code"""
    # CRITICAL FIX: Handle None values
    if region_code is None:
        region_code = 'EUW'
    
    try:
        # Ensure region_code is a string and uppercase
        region_code = str(region_code).strip().upper()
        
        # Use the existing regions dictionary
        return regions.get(region_code, regions.get('EUW'))
    except Exception as e:
        # If any error occurs, default to EUW
        return regions.get('EUW')


regions: Dict[str, Region] = {
    "BR": Region(
        name="Brazil",
        region_upper="BR1",
        region_lower="br1",
        league_edge_url="https://br-red.lol.sgp.pvp.net",
        player_platform_edge_url="https://usw2-red.pp.sgp.pvp.net",
        xmpp_url="br.chat.si.riotgames.com",
        rms_url="eu.edge.rms.si.riotgames.com",
        chat_tag="br1",
        rtmp_host="feapp.br1.lol.pvp.net",
        rtmp_port="2099"
    ),
    "EUW": Region(
        name="Europe West",
        region_upper="EUW1",
        region_lower="euw1",
        league_edge_url="https://euw-red.lol.sgp.pvp.net",
        player_platform_edge_url="https://euc1-red.pp.sgp.pvp.net",
        xmpp_url="euw1.chat.si.riotgames.com",
        rms_url="eu.edge.rms.si.riotgames.com",
        chat_tag="eu1",
        rtmp_host="feapp.euw1.lol.pvp.net",
        rtmp_port="2099"
    ),
    "EUNE": Region(
        name="Europe Nordic & East",
        region_upper="EUN1",
        region_lower="eun1",
        league_edge_url="https://eune-red.lol.sgp.pvp.net",
        player_platform_edge_url="https://euc1-red.pp.sgp.pvp.net",
        xmpp_url="eun1.chat.si.riotgames.com",
        rms_url="eu.edge.rms.si.riotgames.com",
        chat_tag="eu1",
        rtmp_host="feapp.eun1.lol.pvp.net",
        rtmp_port="2099"
    ),
    "JP": Region(
        name="Japan",
        region_upper="JP1",
        region_lower="jp1",
        league_edge_url="https://jp-red.lol.sgp.pvp.net",
        player_platform_edge_url="https://apne1-red.pp.sgp.pvp.net",
        xmpp_url="jp1.chat.si.riotgames.com",
        rms_url="eu.edge.rms.si.riotgames.com",
        chat_tag="jp1",
        rtmp_host="feapp.jp1.lol.pvp.net",
        rtmp_port="2099"
    ),
    "LA1": Region(
        name="Latin America North",
        region_upper="LA1",
        region_lower="la1",
        league_edge_url="https://las-red.lol.sgp.pvp.net",
        player_platform_edge_url="https://usw2-green.pp.sgp.pvp.net",
        xmpp_url="la1.chat.si.riotgames.com",
        rms_url="eu.edge.rms.si.riotgames.com",
        chat_tag="la1",
        rtmp_host="feapp.la1.lol.pvp.net",
        rtmp_port="2099"
    ),
    "LA2": Region(
        name="Latin America South",
        region_upper="LA2",
        region_lower="la2",
        league_edge_url="https://lan-red.lol.sgp.pvp.net",
        player_platform_edge_url="https://usw2-green.pp.sgp.pvp.net",
        xmpp_url="la1.chat.si.riotgames.com",
        rms_url="eu.edge.rms.si.riotgames.com",
        chat_tag="la2",
        rtmp_host="feapp.la2.lol.pvp.net",
        rtmp_port="2099"
    ),
    "NA": Region(
        name="North America",
        region_upper="NA1",
        region_lower="na1",
        league_edge_url="https://usw2-red.pp.sgp.pvp.net",
        player_platform_edge_url="https://usw2-red.pp.sgp.pvp.net",
        xmpp_url="na2.chat.si.riotgames.com",
        rms_url="eu.edge.rms.si.riotgames.com",
        chat_tag="na1",
        rtmp_host="feapp.na1.lol.pvp.net",
        rtmp_port="2099"
    ),
    "OC1": Region(
        name="Oceania",
        region_upper="OC1",
        region_lower="oc1",
        league_edge_url="https://oce-red.lol.sgp.pvp.net",
        player_platform_edge_url="https://usw2-green.pp.sgp.pvp.net",
        xmpp_url="oc1.chat.si.riotgames.com",
        rms_url="eu.edge.rms.si.riotgames.com",
        chat_tag="oc1",
        rtmp_host="feapp.oc1.lol.pvp.net",
        rtmp_port="2099"
    ),
    "RU": Region(
        name="Russia",
        region_upper="RU",
        region_lower="ru",
        league_edge_url="https://ru-red.lol.sgp.pvp.net",
        player_platform_edge_url="https://euc1-green.pp.sgp.pvp.net",
        xmpp_url="ru1.chat.si.riotgames.com",
        rms_url="eu.edge.rms.si.riotgames.com",
        chat_tag="ru1",
        rtmp_host="feapp.ru.lol.pvp.net",
        rtmp_port="2099"
    ),
    "TR": Region(
        name="Turkey",
        region_upper="TR1",
        region_lower="tr1",
        league_edge_url="https://tr-red.lol.sgp.pvp.net",
        player_platform_edge_url="https://euc1-green.pp.sgp.pvp.net",
        xmpp_url="tr1.chat.si.riotgames.com",
        rms_url="eu.edge.rms.si.riotgames.com",
        chat_tag="tr1",
        rtmp_host="idk",
        rtmp_port="2099"
    ),
}
