#!/usr/bin/env python3
"""
Quick test for the Fizz chroma issue
"""

import sys
import os
import json

# Add the accounts directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'accounts'))

from account_pool import AccountPool

def test_fizz_chroma():
    """Test the specific Fizz chroma that was showing as 'Fizz (Chroma)'"""
    print("=== Testing Fizz Chroma Issue ===")

    # Create an AccountPool instance
    pool = AccountPool()

    # Test multiple problematic chromas from the logs
    test_cases = [
        (105005, "Fizz (Scorch)"),  # Fizz Scorch chroma
        (107019, "Pretty Kitty Rengar (Chroma)"),  # Pretty Kitty Rengar chroma
        (19018, "<PERSON> Warwick (Chroma)"),  # Grey Warwick chroma
        (37057, "Victorious Sona (Bronze)"),  # Victorious Sona Bronze
        (37058, "Victorious Sona (Silver)"),  # Victorious Sona Silver
        (37059, "Victorious Sona (Gold)"),  # Victorious Sona Gold
    ]

    for skin_id, expected_name in test_cases:
        print(f"\n--- Testing Skin ID: {skin_id} ---")

        # Test the skin info
        skin_info = pool._get_skin_info(skin_id)
        actual_name = skin_info['name']

        print(f"Expected: {expected_name}")
        print(f"Actual:   {actual_name}")
        print(f"Success:  {actual_name == expected_name}")

        # Test chroma detection
        is_chroma = pool._is_chroma(actual_name, skin_id)
        print(f"Is Chroma: {is_chroma}")

if __name__ == "__main__":
    test_fizz_chroma()
