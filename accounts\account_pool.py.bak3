from typing import Dict, List, Optional, Any
from database.manager import DatabaseManager
from league_api.auth import Authenticator
from custom_logger.logger import logger
from dataclasses import dataclass
from datetime import datetime
import base64
import json
from league_api.penalties import check_leaver_penalty
from league_api.rank import get_rank_info
import socket
import os
import asyncio
import logging
import aiohttp
from bs4 import BeautifulSoup
from pathlib import Path
import requests

def decode_jwt_payload(jwt_token: str) -> dict:
    """Decode the payload part of a JWT token"""
    try:
        # Split the token into parts
        parts = jwt_token.split('.')
        if len(parts) != 3:
            return {}
            
        # Get the payload (middle part)
        payload = parts[1]
        
        # Add padding if needed
        padding = len(payload) % 4
        if padding:
            payload += '=' * (4 - padding)
            
        # Decode base64
        decoded = base64.b64decode(payload)
        
        # Parse JSON
        return json.loads(decoded)
    except Exception as e:
        logger.error(f"Error decoding JWT: {str(e)}")
        return {}

@dataclass
class Account:
    username: str
    password: str
    region: str
    region_code: str
    game_name: str = ""
    tag_line: str = ""
    summoner_level: int = 0
    blue_essence: int = 0
    riot_points: int = 0
    champions_owned: int = 0
    last_checked: str = ""
    created_at: str = ""
    penalty_minutes: int = 0
    is_banned: bool = False
    ban_info: str = ""
    ranked_games_remaining: int = 0
    rank_info: dict = None
    country: str = ""
    creation_region: str = ""
    authenticator: Optional[Authenticator] = None
    note: str = ""

    def __post_init__(self):
        # Initialize rank_info with default values
        if self.rank_info is None:
            self.rank_info = {
                'solo': {'tier': 'UNRANKED', 'division': '', 'lp': 0, 'wins': 0, 'losses': 0, 
                        'previous_tier': 'UNRANKED', 'previous_division': ''},
                'flex': {'tier': 'UNRANKED', 'division': '', 'lp': 0, 'wins': 0, 'losses': 0, 
                        'previous_tier': 'UNRANKED', 'previous_division': ''},
                'season_history': []
            }
        elif 'season_history' not in self.rank_info:
            # Ensure season_history exists even if rank_info was provided
            self.rank_info['season_history'] = []

    @property
    def ign(self) -> str:
        """Get formatted in-game name"""
        if self.game_name and self.tag_line:
            return f"{self.game_name}#{self.tag_line}"
        return self.username

    def init_authenticator(self):
        """Initialize authenticator if not already done"""
        if not self.authenticator:
            self.authenticator = Authenticator(
                username=self.username,
                password=self.password,
                region_code=self.region,
                verify_ssl=False
            )

class AccountPool:
    def __init__(self):
        self.db_manager = DatabaseManager()
        self._accounts = []
        self.skin_database = self._load_skin_database()

    def _load_skin_database(self) -> Dict[int, Dict[str, str]]:
        """Load skin database from JSON file or download from Community Dragon API if needed"""
        try:
            # Path to the skin database file
            json_db_path = 'data/skin_database.json'
            
            # Create data directory if it doesn't exist
            data_dir = Path('data')
            data_dir.mkdir(exist_ok=True)
            
            # Check if the database file exists
            if os.path.exists(json_db_path):
                # Load the existing database
                with open(json_db_path, 'r', encoding='utf-8') as f:
                    skin_db = json.load(f)
                logger.info(f"Loaded {len(skin_db)} skin entries from JSON database")
                return skin_db
            else:
                # Database file doesn't exist, download from Community Dragon API
                logger.info("Skin database file not found, downloading from Community Dragon API...")
                
                # URL for the Community Dragon skins.json file
                url = "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/skins.json"
                
                # Download the skins data
                response = requests.get(url)
                response.raise_for_status()  # Raise an exception for HTTP errors
                
                # Parse the JSON data
                skins_data = response.json()
                logger.info(f"Downloaded data for {len(skins_data)} skins from Community Dragon API")
                
                # Process the data into our format
                skin_db = {}
                for skin_id, skin_info in skins_data.items():
                    # Extract the champion name from the skin name or path
                    champion_name = "Unknown"
                    
                    # The champion name is often in the path
                    if "Characters" in skin_info.get("splashPath", ""):
                        path_parts = skin_info.get("splashPath", "").split("/")
                        for i, part in enumerate(path_parts):
                            if part == "Characters" and i + 1 < len(path_parts):
                                champion_name = path_parts[i + 1]
                                break
                    
                    # For base skins, the name is usually just the champion name
                    if skin_info.get("isBase", False) and skin_info.get("name", ""):
                        champion_name = skin_info.get("name", "Unknown")
                    
                    # Store in our database format
                    skin_db[skin_id] = {
                        "name": skin_info.get("name", f"Unknown Skin ({skin_id})"),
                        "champion": champion_name,
                        "rarity": skin_info.get("rarity", "Unknown")
                    }
                
                # Save the database to file
                with open(json_db_path, 'w', encoding='utf-8') as f:
                    json.dump(skin_db, f, indent=2, ensure_ascii=False)
                
                logger.info(f"Successfully created skin database with {len(skin_db)} entries")
                return skin_db
            
            # Fall back to the old translation file if JSON download fails
            translation_path = 'skin translation.txt'
            if os.path.exists(translation_path):
                with open(translation_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # Extract all tuples from the content
                    import ast
                    # Find all tuples in the format ("id", "name")
                    translations_str = content.replace("translations = {", "{")
                    translations = ast.literal_eval(translations_str)
                    
                    skin_db = {}
                    for skin_id, skin_name in translations:
                        # Some entries might be variant discounts or special cases
                        if skin_id.startswith("9"):  # Skip variant discounts and special cases
                            continue
                            
                        # For regular skins
                        try:
                            skin_id_int = int(skin_id)
                            # If skin name contains " / ", take the first part
                            if " / " in skin_name:
                                skin_name = skin_name.split(" / ")[0]
                            
                            # Try to extract champion name from skin name
                            # Most skin names are in format "Skin Name ChampionName" or "ChampionName Skin Name"
                            words = skin_name.split()
                            champion_name = words[-1]  # Default to last word as champion name
                            
                            # Store in database
                            skin_db[str(skin_id_int)] = {
                                'name': skin_name,
                                'champion': champion_name,
                                'rarity': 'Unknown'  # You can add rarity if available
                            }
                        except ValueError:
                            continue  # Skip if skin_id can't be converted to int
                    
                    logger.info(f"Loaded {len(skin_db)} skin translations from text file")
                    return skin_db
            else:
                logger.warning("No skin database found (neither JSON nor text file)")
                return {}
        except Exception as e:
            logger.error(f"Error loading skin database: {str(e)}")
            return {}

    async def _load_accounts(self):
        """Load accounts from database"""
        try:
            accounts_data = await self.db_manager.get_all_accounts()
            self._accounts = []
        
            for account_data in accounts_data:
                try:
                    # Log detailed account data for debugging
                    logger.info(f"Loading account {account_data.get('username')} from database")
                    logger.info(f"Account details: BE={account_data.get('blue_essence')}, RP={account_data.get('riot_points')}, Level={account_data.get('summoner_level')}")
                    logger.info(f"Account info: created_at={account_data.get('created_at')}, creation_region={account_data.get('creation_region')}, country={account_data.get('country')}")
                    logger.info(f"Game info: game_name={account_data.get('game_name')}, tag_line={account_data.get('tag_line')}")
                    
                    # Create rank_info dictionary
                    rank_info = {
                        'solo': {
                            'tier': account_data.get('solo_tier', 'UNRANKED'),
                            'division': account_data.get('solo_division', ''),
                            'lp': account_data.get('solo_lp', 0),
                            'wins': account_data.get('solo_wins', 0),
                            'losses': account_data.get('solo_losses', 0),
                            'previous_tier': account_data.get('solo_previous_tier', 'UNRANKED'),
                            'previous_division': account_data.get('solo_previous_division', '')
                        },
                        'flex': {
                            'tier': account_data.get('flex_tier', 'UNRANKED'),
                            'division': account_data.get('flex_division', ''),
                            'lp': account_data.get('flex_lp', 0),
                            'wins': account_data.get('flex_wins', 0),
                            'losses': account_data.get('flex_losses', 0),
                            'previous_tier': account_data.get('flex_previous_tier', 'UNRANKED'),
                            'previous_division': account_data.get('flex_previous_division', '')
                        }
                    }
                    
                    # Add season history if available
                    if 'rank_info' in account_data and 'season_history' in account_data['rank_info']:
                        rank_info['season_history'] = account_data['rank_info']['season_history']
                        logger.info(f"Loaded {len(account_data['rank_info']['season_history'])} season history entries for {account_data.get('username')}")
                    
                    # Get region and region_code
                    region = account_data.get('region', '')
                    # Ensure region_code is set properly
                    region_code = account_data.get('region_code')
                    if not region_code and region:
                        # If region_code is not set but region is, derive region_code from region
                        region_code = region.upper()[:3] if region else 'EUW'
                    elif not region_code:
                        # If neither is set, default to EUW instead of NA
                        region_code = 'EUW'
                    
                    logger.info(f"Using region={region}, region_code={region_code} for account {account_data.get('username')}")
                    
                    # Create account object
                    account = Account(
                        username=account_data.get('username'),
                        password=account_data.get('password', ''),
                        region=region,
                        region_code=region_code,
                        game_name=account_data.get('game_name', ''),
                        tag_line=account_data.get('tag_line', ''),
                        summoner_level=account_data.get('summoner_level', 0),
                        blue_essence=account_data.get('blue_essence', 0),
                        riot_points=account_data.get('riot_points', 0),
                        champions_owned=account_data.get('champion_count', 0),
                        last_checked=account_data.get('last_checked', ''),
                        created_at=account_data.get('created_at', ''),
                        penalty_minutes=account_data.get('penalty_minutes', 0),
                        is_banned=bool(account_data.get('is_banned', False)),
                        ban_info=account_data.get('ban_info', ''),
                        ranked_games_remaining=account_data.get('ranked_games_remaining', 0),
                        rank_info=rank_info,
                        country=account_data.get('country', ''),
                        creation_region=account_data.get('creation_region', '')
                    )
                    
                    # Set tokens if available
                    if account_data.get('refresh_token'):
                        account.refresh_token = account_data.get('refresh_token')
                    if account_data.get('access_token'):
                        account.access_token = account_data.get('access_token')
                    if account_data.get('id_token'):
                        account.id_token = account_data.get('id_token')
                    
                    self._accounts.append(account)
                    
                except Exception as e:
                    logger.error(f"Error loading account {account_data.get('username')}: {str(e)}")
            
            logger.info(f"Loaded {len(self._accounts)} accounts from database")
            return self._accounts
        except Exception as e:
            logger.error(f"Error loading accounts: {str(e)}")
            return []

    def get_all_accounts(self) -> List[Account]:
        """Get all accounts"""
        return self._accounts

    async def get_account_credentials(self, region: str) -> List[Account]:
        """Get accounts for specific region"""
        return [acc for acc in self._accounts if acc.region == region]

    async def check_account(self, account):
        """Check account status and update database"""
        try:
            logger.info(f"Starting check for account {account.username} with region {account.region}")
            
            # Get userinfo
            logger.info(f"Getting userinfo for {account.username}")
            userinfo = await account.authenticator.get_userinfo()
            if not userinfo:
                logger.error(f"Failed to get userinfo for {account.username}")
                return
                
            # Decode userinfo and get tokens
            logger.info(f"Got userinfo for {account.username}, decoding JWT")
            decoded_info = decode_jwt_payload(userinfo)
            
            # Check ban status
            ban_info = decoded_info.get('ban', {}).get('restrictions', [])
            if ban_info:
                # Account is banned
                logger.warning(f"Account {account.username} is banned")
                
                # Get ban reasons
                ban_reasons = []
                for restriction in ban_info:
                    type_id = restriction.get('type')
                    reason = restriction.get('reason', 'No reason provided')
                    expires_at = restriction.get('expirationMillis', 0)
                    
                    # Format expiration date if available
                    expiration_str = 'Permanent'
                    if expires_at and expires_at > 0:
                        try:
                            expiration_date = datetime.fromtimestamp(expires_at / 1000)
                            expiration_str = expiration_date.strftime("%Y-%m-%d %H:%M:%S")
                        except Exception:
                            expiration_str = f"Timestamp: {expires_at}"
                    
                    ban_reasons.append(f"Type: {type_id}, Reason: {reason}, Expires: {expiration_str}")
                
                # Update account info
                account.is_banned = True
                account.ban_info = "; ".join(ban_reasons)
                
                # Save to database
                await self.db_manager.save_account_info(
                    account.username,
                    account.region,
                    {
                        'is_banned': True,
                        'ban_info': account.ban_info,
                        'last_checked': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                )
                return

            # Get tokens
            try:
                logger.info(f"Getting tokens for {account.username}")
                queue_token = await account.authenticator.get_queue_token()
                session_token = await account.authenticator.get_session_token(queue_token)
                entitlements_token = await account.authenticator.get_entitlements()
                logger.info(f"Got all tokens for {account.username}")
                
                # Get champion data
                try:
                    logger.info(f"Importing get_owned_champions for {account.username}")
                    from main import get_owned_champions
                    
                    # Determine region code for champions
                    region_code_for_champions = account.region_code
                    
                    # If region_code is not set, try to get it from the JWT token
                    if not region_code_for_champions:
                        # Try to get region from JWT token
                        jwt_region = decoded_info.get('dat', {}).get('r', '')
                        if jwt_region:
                            # Map region codes to regions
                            region_mapping = {
                                'EUW1': 'EUW',
                                'NA1': 'NA',
                                'EUN1': 'EUNE',
                                'LA1': 'LAN',
                                'LA2': 'LAS',
                                'BR1': 'BR',
                                'TR1': 'TR',
                                'RU': 'RU',
                                'JP1': 'JP',
                                'KR': 'KR',
                                'OC1': 'OCE'
                            }
                            region_code_for_champions = region_mapping.get(jwt_region, account.region)
                            logger.info(f"Using region code from JWT token: {region_code_for_champions} for {account.username}")
                        else:
                            # If not in JWT, use account.region
                            region_code_for_champions = account.region
                            logger.info(f"Using account region for champion data retrieval: {region_code_for_champions} for {account.username}")
                    else:
                        logger.info(f"Using existing region code for champion data retrieval: {region_code_for_champions} for {account.username}")
                    
                    logger.info(f"Calling get_owned_champions for {account.username} with region {region_code_for_champions}")
                    
                    # Get champion data
                    champion_data = await get_owned_champions(
                        session_token=session_token,
                        entitlements_token=entitlements_token,
                        puuid=decoded_info.get('sub', ''),
                        account_id=str(decoded_info.get('original_account_id', '')),
                        region_code=region_code_for_champions
                    )
                    logger.info(f"Returned from get_owned_champions for {account.username}")
                    
                    # Update account info
                    account.summoner_level = decoded_info.get('lol_account', {}).get('summoner_level', 0)
                    account.game_name = decoded_info.get('acct', {}).get('game_name', account.username)
                    account.tag_line = decoded_info.get('acct', {}).get('tag_line', '')
                    account.blue_essence = champion_data.get('blue_essence', 0)
                    account.riot_points = champion_data.get('riot_points', 0)
                    account.champions_owned = len(champion_data.get('champions', []))
                    account.last_checked = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    account.country = decoded_info.get('country', '')
                    
                    # Extract creation date and region
                    acct_info = decoded_info.get('acct', {})
                    created_at_timestamp = acct_info.get('created_at')
                    created_at = None
                    if created_at_timestamp:
                        try:
                            # Convert timestamp to readable date
                            created_at = datetime.fromtimestamp(created_at_timestamp / 1000).strftime("%Y-%m-%d %H:%M:%S")
                            logger.info(f"Extracted creation date for {account.username}: {created_at} from timestamp {created_at_timestamp}")
                        except Exception as e:
                            logger.error(f"Error converting creation timestamp: {str(e)}")
                    else:
                        # Try to get creation date from country_at as fallback
                        country_at = decoded_info.get('country_at')
                        if country_at:
                            try:
                                created_at = datetime.fromtimestamp(country_at / 1000).strftime("%Y-%m-%d %H:%M:%S")
                                logger.info(f"Using country_at as fallback for creation date for {account.username}: {created_at}")
                            except Exception as e:
                                logger.error(f"Error converting country_at timestamp: {str(e)}")
                    
                    # Get creation region from region info
                    creation_region = account.region  # Default to current region
                    region_info = decoded_info.get('region', {})
                    if region_info and isinstance(region_info, dict):
                        region_tag = region_info.get('tag', '')
                        if region_tag:
                            # Map region tag to region code
                            region_tag_mapping = {
                                'na': 'NA',
                                'euw': 'EUW',
                                'eune': 'EUNE',
                                'br': 'BR',
                                'lan': 'LAN',
                                'las': 'LAS',
                                'oce': 'OCE',
                                'tr': 'TR',
                                'ru': 'RU',
                                'jp': 'JP',
                                'kr': 'KR'
                            }
                            creation_region = region_tag_mapping.get(region_tag.lower(), creation_region)
                            logger.info(f"Extracted creation region for {account.username}: {creation_region} from tag {region_tag}")
                    
                    account.created_at = created_at
                    account.creation_region = creation_region
                    
                    # Create a comprehensive account info dictionary to save to database
                    account_info = {
                        'summoner_level': account.summoner_level,
                        'game_name': account.game_name,
                        'tag_line': account.tag_line,
                        'blue_essence': account.blue_essence,
                        'riot_points': account.riot_points,
                        'champion_count': account.champions_owned,
                        'last_checked': account.last_checked,
                        'country': account.country,
                        'creation_region': account.creation_region,
                        'created_at': account.created_at
                    }
                    
                    # Save account info to database
                    logger.info(f"Saving comprehensive account info for {account.username}: BE={account.blue_essence}, RP={account.riot_points}, Level={account.summoner_level}")
                    await self.db_manager.save_account_info(
                        account.username,
                        account.region,
                        account_info
                    )
                    
                    # Save champions to database
                    champions_to_save = []
                    for champ in champion_data.get('champions', []):
                        if isinstance(champ, dict) and 'id' in champ and 'name' in champ:
                            champions_to_save.append({
                                'id': champ['id'],
                                'name': champ['name'],
                                'purchase_date': champ.get('purchase_date', '')
                            })
                    
                    if champions_to_save:
                        logger.info(f"Saving {len(champions_to_save)} champions for {account.username}")
                        await self.db_manager.save_champions(account.username, champions_to_save)
                    else:
                        logger.warning(f"No champions to save for {account.username}")
                    
                    # Get skin data
                    try:
                        logger.info(f"Getting skin data for {account.username}")
                        
                        # Determine region code for skins
                        region_code_for_skins = account.region_code
                        
                        # If region_code is not set, try to get it from the JWT token
                        if not region_code_for_skins:
                            # Try to get region from JWT token
                            jwt_region = decoded_info.get('dat', {}).get('r', '')
                            if jwt_region:
                                # Map region codes to regions
                                region_mapping = {
                                    'EUW1': 'EUW',
                                    'NA1': 'NA',
                                    'EUN1': 'EUNE',
                                    'LA1': 'LAN',
                                    'LA2': 'LAS',
                                    'BR1': 'BR',
                                    'TR1': 'TR',
                                    'RU': 'RU',
                                    'JP1': 'JP',
                                    'KR': 'KR',
                                    'OC1': 'OCE'
                                }
                                region_code_for_skins = region_mapping.get(jwt_region, account.region)
                                logger.info(f"Using region code from JWT token for skin data: {region_code_for_skins} for {account.username}")
                            else:
                                # If not in JWT, use account.region
                                region_code_for_skins = account.region
                                logger.info(f"Using account region for skin data retrieval: {region_code_for_skins} for {account.username}")
                        else:
                            logger.info(f"Using existing region code for skin data retrieval: {region_code_for_skins} for {account.username}")
                        
                        # Get skin data
                        skins = await self.get_owned_skins(
                            session_token=session_token,
                            entitlements_token=entitlements_token,
                            puuid=decoded_info.get('sub', ''),
                            account_id=str(decoded_info.get('original_account_id', '')),
                            region_code=region_code_for_skins,
                            session=account.authenticator.session
                        )
                        
                        # Process skins
                        processed_skins = []
                        for skin in skins:
                            if isinstance(skin, dict) and 'id' in skin and 'name' in skin:
                                # Get champion name from skin info
                                skin_info = self._get_skin_info(skin['id'])
                                processed_skin = {
                                    'id': skin['id'],
                                    'name': skin['name'],
                                    'champion_name': skin_info.get('champion', 'Unknown'),
                                    'purchase_date': skin.get('purchase_date', '')
                                }
                                processed_skins.append(processed_skin)
                        
                        # Save skins to database
                        if processed_skins:
                            logger.info(f"Saving {len(processed_skins)} skins for {account.username}")
                            await self.db_manager.save_skins(account.username, processed_skins)
                        else:
                            logger.warning(f"No skins to save for {account.username}")
                        
                        logger.info(f"Got skin data for {account.username}")
                    except Exception as e:
                        logger.error(f"Error getting skin data: {str(e)}")
                    
                    # Check penalties
                    try:
                        logger.info(f"Checking penalties for {account.username}")
                        try:
                            penalty_minutes, ranked_games = await check_leaver_penalty(
                                session_token,
                                account.region_code,
                                account.authenticator.session
                            )
                            account.penalty_minutes = penalty_minutes
                            account.ranked_games_remaining = ranked_games
                            logger.info(f"Got penalty info for {account.username}: {penalty_minutes} minutes, {ranked_games} games")
                        except Exception as e:
                            # Handle 403 error for NA accounts gracefully
                            if "403" in str(e) and account.region_code == "NA":
                                logger.info(f"Penalty info not available for NA account {account.username} (403 Forbidden)")
                                account.penalty_minutes = 0
                                account.ranked_games_remaining = 0
                            else:
                                logger.error(f"Failed to get penalty info: {str(e)}")
                                account.penalty_minutes = 0
                                account.ranked_games_remaining = 0
                        
                        # Update penalty info in database
                        await self.db_manager.save_account_info(
                            account.username,
                            account.region,
                            {
                                'penalty_minutes': account.penalty_minutes,
                                'ranked_games_remaining': account.ranked_games_remaining
                            }
                        )
                    except Exception as e:
                        logger.error(f"Error checking penalties: {str(e)}")
                        logger.exception(e)
                    
                    # Check rank
                    try:
                        logger.info(f"Checking rank for {account.game_name}#{account.tag_line}")

                        # Get the correct region code from various sources
                        region_code_for_rank = None
                        
                        # First try to get from JWT token
                        jwt_region = decoded_info.get('dat', {}).get('r', '')
                        if jwt_region:
                            if jwt_region == 'NA1':
                                region_code_for_rank = 'NA'
                            else:
                                region_code_for_rank = jwt_region
                        
                        # If not found in JWT, use account.region_code
                        if not region_code_for_rank and account.region_code:
                            region_code_for_rank = account.region_code
                        
                        # If still not found, use account.region
                        if not region_code_for_rank and account.region:
                            region_code_for_rank = account.region
                        
                        # Last resort, default to EUW
                        if not region_code_for_rank:
                            region_code_for_rank = 'EUW'
                        
                        logger.info(f"Using region code {region_code_for_rank} for rank info retrieval")
                        
                        rank_data = await get_rank_info(
                            session_token=session_token,
                            puuid=decoded_info.get('sub', ''),
                            region_code=region_code_for_rank,
                            session=account.authenticator.session,
                            entitlements_token=entitlements_token,
                            game_name=account.game_name,
                            tag_line=account.tag_line,
                            authenticator=account.authenticator
                        )
                        account.rank_info = rank_data
                        logger.info(f"Got rank info for {account.username}")
                        
                        # Extract rank info for database
                        solo_rank = rank_data.get('solo', {})
                        flex_rank = rank_data.get('flex', {})
                        
                        # Save rank info to database
                        rank_info_to_save = {
                            'solo_tier': solo_rank.get('tier', 'UNRANKED'),
                            'solo_division': solo_rank.get('division', ''),
                            'solo_lp': solo_rank.get('lp', 0),
                            'solo_wins': solo_rank.get('wins', 0),
                            'solo_losses': solo_rank.get('losses', 0),
                            'solo_previous_tier': solo_rank.get('previous_tier', 'UNRANKED'),
                            'solo_previous_division': solo_rank.get('previous_division', ''),
                            'flex_tier': flex_rank.get('tier', 'UNRANKED'),
                            'flex_division': flex_rank.get('division', ''),
                            'flex_lp': flex_rank.get('lp', 0),
                            'flex_wins': flex_rank.get('wins', 0),
                            'flex_losses': flex_rank.get('losses', 0),
                            'flex_previous_tier': flex_rank.get('previous_tier', 'UNRANKED'),
                            'flex_previous_division': flex_rank.get('previous_division', ''),
                            'season_history': rank_data.get('season_history', [])
                        }
                        
                        # Add season history to the data to save
                        if 'season_history' in rank_data and rank_data['season_history']:
                            rank_info_to_save['season_history'] = rank_data['season_history']
                            logger.info(f"Including {len(rank_data['season_history'])} season history entries for {account.username}")
                        
                        logger.info(f"Saving rank info for {account.username}: Solo={rank_info_to_save['solo_tier']} {rank_info_to_save['solo_division']}, Flex={rank_info_to_save['flex_tier']} {rank_info_to_save['flex_division']}")
                        await self.db_manager.save_account_info(
                            account.username,
                            account.region,
                            rank_info_to_save
                        )
                    except Exception as e:
                        logger.error(f"Error checking rank: {str(e)}")
                
                except Exception as e:
                    logger.error(f"Error getting champion data: {str(e)}")
                    
                    # Update basic account info
                    account.summoner_level = decoded_info.get('lol_account', {}).get('summoner_level', 0)
                    account.game_name = decoded_info.get('acct', {}).get('game_name', account.username)
                    account.tag_line = decoded_info.get('acct', {}).get('tag_line', '')
                    account.last_checked = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    account.country = decoded_info.get('country', '')
                    account.creation_region = account.region
                    
                    # Save basic info to database
                    await self.db_manager.save_account_info(
                        account.username,
                        account.region,
                        {
                            'summoner_level': account.summoner_level,
                            'game_name': account.game_name,
                            'tag_line': account.tag_line,
                            'last_checked': account.last_checked,
                            'country': account.country,
                            'creation_region': account.creation_region
                        }
                    )
            except Exception as e:
                logger.error(f"Error getting tokens: {str(e)}")
                
                # Update basic account info from userinfo
                account.summoner_level = decoded_info.get('lol_account', {}).get('summoner_level', 0)
                account.game_name = decoded_info.get('acct', {}).get('game_name', account.username)
                account.tag_line = decoded_info.get('acct', {}).get('tag_line', '')
                account.last_checked = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                account.country = decoded_info.get('country', '')
                account.creation_region = account.region
                
                # Save basic info to database
                await self.db_manager.save_account_info(
                    account.username,
                    account.region,
                    {
                        'summoner_level': account.summoner_level,
                        'game_name': account.game_name,
                        'tag_line': account.tag_line,
                        'last_checked': account.last_checked,
                        'country': account.country,
                        'creation_region': account.creation_region
                    }
                )
        except socket.gaierror as e:
            # Handle DNS resolution errors
            logger.error(f"DNS resolution error for {account.username}: {str(e)}")
            
            # Try to update basic account info if possible
            if hasattr(account, 'authenticator') and account.authenticator and hasattr(account.authenticator, 'userinfo'):
                try:
                    decoded_info = decode_jwt_payload(account.authenticator.userinfo)
                    
                    # Update basic account info
                    account.summoner_level = decoded_info.get('lol_account', {}).get('summoner_level', 0)
                    account.game_name = decoded_info.get('acct', {}).get('game_name', account.username)
                    account.tag_line = decoded_info.get('acct', {}).get('tag_line', '')
                    account.last_checked = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    account.country = decoded_info.get('country', '')
                    account.creation_region = account.region

                    # Save to database
                    await self.db_manager.save_account_info(
                        account.username,
                        account.region,
                        {
                            'summoner_level': account.summoner_level,
                            'game_name': account.game_name,
                            'tag_line': account.tag_line,
                            'last_checked': account.last_checked,
                            'country': account.country,
                            'creation_region': account.creation_region
                        }
                    )
                    logger.info(f"Saved basic info for {account.username} after DNS resolution error")
                except Exception as e:
                    logger.error(f"Error saving basic info after DNS error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error checking account {account.username}: {str(e)}")
            logger.exception(e)

    async def get_owned_skins(self, session_token: str, entitlements_token: str, puuid: str, 
                            account_id: str, region_code: str, session) -> List[Dict]:
        """Get owned skins for an account"""
        try:
            # Map region to data center using OpenBullet config approach
            region_to_datacenter = {
                'BR': 'usw',
                'LA1': 'usw',
                'LA2': 'usw',
                'NA': 'usw',
                'OC1': 'usw',
                'PBE1': 'usw',
                'RU': 'euc',
                'TR': 'euc',
                'EUNE': 'euc',
                'EUW': 'euc',
                'ME1': 'euc',
                'JP': 'apne',
                'KR': 'apne',
                'VN2': 'apse',
                'PH2': 'apse',
                'SG2': 'apse',
                'TW2': 'apse',
                'TH2': 'apse'
            }
            
            # Map datacenter to specific instance
            datacenter_to_instance = {
                'usw': 'usw2',
                'euc': 'euc1',
                'apse': 'apse1',
                'apne': 'apne1'
            }
            
            # Get datacenter and instance
            datacenter = region_to_datacenter.get(region_code, 'euc')
            instance = datacenter_to_instance.get(datacenter, 'euc1')
            
            # Define region-specific base URLs
            region_urls = {
                'EUW': 'https://euw-red.lol.sgp.pvp.net',
                'NA': 'https://na-red.lol.sgp.pvp.net',
                'BR': 'https://br-red.lol.sgp.pvp.net',
                'EUNE': 'https://eun-red.lol.sgp.pvp.net',
                'JP': 'https://jp-red.lol.sgp.pvp.net',
                'LA1': 'https://la1-red.lol.sgp.pvp.net',
                'LA2': 'https://la2-red.lol.sgp.pvp.net',
                'OC1': 'https://oc1-red.lol.sgp.pvp.net',
                'RU': 'https://ru-red.lol.sgp.pvp.net',
                'TR': 'https://tr-red.lol.sgp.pvp.net'
            }
            
            # Define multiple URL patterns to try
            url_patterns = [
                # Standard pattern
                {
                    "base_url": region_urls.get(region_code, region_urls['EUW']),
                    "inventory_path": "/lolinventoryservice-ledge/v2/inventoriesWithLoyalty",
                    "location_format": f"lolriot.aws-{instance}-prod.{region_code.lower()}"
                },
                # Instance-based pattern (for NA)
                {
                    "base_url": f"https://{instance}-red.pp.sgp.pvp.net",
                    "inventory_path": "/lolinventoryservice-ledge/v2/inventoriesWithLoyalty",
                    "location_format": f"lolriot.aws-{instance}-prod.na1"
                },
                # Alternative endpoint
                {
                    "base_url": region_urls.get(region_code, region_urls['EUW']),
                    "inventory_path": "/lolinventoryservice-ledge/v1/inventories/CHAMPION_SKIN",
                    "location_format": f"lolriot.aws-{instance}-prod.{region_code.lower()}"
                },
                # NA-specific pattern
                {
                    "base_url": "https://na-red.lol.sgp.pvp.net",
                    "inventory_path": "/lolinventoryservice-ledge/v2/inventoriesWithLoyalty",
                    "location_format": "lolriot.aws-usw2-prod.na"
                }
            ]
            
            # Set up headers
            headers = {
                'Authorization': f'Bearer {session_token}',
                'X-Riot-Entitlements-JWT': entitlements_token,
                'Content-Type': 'application/json',
                'User-Agent': 'LeagueOfLegendsClient/13.8.496.2366 (CEF 91.0.4472.106)',
                'Accept': 'application/json',
                'Accept-Encoding': 'deflate, gzip, zstd'
            }
            
            # Create a TCP connector with IPv4 family and SSL verification disabled
            connector = aiohttp.TCPConnector(family=socket.AF_INET, ssl=False)
            
            # Try each URL pattern until one works
            async with aiohttp.ClientSession(connector=connector) as skin_session:
                for i, pattern in enumerate(url_patterns, 1):
                    try:
                        logger.info(f"Trying skin URL pattern {i}/{len(url_patterns)}")
                        logger.info(f"Using base URL: {pattern['base_url']}")
                        
                        inventory_url = f"{pattern['base_url']}{pattern['inventory_path']}"
                        logger.info(f"Inventory URL: {inventory_url}")
                        
                        location_format = pattern['location_format']
                        logger.info(f"Location format: {location_format}")
                        
                        # Set up params for inventory request
                        inventory_params = {
                'puuid': puuid,
                'accountId': account_id,
                            'inventoryTypes': 'CHAMPION_SKIN',
                            'location': location_format
                        }
                        
                        logger.info(f"Attempting to resolve hostname: {pattern['base_url'].replace('https://', '')}")
                        try:
                            hostname = pattern['base_url'].replace('https://', '')
                            ip_address = socket.gethostbyname(hostname)
                            logger.info(f"Successfully resolved {hostname} to {ip_address}")
                        except Exception as e:
                            logger.error(f"Failed to resolve hostname {hostname}: {str(e)}")
                            continue
                        
                        # Get inventory data
                        inventory_response = await skin_session.get(inventory_url, headers=headers, params=inventory_params)
                        
                        if inventory_response.status == 200:
                            inventory_data = await inventory_response.json()
                            
                            # Extract skins data
                            skins_raw = inventory_data.get('data', {}).get('items', {}).get('CHAMPION_SKIN', [])
                            
        # Process skin data
        skins = []
        for skin in skins_raw:
        skin_id = skin.get('itemId')
        if skin_id:
        skin_info = self._get_skin_info(skin_id)
        skin_data = {
        'id': skin_id,
        'name': skin_info.get('name', 'Unknown'),
        'champion': skin_info.get('champion', 'Unknown'),
        'purchase_date': skin.get('purchaseDate')
                                    }
                                    skins.append(skin_data)
                            
                            logger.info(f"Successfully retrieved skin data with pattern {i}")
                        logger.info(f"Found {len(skins)} skins for account {puuid}")
                        return skins
                    else:
                            response_text = await inventory_response.text()
                            logger.error(f"Failed to get skin data with pattern {i}. Status: {inventory_response.status}")
                            logger.error(f"Response: {response_text}")
                    except Exception as e:
                        logger.error(f"Error trying skin URL pattern {i}: {str(e)}")
                
                logger.error("All skin URL patterns failed")
                        return []
        except Exception as e:
            logger.error(f"Error in get_owned_skins: {str(e)}")
            return []

    def _get_skin_info(self, skin_id: int) -> Dict:
        """Get skin information from the skin database"""
        try:
            # Load skin database if not already loaded
            if not hasattr(self, '_skin_db'):
                self._skin_db = self._load_skin_database()
            
            skin_id_str = str(skin_id)
            
            # First check if it's a main skin
            if skin_id_str in self._skin_db:
                skin_info = self._skin_db[skin_id_str]
                return {
                    'name': skin_info.get('name', f'Unknown Skin ({skin_id})'),
                    'champion': self._extract_champion_from_skin(skin_info),
                    'rarity': skin_info.get('rarity', 'Unknown')
                }
            
            # If not a main skin, check if it's a chroma
            for main_skin_id, skin_info in self._skin_db.items():
                if "chromas" in skin_info and skin_info["chromas"]:
                    for chroma in skin_info["chromas"]:
                        if str(chroma.get("id")) == skin_id_str:
                            # Found the chroma
                            champion = self._extract_champion_from_skin(skin_info)
                            
                            # Get the base skin name
                            base_skin_name = skin_info.get('name', f'Unknown Skin ({main_skin_id})')
                            
                            # Try to get a descriptive name for the chroma
                            chroma_description = ""
                            
                            # Check if there's a description
                            if "descriptions" in chroma and chroma["descriptions"]:
                                for desc in chroma["descriptions"]:
                                    if "description" in desc:
                                        chroma_description = desc["description"]
                                        break
                            
                            # If no description, try to use color
                            if not chroma_description and "colors" in chroma and chroma["colors"]:
                                chroma_description = f"Color: {chroma['colors'][0]}"
                            
                            # Create a name that includes the base skin and chroma info
                            if chroma_description:
                                # Extract a short version of the description
                                short_desc = self._extract_short_chroma_description(chroma_description)
                                skin_name = f"{base_skin_name} ({short_desc})"
            else:
                                skin_name = f"{base_skin_name} (Chroma)"
                            
                return {
                                'name': skin_name,
                                'champion': champion,
                                'rarity': 'Chroma'
                }
            
            # If not found, return unknown
            return {'name': f'Unknown Skin ({skin_id})', 'champion': 'Unknown'}
            
        except Exception as e:
            logger.error(f"Error getting skin info: {str(e)}")
            return {'name': f'Unknown Skin ({skin_id})', 'champion': 'Unknown'}
    
    def _extract_short_chroma_description(self, description: str) -> str:
        """Extract a short description from a chroma description"""
        try:
            # For ranked rewards, extract the rank
            if "reached" in description and "rank" in description:
                # Try to extract the rank
                rank_keywords = ["Bronze", "Silver", "Gold", "Platinum", "Diamond", "Master", "Grandmaster", "Challenger"]
                for rank in rank_keywords:
                    if rank in description:
                        return rank
            
            # For bundle exclusives
            if "bundle exclusive" in description:
                return "Bundle"
            
            # For event exclusives
            if "Loot exclusive" in description and "event" in description:
                # Try to extract the event name
                event_start = description.find("in the ") + 7
                if event_start > 7:  # Found "in the "
                    event_end = description.find(" event", event_start)
                    if event_end > 0:
                        return description[event_start:event_end]
                return "Event"
            
            # Default to a short version
            if len(description) > 20:
                return description[:20] + "..."
            return description
        except Exception:
            return "Chroma"
    
    def _extract_champion_from_skin(self, skin_info: Dict) -> str:
        """Extract champion name from skin info"""
        try:
            # The champion name is often in the path
            if "splashPath" in skin_info and "Characters" in skin_info["splashPath"]:
                path_parts = skin_info["splashPath"].split("/")
                for i, part in enumerate(path_parts):
                    if part == "Characters" and i + 1 < len(path_parts):
                        return path_parts[i + 1]
            
            # For base skins, the name is usually just the champion name
            if skin_info.get("isBase", False) and "name" in skin_info:
                return skin_info["name"]
            
            # If all else fails, return Unknown
            return "Unknown"
        except Exception:
            return "Unknown"