import asyncio
import os
import aiosqlite
import logging
import json
from typing import List, Dict, Optional
from custom_logger.logger import logger

# Define database path
DB_PATH = 'accounts.db'

logger = logging.getLogger(__name__)

class DatabaseManager:
    def __init__(self):
        self.db_path = DB_PATH

    async def init_db(self):
        async with aiosqlite.connect(self.db_path) as db:
            # Create accounts table if it doesn't exist
            await db.execute('''
                CREATE TABLE IF NOT EXISTS accounts (
                    username TEXT NOT NULL PRIMARY KEY,
                    password TEXT,
                    region TEXT,
                    refresh_token TEXT,
                    access_token TEXT,
                    id_token TEXT,
                    entitlements_token TEXT,
                    userinfo TEXT,
                    timestamp BIGINT,
                    puuid TEXT,
                    account_id TEXT,
                    game_name TEXT,
                    tag_line TEXT,
                    champion_count INTEGER DEFAULT 0,
                    blue_essence INTEGER DEFAULT 0,
                    riot_points INTEGER DEFAULT 0,
                    summoner_level INTEGER DEFAULT 0,
                    created_at TEXT,
                    last_checked TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    penalty_minutes INTEGER DEFAULT 0,
                    ranked_games_remaining INTEGER DEFAULT 0,
                    country TEXT DEFAULT NULL,
                    creation_region TEXT DEFAULT NULL,
                    is_banned BOOLEAN DEFAULT FALSE,
                    ban_info TEXT,
                    solo_tier TEXT DEFAULT 'UNRANKED',
                    solo_division TEXT DEFAULT '',
                    solo_lp INTEGER DEFAULT 0,
                    solo_wins INTEGER DEFAULT 0,
                    solo_losses INTEGER DEFAULT 0,
                    solo_previous_tier TEXT DEFAULT 'UNRANKED',
                    solo_previous_division TEXT DEFAULT '',
                    flex_tier TEXT DEFAULT 'UNRANKED',
                    flex_division TEXT DEFAULT '',
                    flex_lp INTEGER DEFAULT 0,
                    flex_wins INTEGER DEFAULT 0,
                    flex_losses INTEGER DEFAULT 0,
                    flex_previous_tier TEXT DEFAULT 'UNRANKED',
                    flex_previous_division TEXT DEFAULT '',
                    chat_restrictions TEXT
                )
            ''')

            # Create owned_champions table if it doesn't exist
            await db.execute('''
                CREATE TABLE IF NOT EXISTS owned_champions (
                    username TEXT,
                    champion_id INTEGER,
                    champion_name TEXT,
                    purchase_date TEXT,
                    PRIMARY KEY (username, champion_id),
                    FOREIGN KEY (username) REFERENCES accounts(username) ON DELETE CASCADE
                )
            ''')

            # Create owned_skins table if it doesn't exist
            await db.execute('''
                CREATE TABLE IF NOT EXISTS owned_skins (
                    username TEXT,
                    skin_id INTEGER,
                    skin_name TEXT,
                    champion_name TEXT,
                    rarity TEXT,
                    purchase_date TEXT,
                    PRIMARY KEY (username, skin_id),
                    FOREIGN KEY (username) REFERENCES accounts(username) ON DELETE CASCADE
                )
            ''')
            
            # Create season_history table if it doesn't exist
            await db.execute('''
                CREATE TABLE IF NOT EXISTS season_history (
                    username TEXT,
                    season INTEGER,
                    split TEXT,
                    queue_type TEXT,
                    peak_tier TEXT,
                    peak_division TEXT,
                    end_tier TEXT,
                    end_division TEXT,
                    PRIMARY KEY (username, season, split, queue_type),
                    FOREIGN KEY (username) REFERENCES accounts(username) ON DELETE CASCADE
                )
            ''')
            
            # Check for missing columns and add them
            cursor = await db.execute("PRAGMA table_info(accounts)")
            columns = await cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            # Add new columns if they don't exist
            new_columns = {
                'country': 'TEXT DEFAULT NULL',
                'creation_region': 'TEXT DEFAULT NULL',
                'solo_tier': "TEXT DEFAULT 'UNRANKED'",
                'solo_division': "TEXT DEFAULT ''",
                'solo_lp': 'INTEGER DEFAULT 0',
                'solo_wins': 'INTEGER DEFAULT 0',
                'solo_losses': 'INTEGER DEFAULT 0',
                'solo_previous_tier': "TEXT DEFAULT 'UNRANKED'",
                'solo_previous_division': "TEXT DEFAULT ''",
                'flex_tier': "TEXT DEFAULT 'UNRANKED'",
                'flex_division': "TEXT DEFAULT ''",
                'flex_lp': 'INTEGER DEFAULT 0',
                'flex_wins': 'INTEGER DEFAULT 0',
                'flex_losses': 'INTEGER DEFAULT 0',
                'flex_previous_tier': "TEXT DEFAULT 'UNRANKED'",
                'flex_previous_division': "TEXT DEFAULT ''",
                'chat_restrictions': 'TEXT DEFAULT NULL',
                'is_glitch_account': 'BOOLEAN DEFAULT 0'
            }
            
            for col_name, col_type in new_columns.items():
                if col_name not in column_names:
                    await db.execute(f"ALTER TABLE accounts ADD COLUMN {col_name} {col_type}")
                    logger.info(f"Added column {col_name} to accounts table")
            
            await db.commit()

    async def get_all_accounts(self) -> List[Dict]:
        """Get all accounts from database"""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            cursor = await db.execute("""
                SELECT username, password, region, refresh_token, access_token,
                       id_token, entitlements_token, userinfo, timestamp,
                       puuid, account_id, champion_count, blue_essence,
                       riot_points, summoner_level, last_checked, created_at,
                       is_banned, ban_info, game_name, tag_line, country,
                       creation_region, penalty_minutes, ranked_games_remaining,
                       solo_tier, solo_division, solo_lp, solo_wins, solo_losses,
                       solo_previous_tier, solo_previous_division,
                       flex_tier, flex_division, flex_lp, flex_wins, flex_losses,
                       flex_previous_tier, flex_previous_division, is_glitch_account
                FROM accounts
            """)
            rows = await cursor.fetchall()
            
            accounts = []
            for row in rows:
                account = dict(row)
                # Safely handle region code extraction without defaulting
                region = account.get('region')
                account['region_code'] = region.upper()[:3] if region and region.strip() else None
                
                # Load season history for this account
                try:
                    logger.info(f"Loading season history for account {account['username']}")
                    cursor = await db.execute("""
                        SELECT season, split, queue_type, peak_tier, peak_division, end_tier, end_division
                        FROM season_history
                        WHERE username = ?
                        ORDER BY season DESC, split DESC
                    """, (account['username'],))
                    
                    season_history_rows = await cursor.fetchall()
                    season_history = []
                    
                    for sh_row in season_history_rows:
                        season_entry = {
                            'season': sh_row[0],
                            'split': sh_row[1],
                            'queue_type': sh_row[2],
                            'peak_tier': sh_row[3],
                            'peak_division': sh_row[4],
                            'end_tier': sh_row[5],
                            'end_division': sh_row[6]
                        }
                        season_history.append(season_entry)
                    
                    # Create rank_info structure if it doesn't exist
                    if 'rank_info' not in account:
                        account['rank_info'] = {
                            'solo': {
                                'tier': account.get('solo_tier', 'UNRANKED'),
                                'division': account.get('solo_division', ''),
                                'lp': account.get('solo_lp', 0),
                                'wins': account.get('solo_wins', 0),
                                'losses': account.get('solo_losses', 0),
                                'previous_tier': account.get('solo_previous_tier', 'UNRANKED'),
                                'previous_division': account.get('solo_previous_division', '')
                            },
                            'flex': {
                                'tier': account.get('flex_tier', 'UNRANKED'),
                                'division': account.get('flex_division', ''),
                                'lp': account.get('flex_lp', 0),
                                'wins': account.get('flex_wins', 0),
                                'losses': account.get('flex_losses', 0),
                                'previous_tier': account.get('flex_previous_tier', 'UNRANKED'),
                                'previous_division': account.get('flex_previous_division', '')
                            }
                        }
                    
                    # Add season history to rank_info
                    account['rank_info']['season_history'] = season_history
                    logger.info(f"Loaded {len(season_history)} season history entries for {account['username']}")
                except Exception as e:
                    logger.error(f"Error loading season history for {account['username']}: {str(e)}")
                    logger.exception(e)
                    # Initialize empty season history if there was an error
                    if 'rank_info' in account:
                        account['rank_info']['season_history'] = []
                    else:
                        account['rank_info'] = {
                            'solo': {
                                'tier': account.get('solo_tier', 'UNRANKED'),
                                'division': account.get('solo_division', ''),
                                'lp': account.get('solo_lp', 0),
                                'wins': account.get('solo_wins', 0),
                                'losses': account.get('solo_losses', 0),
                                'previous_tier': account.get('solo_previous_tier', 'UNRANKED'),
                                'previous_division': account.get('solo_previous_division', '')
                            },
                            'flex': {
                                'tier': account.get('flex_tier', 'UNRANKED'),
                                'division': account.get('flex_division', ''),
                                'lp': account.get('flex_lp', 0),
                                'wins': account.get('flex_wins', 0),
                                'losses': account.get('flex_losses', 0),
                                'previous_tier': account.get('flex_previous_tier', 'UNRANKED'),
                                'previous_division': account.get('flex_previous_division', '')
                            },
                            'season_history': []
                        }
                
                accounts.append(account)
            
            return accounts

    async def save_account_info(self, username: str, region: str, info: Dict):
        """Save or update account info"""
        # Ensure region is not None before saving
        if not region:
            region = ''  # Use empty string as default
            
        # Log the info being saved for debugging
        logger.info(f"Saving account info for {username}: created_at={info.get('created_at')}, creation_region={info.get('creation_region')}")
        logger.info(f"Account details: BE={info.get('blue_essence')}, RP={info.get('riot_points')}, Level={info.get('summoner_level')}")
        logger.info(f"Rank info: Solo={info.get('solo_tier')} {info.get('solo_division')}, Flex={info.get('flex_tier')} {info.get('flex_division')}")
        logger.info(f"Penalties: {info.get('penalty_minutes')} minutes, {info.get('ranked_games_remaining')} ranked games remaining")
        logger.info(f"Game info: game_name={info.get('game_name')}, tag_line={info.get('tag_line')}")
        
        # Add more detailed logging
        logger.info(f"DETAILED INFO DUMP: {json.dumps(info, indent=2)}")
            
        try:
            async with aiosqlite.connect(self.db_path) as db:
                # First check if the account exists
                cursor = await db.execute("SELECT username FROM accounts WHERE username = ?", (username,))
                row = await cursor.fetchone()
                
                if not row:
                    # Account doesn't exist, insert it
                    logger.info(f"Account {username} doesn't exist in database, creating it")
                    await db.execute("""
                        INSERT INTO accounts (username, password, region)
                        VALUES (?, ?, ?)
                    """, (username, '', region))
                    await db.commit()
                
                # Now do the update
                try:
                    # Build the SQL update statement dynamically based on the info provided
                    update_fields = []
                    update_values = []
                    
                    # Always update region
                    update_fields.append("region = ?")
                    update_values.append(region)
                    
                    # Add all other fields from the info dictionary
                    for key, value in info.items():
                        if key in ['username', 'password', 'season_history']:  # Skip these fields
                            continue
                        update_fields.append(f"{key} = ?")
                        update_values.append(value)
                    
                    # Add username as the last parameter for the WHERE clause
                    update_values.append(username)
                    
                    # Construct the SQL statement
                    sql = f"""
                        UPDATE accounts SET
                            {', '.join(update_fields)}
                        WHERE username = ?
                    """
                    
                    logger.info(f"Executing SQL update: {sql}")
                    logger.info(f"Update values: {update_values}")
                    
                    await db.execute(sql, update_values)
                    await db.commit()
                    logger.info(f"Successfully updated account info for {username}")
                except Exception as e:
                    logger.error(f"Error updating account info: {str(e)}")
                    logger.exception(e)
                    raise
                
                # Save season history if provided
                if 'season_history' in info and info['season_history']:
                    try:
                        # First delete existing season history for this account
                        await db.execute("DELETE FROM season_history WHERE username = ?", (username,))
                        
                        # Then insert the new season history
                        for season_data in info['season_history']:
                            await db.execute("""
                                INSERT INTO season_history 
                                (username, season, split, queue_type, peak_tier, peak_division, end_tier, end_division)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                            """, (
                                username,
                                season_data.get('season'),
                                season_data.get('split'),
                                season_data.get('queue_type'),
                                season_data.get('peak_tier'),
                                season_data.get('peak_division'),
                                season_data.get('end_tier'),
                                season_data.get('end_division')
                            ))
                        
                        await db.commit()
                        logger.info(f"Saved {len(info['season_history'])} season history entries for {username}")
                    except Exception as e:
                        logger.error(f"Error saving season history: {str(e)}")
                        logger.exception(e)
                
                # Verify the data was saved correctly
                try:
                    cursor = await db.execute("SELECT created_at, creation_region, blue_essence, riot_points, summoner_level, game_name, tag_line FROM accounts WHERE username = ?", (username,))
                    row = await cursor.fetchone()
                    if row:
                        logger.info(f"Verified saved data for {username}: created_at={row[0]}, creation_region={row[1]}, BE={row[2]}, RP={row[3]}, Level={row[4]}, game_name={row[5]}, tag_line={row[6]}")
                    else:
                        logger.warning(f"Could not verify saved data for {username}")
                except Exception as e:
                    logger.error(f"Error verifying saved data: {str(e)}")
                    logger.exception(e)
        except Exception as e:
            logger.error(f"Database error in save_account_info: {str(e)}")
            logger.exception(e)

    async def get_accounts_by_region(self, region: str) -> List[Dict]:
        """Get accounts for specific region"""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            cursor = await db.execute("""
                SELECT * FROM accounts WHERE region = ?
            """, (region,))
            rows = await cursor.fetchall()
            
            accounts = []
            for row in rows:
                account = dict(row)
                # Safely handle region code extraction without defaulting
                region = account.get('region')
                account['region_code'] = region.upper()[:3] if region and region.strip() else None
                accounts.append(account)
            
            return accounts

    async def get_account(self, username: str):
        """Get account credentials and tokens (legacy method)"""
        return await self.get_account_info(username)

    async def get_account_info(self, username: str) -> Dict:
        """Get account info from database"""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            cursor = await db.execute("""
                SELECT username, password, region, refresh_token, access_token, 
                       id_token, entitlements_token, userinfo, timestamp,
                       puuid, account_id, game_name, tag_line, champion_count,
                       blue_essence, riot_points, summoner_level, created_at,
                       last_checked, penalty_minutes, ranked_games_remaining,
                       is_banned, ban_info, country, creation_region,
                       solo_tier, solo_division, solo_lp, solo_wins, solo_losses,
                       solo_previous_tier, solo_previous_division,
                       flex_tier, flex_division, flex_lp, flex_wins, flex_losses,
                       flex_previous_tier, flex_previous_division, chat_restrictions,
                       is_glitch_account
                FROM accounts 
                WHERE username = ?
            """, (username,))
            row = await cursor.fetchone()
            
            if row:
                account = dict(row)
                # Safely handle region code extraction without defaulting
                region = account.get('region')
                account['region_code'] = region.upper()[:3] if region and region.strip() else None
                
                # Load season history
                try:
                    cursor = await db.execute("""
                        SELECT season, split, queue_type, peak_tier, peak_division, end_tier, end_division
                        FROM season_history
                        WHERE username = ?
                        ORDER BY season DESC, split DESC
                    """, (username,))
                    season_rows = await cursor.fetchall()
                    
                    if season_rows:
                        season_history = []
                        for season_row in season_rows:
                            season_history.append({
                                'season': season_row[0],
                                'split': season_row[1],
                                'queue_type': season_row[2],
                                'peak_tier': season_row[3],
                                'peak_division': season_row[4],
                                'end_tier': season_row[5],
                                'end_division': season_row[6]
                            })
                        
                        # Create rank_info structure if it doesn't exist
                        if 'rank_info' not in account:
                            account['rank_info'] = {
                                'solo': {
                                    'tier': account.get('solo_tier', 'UNRANKED'),
                                    'division': account.get('solo_division', ''),
                                    'lp': account.get('solo_lp', 0),
                                    'wins': account.get('solo_wins', 0),
                                    'losses': account.get('solo_losses', 0),
                                    'previous_tier': account.get('solo_previous_tier', 'UNRANKED'),
                                    'previous_division': account.get('solo_previous_division', '')
                                },
                                'flex': {
                                    'tier': account.get('flex_tier', 'UNRANKED'),
                                    'division': account.get('flex_division', ''),
                                    'lp': account.get('flex_lp', 0),
                                    'wins': account.get('flex_wins', 0),
                                    'losses': account.get('flex_losses', 0),
                                    'previous_tier': account.get('flex_previous_tier', 'UNRANKED'),
                                    'previous_division': account.get('flex_previous_division', '')
                                }
                            }
                        
                        # Add season history to rank_info
                        account['rank_info']['season_history'] = season_history
                        logger.info(f"Loaded {len(season_history)} season history entries for {username}")
                except Exception as e:
                    logger.error(f"Error loading season history: {str(e)}")
                    logger.exception(e)
                
                return account
            return None

    async def update_account(self, username: str, **kwargs):
        """Update account fields in database"""
        allowed_fields = {
            'password', 'region', 'refresh_token', 'access_token', 'id_token',
            'entitlements_token', 'userinfo', 'timestamp', 'puuid', 'account_id',
            'champion_count', 'blue_essence', 'riot_points', 'summoner_level',
            'last_checked', 'created_at', 'is_banned', 'ban_info', 'game_name',
            'tag_line', 'country', 'creation_region', 'penalty_minutes',
            'ranked_games_remaining', 'solo_tier', 'solo_division', 'solo_lp', 
            'solo_wins', 'solo_losses', 'solo_previous_tier', 'solo_previous_division',
            'flex_tier', 'flex_division', 'flex_lp', 'flex_wins', 'flex_losses',
            'flex_previous_tier', 'flex_previous_division', 'chat_restrictions',
            'is_glitch_account'
        }

        current_time = int(asyncio.get_running_loop().time())
        
        # First, get existing account data to preserve it
        existing_data = await self.get_account_info(username)
        
        async with aiosqlite.connect(self.db_path) as db:
            if existing_data:
                # Update existing account while preserving other data
                update_fields = []
                update_values = []
                
                # Add all allowed fields from kwargs
                for key, value in kwargs.items():
                    if key in allowed_fields:
                        update_fields.append(f"{key} = ?")
                        update_values.append(value)
                
                # Add username as the last parameter for the WHERE clause
                update_values.append(username)
                
                # Construct the SQL statement
                sql = f"""
                    UPDATE accounts SET
                        {', '.join(update_fields)}
                    WHERE username = ?
                """
                
                logger.info(f"Executing SQL update: {sql}")
                logger.info(f"Update values: {update_values}")
                
                await db.execute(sql, update_values)
            else:
                # Insert new account
                await db.execute('''
                    INSERT INTO accounts (
                        username, password, refresh_token, access_token, id_token, 
                        entitlements_token, userinfo, timestamp, region
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (username, kwargs.get('password'), kwargs.get('refresh_token'), kwargs.get('access_token'), kwargs.get('id_token'), 
                      kwargs.get('entitlements_token'), kwargs.get('userinfo'), current_time, kwargs.get('region', '')))
            
            await db.commit()
            logger.info(f"Updated account fields for {username}")

    async def delete_account(self, username: str):
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute('DELETE FROM accounts WHERE username = ?', (username,))
            await db.commit()

    async def save_champions(self, username: str, champions: List[Dict]):
        """Save owned champions for an account"""
        async with aiosqlite.connect(self.db_path) as db:
            # Clear existing champions
            await db.execute("DELETE FROM owned_champions WHERE username = ?", (username,))
            
            # Insert new champions
            for champ in champions:
                await db.execute("""
                    INSERT INTO owned_champions (username, champion_id, champion_name, purchase_date)
                    VALUES (?, ?, ?, ?)
                """, (username, champ['id'], champ['name'], champ.get('purchase_date')))
            await db.commit()

    async def get_owned_champions(self, username: str) -> List[Dict]:
        """Get owned champions for an account"""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            cursor = await db.execute("""
                SELECT champion_id, champion_name, purchase_date
                FROM owned_champions
                WHERE username = ?
                ORDER BY champion_name
            """, (username,))
            rows = await cursor.fetchall()
            return [dict(row) for row in rows]

    async def get_first_champions(self, username: str, limit: int = 5) -> List[Dict]:
        """Get first champions purchased for an account"""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            cursor = await db.execute("""
                SELECT champion_name, purchase_date
                FROM owned_champions
                WHERE username = ?
                ORDER BY purchase_date ASC
                LIMIT ?
            """, (username, limit))
            rows = await cursor.fetchall()
            return [dict(row) for row in rows]

    async def clear_all_accounts(self):
        """Clear all accounts from database"""
        async with aiosqlite.connect(self.db_path) as db:
            # Clear accounts table
            await db.execute("DELETE FROM accounts")
            # Clear owned champions table
            await db.execute("DELETE FROM owned_champions")
            await db.commit()

    async def save_skins(self, username: str, skins: List[Dict]):
        """Save owned skins for an account"""
        async with aiosqlite.connect(self.db_path) as db:
            # Clear existing skins
            await db.execute("DELETE FROM owned_skins WHERE username = ?", (username,))
            
            # Insert new skins
            for skin in skins:
                await db.execute("""
                    INSERT INTO owned_skins (username, skin_id, skin_name, champion_name, rarity, purchase_date)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (username, skin['id'], skin['name'], skin['champion_name'], 
                      skin.get('rarity', 'Unknown'), skin.get('purchase_date', '')))
            await db.commit()

    async def get_owned_skins(self, username: str) -> List[Dict]:
        """Get owned skins for an account"""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            cursor = await db.execute("""
                SELECT skin_id, skin_name, champion_name, rarity, purchase_date
                FROM owned_skins
                WHERE username = ?
                ORDER BY champion_name, skin_name
            """, (username,))
            rows = await cursor.fetchall()
            return [dict(row) for row in rows]

    async def get_champions(self, username: str) -> List[Dict]:
        """Get champions with proper formatting for the HTML template"""
        champions = await self.get_owned_champions(username)
        formatted_champions = []
        
        for champ in champions:
            formatted_champions.append({
                'id': champ.get('champion_id', 0),
                'name': champ.get('champion_name', 'Unknown'),
                'purchase_date': champ.get('purchase_date', 'Unknown')
            })
        
        return formatted_champions
    
    async def get_skins(self, username: str) -> List[Dict]:
        """Get skins with proper formatting for the HTML template"""
        skins = await self.get_owned_skins(username)
        formatted_skins = []
        
        for skin in skins:
            # Format champion name for URL (remove spaces)
            champion_id = skin.get('champion_name', '').replace(' ', '')
            
            formatted_skins.append({
                'skin_id': skin.get('skin_id', 0),
                'skin_name': skin.get('skin_name', 'Unknown'),
                'champion_name': skin.get('champion_name', 'Unknown'),
                'champion_id': champion_id,
                'purchase_date': skin.get('purchase_date', 'Unknown'),
                'rarity': skin.get('rarity', 'Unknown')
            })
        
        return formatted_skins
    
    async def get_season_history(self, username: str) -> List[Dict]:
        """Get season history with proper formatting for the HTML template"""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            cursor = await db.execute("""
                SELECT season, split, queue_type, peak_tier, peak_division, end_tier, end_division
                FROM season_history
                WHERE username = ?
                ORDER BY season DESC, split DESC
            """, (username,))
            rows = await cursor.fetchall()
            
            formatted_history = []
            for row in rows:
                # Only include solo queue entries
                if row['queue_type'] == 'RANKED_SOLO_5x5':
                    formatted_history.append({
                        'season': f"Season {row['season']}",
                        'peak_tier': row['peak_tier'],
                        'peak_tier_lower': row['peak_tier'].lower(),
                        'peak_division': row['peak_division'],
                        'end_tier': row['end_tier'],
                        'end_tier_lower': row['end_tier'].lower(),
                        'end_division': row['end_division']
                    })
            
            return formatted_history
