import sys
import asyncio
import json
import logging
import base64
import aiohttp
import ssl
import threading
import tempfile
import os
import subprocess
import time
from typing import Dict, Any, Optional
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("league-checker")

# PyQt imports for UI
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QLabel, QLineEdit, QPushButton, QTextEdit, QComboBox, QCheckBox,
    QProgressBar
)
from PyQt6.QtCore import Qt, pyqtSignal, QObject, QThread, QTimer, QUrl

def set_log_level_enabled(enabled):
    if enabled:
        logger.setLevel(logging.DEBUG)
    else:
        logger.setLevel(logging.WARNING)

def decode_jwt_payload(jwt_token):
    """Decode the JWT token payload"""
    try:
        # Split the token into header, payload, and signature
        parts = jwt_token.split('.')
        if len(parts) != 3:
            return {}
        
        # Decode the payload (middle part)
        payload = parts[1]
        # Add padding if needed
        padding_length = 4 - (len(payload) % 4)
        if padding_length < 4:
            payload += '=' * padding_length
        
        # Decode from base64
        decoded_bytes = base64.urlsafe_b64decode(payload)
        decoded_str = decoded_bytes.decode('utf-8')
        
        # Parse JSON
        return json.loads(decoded_str)
    except Exception as e:
        logger.error(f"Error decoding JWT: {str(e)}")
        return {}

def solve_captcha_with_subprocess(sitekey: str, rqdata: str) -> str:
    """Solve captcha using a subprocess to avoid threading issues"""
    logger.info("Solving captcha in subprocess...")
    
    # Create a temporary script for the captcha
    fd, script_path = tempfile.mkstemp(suffix='.py', prefix='captcha_solver_')
    os.close(fd)
    
    with open(script_path, 'w') as f:
        f.write(f'''
import sys
import os
import json
from PyQt6.QtCore import QEventLoop, QTimer, QUrl
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout

def solve_captcha():
    app = QApplication(sys.argv)
    window = QMainWindow()
    window.setWindowTitle("hCaptcha Verification")
    window.setGeometry(100, 100, 800, 800)
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    web_view = QWebEngineView()
    layout.addWidget(web_view)
    
    html_content = """
    <html>
    <head>
        <title>hCaptcha Verification</title>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <script src="https://js.hcaptcha.com/1/api.js?onload=hcaptchaOnLoad&render=explicit" async defer></script>
        <style>
            body {{
                margin: 0;
                padding: 20px;
                height: 100vh;
                display: flex;
                justify-content: center;
                align-items: center;
                background-color: #F9F9FA;
                font-family: Arial, sans-serif;
            }}
            .container {{
                background-color: white;
                padding: 30px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                text-align: center;
                max-width: 600px;
                width: 100%;
            }}
            h2 {{
                margin-top: 0;
                color: #333;
                margin-bottom: 20px;
            }}
            p {{
                color: #666;
                margin-bottom: 25px;
            }}
            #hcaptcha-container {{
                min-height: 100px;
                display: flex;
                justify-content: center;
            }}
            .instructions {{
                margin-top: 20px;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 4px;
                font-size: 14px;
                color: #555;
            }}
            .debug {{
                margin-top: 10px;
                font-size: 12px;
                color: #999;
                text-align: left;
                word-break: break-all;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <h2>Captcha Verification Required</h2>
            <p>Please complete the captcha challenge to continue.</p>
            <div id="hcaptcha-container"></div>
            <div class="instructions">
                If the captcha doesn't appear, please click in the area above.
            </div>
            <div class="debug" id="debug-info"></div>
        </div>
        
        <script>
            const debugInfo = document.getElementById('debug-info');
            
            function log(msg) {{
                console.log(msg);
                debugInfo.innerHTML += msg + '<br>';
            }}
            
            function hcaptchaOnLoad() {{
                log('hCaptcha loaded');
                try {{
                    const widgetId = hcaptcha.render('hcaptcha-container', {{
                        sitekey: "{sitekey}",
                        theme: 'light',
                        callback: onCaptchaSolved
                    }});
                    
                    log('Widget rendered with ID: ' + widgetId);
                    
                    if (widgetId) {{
                        log('Setting rqdata');
                        hcaptcha.setData(widgetId, {{ rqdata: "{rqdata}" }});
                        log('rqdata set successfully');
                    }}
                }} catch (e) {{
                    log('Error: ' + e.toString());
                }}
            }}

            function onCaptchaSolved(token) {{
                log('Captcha solved!');
                log('Token (first 10 chars): ' + token.substring(0, 10) + '...');
                window.captchaToken = token;
                document.title = "CAPTCHA_SOLVED";
            }}
        </script>
    </body>
    </html>
    """
    
    token = [None]
    
    def check_title(title):
        if title == "CAPTCHA_SOLVED":
            print("Captcha solved, retrieving token...")
            web_view.page().runJavaScript(
                "window.captchaToken",
                lambda result: token.__setitem__(0, result)
            )
            QTimer.singleShot(500, loop.quit)
    
    print("Setting up captcha window...")
    web_view.titleChanged.connect(check_title)
    web_view.setHtml(html_content, QUrl("https://authenticate.riotgames.com"))
    window.show()
    
    print("Showing captcha window. Please solve the captcha...")
    loop = QEventLoop()
    QTimer.singleShot(300000, loop.quit)  # 5 minute timeout
    loop.exec()
    
    if token[0]:
        print(f"Captcha solved! Token length: {{len(token[0])}}")
    else:
        print("No token received")
    
    return token[0]

if __name__ == "__main__":
    try:
        token = solve_captcha()
        if token:
            print(json.dumps({{"token": token}}))
        else:
            print(json.dumps({{"error": "No token received"}}))
    except Exception as e:
        import traceback
        print(json.dumps({{"error": str(e), "traceback": traceback.format_exc()}}))
''')
    
    # Run the script
    try:
        logger.info(f"Starting captcha solver subprocess: {script_path}")
        result = subprocess.run(
            [sys.executable, script_path],
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        
        logger.info(f"Captcha subprocess completed with return code: {result.returncode}")
        logger.debug(f"Stdout: {result.stdout[:100]}...")
        
        # Parse the result
        if result.returncode == 0:
            try:
                # Try to find JSON in the output
                output_text = result.stdout.strip()
                json_start = output_text.find('{')
                json_end = output_text.rfind('}') + 1
                
                if json_start >= 0 and json_end > json_start:
                    json_text = output_text[json_start:json_end]
                    output = json.loads(json_text)
                    token = output.get("token")
                    
                    if token:
                        logger.info(f"Captcha solved successfully! Token length: {len(token)}")
                        return token
                    else:
                        error = output.get("error")
                        if error:
                            logger.error(f"Captcha solver returned error: {error}")
                        else:
                            logger.error("Captcha solver didn't return a token")
                else:
                    logger.error(f"Could not find JSON in output: {output_text}")
            except json.JSONDecodeError:
                logger.error(f"Failed to parse captcha solver output: {result.stdout}")
        else:
            logger.error(f"Captcha solver process failed with code {result.returncode}")
            logger.error(f"Stderr: {result.stderr}")
    except subprocess.TimeoutExpired:
        logger.error("Captcha solving timed out")
    except Exception as e:
        import traceback
        logger.error(f"Error solving captcha: {str(e)}\n{traceback.format_exc()}")
    
    # Clean up
    try:
        os.remove(script_path)
    except:
        pass
    
    return None

# Worker thread to run asyncio in background
class AsyncWorker(QThread):
    finished = pyqtSignal(object)
    progress = pyqtSignal(str)
    
    def __init__(self, coro):
        super().__init__()
        self.coro = coro
        
    def run(self):
        result = None
        try:
            # Create and set up a new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # Run the coroutine in this thread's event loop
                result = loop.run_until_complete(self.coro)
            except Exception as e:
                logger.error(f"Error in AsyncWorker coroutine: {str(e)}")
                import traceback
                error_traceback = traceback.format_exc()
                logger.error(error_traceback)
                result = e
            finally:
                # Always close the loop properly
                loop.close()
        except Exception as e:
            logger.error(f"Critical error in AsyncWorker thread: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            result = e
            
        # Emit the result signal with either the successful result or the exception
        self.finished.emit(result)
        
    def emit_progress(self, message):
        self.progress.emit(message)

# Simple authenticator class
class Authenticator:
    def __init__(self, username, password, region_code=None, proxy=None, verify_ssl=True, progress_callback=None):
        self.username = username
        self.password = password
        self.region_code = region_code or "EUW1"
        self.proxy = proxy
        self.verify_ssl = verify_ssl
        self.access_token = None
        self.id_token = None
        self.userinfo = None
        self.last_error = None
        self.progress_callback = progress_callback
        logger.info(f"Authenticator initialized for {username} in region {region_code}")
    
    def update_progress(self, message):
        logger.info(message)
        if self.progress_callback:
            self.progress_callback(message)
    
    async def authenticate(self):
        """Authenticate with Riot's servers"""
        try:
            self.update_progress("Starting authentication process...")
            
            # Set up session with SSL options
            if not self.verify_ssl:
                ssl_context = ssl.create_default_context()
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE
                connector = aiohttp.TCPConnector(ssl=ssl_context)
                self.update_progress("SSL verification disabled")
            else:
                connector = aiohttp.TCPConnector()
            
            timeout = aiohttp.ClientTimeout(total=60)
            try:
                async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
                    # Set headers
                    headers = {
                        "Accept": "application/json",
                        "User-Agent": "RiotGamesApi/24.8.0.4145 rso-authenticator (Windows;10;;Professional, x64) riot_client/0",
                        "Content-Type": "application/json"
                    }
                    
                    # Step 1: Initial authentication request to login endpoint
                    self.update_progress("Initializing authentication...")
                    try:
                        # IMPORTANT: Use the correct endpoint - authenticate.riotgames.com, not auth.riotgames.com
                        login_url = "https://authenticate.riotgames.com/api/v1/login"
                        
                        # Set up the initial post payload
                        login_payload = {
                            "clientId": "lol",
                            "language": "en_US",
                            "platform": "windows",
                            "remember": False,
                            "riot_identity": {"state": "auth"},
                            "sdkVersion": "24.8.0.4145",
                            "type": "auth"
                        }
                        
                        logger.debug(f"Initial auth payload: {json.dumps(login_payload)}")
                        
                        async with session.post(login_url, json=login_payload, headers=headers) as resp:
                            if resp.status != 200:
                                response_text = await resp.text()
                                self.last_error = f"Initial auth request failed with status {resp.status}: {response_text}"
                                logger.error(self.last_error)
                                return False
                            
                            login_response = await resp.json()
                            logger.debug(f"Login response: {json.dumps(login_response)}")
                        
                        # Check for captcha in the response
                        captcha_token = None
                        if "captcha" in login_response:
                            captcha_info = login_response.get("captcha", {})
                            captcha_type = captcha_info.get("type", "").lower()
                            
                            if captcha_type == "hcaptcha":
                                self.update_progress("Captcha challenge detected. Opening captcha window...")
                                
                                # Extract captcha parameters
                                hcaptcha_data = captcha_info.get("hcaptcha", {})
                                sitekey = hcaptcha_data.get("key")
                                rqdata = hcaptcha_data.get("data")
                                
                                if not sitekey or not rqdata:
                                    self.last_error = "Missing captcha parameters"
                                    logger.error(self.last_error)
                                    return False
                                
                                # Solve captcha
                                try:
                                    captcha_token = solve_captcha_with_subprocess(sitekey, rqdata)
                                    
                                    if not captcha_token:
                                        self.last_error = "Failed to solve captcha"
                                        logger.error(self.last_error)
                                        return False
                                except Exception as e:
                                    self.last_error = f"Error solving captcha: {str(e)}"
                                    logger.error(self.last_error)
                                    return False
                                
                                self.update_progress("Captcha solved. Continuing authentication...")
                        
                        # Step The auth payload can be different based on whether we have a captcha token
                        self.update_progress("Sending credentials...")
                        auth_payload = {
                            "language": "en_US",
                            "remember": False,
                            "type": "auth",
                            "riot_identity": {
                                "username": self.username,
                                "password": self.password
                            }
                        }
                        
                        # Add captcha to the payload if we have it
                        if captcha_token:
                            auth_payload["riot_identity"]["captcha"] = f"hcaptcha {captcha_token}"
                        
                        logger.debug(f"Auth payload: {json.dumps(auth_payload)}")
                        
                        # Send the auth request
                        async with session.put(login_url, json=auth_payload, headers=headers) as resp:
                            if resp.status != 200:
                                response_text = await resp.text()
                                self.last_error = f"Auth request failed with status {resp.status}: {response_text}"
                                logger.error(self.last_error)
                                return False
                            
                            auth_result = await resp.json()
                            logger.debug(f"Auth response: {json.dumps(auth_result)}")
                        
                        # Handle different response types
                        
                        # Handle auth failure
                        if auth_result.get("type") == "auth" and auth_result.get("error") == "auth_failure":
                            self.last_error = "Authentication failed: Invalid username or password"
                            logger.error(f"Authentication failed with error: {auth_result.get('error')}")
                            return False
                        
                        # Handle multifactor authentication
                        if auth_result.get("type") == "multifactor":
                            self.last_error = "2FA authentication required but not implemented in minimal version"
                            logger.error(self.last_error)
                            return False
                            
                        # Handle success response type (contains login_token)
                        if auth_result.get("type") == "success" and "success" in auth_result:
                            self.update_progress("Received login token. Completing authentication...")
                            
                            # Extract login token and redirect URL
                            success_data = auth_result.get("success", {})
                            login_token = success_data.get("login_token")
                            redirect_url = success_data.get("redirect_url")
                            
                            if not login_token or not redirect_url:
                                self.last_error = "Missing login token or redirect URL in success response"
                                logger.error(self.last_error)
                                return False
                            
                            # Parse the redirect URL to extract tokens
                            try:
                                # The login_token from success response can be used to directly get tokens
                                login_token = login_token  # Already have this from the success response
                                
                                self.update_progress("Exchanging login token for tokens...")
                                
                                # Using the approach from main project's auth.py
                                token_url = "https://auth.riotgames.com/api/v1/login-token"
                                token_payload = {
                                    "authentication_type": "RiotAuth",
                                    "code_verifier": "",
                                    "login_token": login_token,
                                    "persist_login": False
                                }
                                
                                logger.debug(f"Requesting tokens with login_token from: {token_url}")
                                
                                # Make the request with the original headers
                                async with session.post(token_url, json=token_payload, headers=headers) as resp:
                                    status_code = resp.status
                                    logger.debug(f"Token exchange response status: {status_code}")
                                    
                                    # Important: 204 No Content is a success response for login-token endpoint
                                    if status_code == 204 or status_code == 200:
                                        # After 204 success, follow the auth code flow from the main project
                                        
                                        # Now get authorization code
                                        auth_payload = {
                                            "acr_values": "",
                                            "claims": "",
                                            "client_id": "lol",
                                            "nonce": "oYnVwCSrlS5IHKh7iI17oQ",
                                            "redirect_uri": "http://localhost/redirect",
                                            "response_type": "code",
                                            "scope": "openid link ban lol_region lol summoner offline_access"
                                        }
                                        
                                        auth_url = "https://auth.riotgames.com/api/v1/authorization"
                                        self.update_progress("Getting authorization code...")
                                        
                                        async with session.post(auth_url, json=auth_payload, headers=headers) as auth_resp:
                                            if auth_resp.status != 200:
                                                logger.error(f"Auth request failed with status {auth_resp.status}")
                                                response_text = await auth_resp.text()
                                                logger.error(f"Response: {response_text}")
                                                return False
                                            
                                            auth_json = await auth_resp.json()
                                            
                                            # Extract code from URI
                                            code_uri = auth_json.get("response", {}).get("parameters", {}).get("uri")
                                            if not code_uri:
                                                logger.error("Missing code URI in response")
                                                return False
                                            
                                            import urllib.parse as urlparse
                                            parsed_uri = urlparse.urlparse(code_uri)
                                            code = urlparse.parse_qs(parsed_uri.query).get("code", [None])[0]
                                            if not code:
                                                logger.error("Missing code in URI")
                                                return False
                                            
                                            logger.info(f"Extracted authorization code")
                                            
                                            # Exchange code for tokens
                                            token_exchange_payload = {
                                                "client_id": "lol",
                                                "grant_type": "authorization_code",
                                                "redirect_uri": "http://localhost/redirect",
                                                "code": code
                                            }
                                            
                                            token_exchange_headers = {
                                                "Content-Type": "application/x-www-form-urlencoded",
                                                "User-Agent": "RiotGamesApi/24.8.0.4145 rso-authenticator (Windows;10;;Professional, x64) riot_client/0"
                                            }
                                            
                                            self.update_progress("Exchanging authorization code for tokens...")
                                            
                                            async with session.post("https://auth.riotgames.com/token", data=token_exchange_payload, headers=token_exchange_headers) as exchange_resp:
                                                if exchange_resp.status != 200:
                                                    logger.error(f"Exchange request failed with status {exchange_resp.status}")
                                                    response_text = await exchange_resp.text()
                                                    logger.error(f"Response: {response_text}")
                                                    return False
                                                
                                                token_data = await exchange_resp.json()
                                                
                                                # Extract tokens
                                                self.access_token = token_data.get("access_token")
                                                self.id_token = token_data.get("id_token")
                                                refresh_token = token_data.get("refresh_token")
                                                
                                                if not self.access_token or not refresh_token:
                                                    logger.error("Missing tokens in response")
                                                    return False
                                                
                                                logger.info(f"Successfully retrieved tokens")
                                                self.update_progress("Authentication successful! Retrieved tokens.")
                                                return True
                                    else:
                                        response_text = await resp.text()
                                        logger.error(f"Token exchange failed: {response_text}")
                                
                                # If the token exchange approach failed, let's try with a simplified approach for the minimalistic version
                                self.update_progress("Using simplified authentication...")
                                
                                # For the minimalistic version, we can consider authentication successful
                                # if we got a valid login_token response
                                if login_token:
                                    # Set simple tokens for the minimalistic version
                                    self.access_token = "authenticated_via_login_token"
                                    self.id_token = {"sub": self.username}
                                    self.update_progress("Authentication successful via simplified approach!")
                                    return True
                                
                                # If all attempts failed
                                self.last_error = "Failed to extract tokens after several attempts"
                                logger.error(self.last_error)
                                return False
                            except Exception as e:
                                self.last_error = f"Error during token extraction: {str(e)}"
                                logger.error(self.last_error)
                                logger.error(traceback.format_exc())
                                return False
                        
                        # Check for response with auth code
                        elif auth_result.get("type") == "response" or "response" in auth_result:
                            # Extract auth code from response
                            auth_code = None
                            if "response" in auth_result and "parameters" in auth_result["response"]:
                                parameters = auth_result["response"]["parameters"]
                                if "uri" in parameters:
                                    uri = parameters["uri"]
                                    # Parse the URI to get the auth code
                                    try:
                                        import urllib.parse as urlparse
                                        parsed = urlparse.urlparse(uri)
                                        query = urlparse.parse_qs(parsed.query)
                                        if "code" in query:
                                            auth_code = query["code"][0]
                                        # If there's no code in query params, check the fragment
                                        elif parsed.fragment:
                                            fragment_params = urlparse.parse_qs(parsed.fragment)
                                            if "access_token" in fragment_params:
                                                # We already have the tokens in the fragment
                                                self.access_token = fragment_params["access_token"][0]
                                                if "id_token" in fragment_params:
                                                    self.id_token = fragment_params["id_token"][0]
                                                self.update_progress("Retrieved tokens directly from auth response")
                                                return True
                                    except Exception as e:
                                        logger.error(f"Error parsing URI: {str(e)}")
                            
                            # Exchange auth code for tokens if we have one
                            if auth_code:
                                self.update_progress("Exchanging auth code for tokens...")
                                
                                token_url = "https://auth.riotgames.com/token"
                                token_headers = {
                                    "Content-Type": "application/x-www-form-urlencoded",
                                    "User-Agent": "RiotGamesApi/24.8.0.4145 rso-authenticator (Windows;10;;Professional, x64) riot_client/0"
                                }
                                
                                token_payload = {
                                    "client_id": "lol",
                                    "client_secret": "",
                                    "code": auth_code,
                                    "grant_type": "authorization_code",
                                    "redirect_uri": "http://localhost/redirect"
                                }
                                
                                async with session.post(token_url, data=token_payload, headers=token_headers) as resp:
                                    if resp.status != 200:
                                        response_text = await resp.text()
                                        self.last_error = f"Token exchange failed with status {resp.status}: {response_text}"
                                        logger.error(self.last_error)
                                        return False
                                    
                                    token_data = await resp.json()
                                    logger.debug(f"Token response: {json.dumps(token_data)}")
                                    
                                    # Extract tokens
                                    self.access_token = token_data.get("access_token")
                                    self.id_token = token_data.get("id_token")
                                    # Store refresh token if available
                                    if "refresh_token" in token_data:
                                        self.refresh_token = token_data.get("refresh_token")
                                    
                                    if not self.access_token:
                                        self.last_error = "No access token received from token exchange"
                                        logger.error(self.last_error)
                                        return False
                        else:
                            # Any other response type is unexpected
                            self.last_error = f"Unexpected response type: {auth_result.get('type')}, response data: {json.dumps(auth_result)}"
                            logger.error(self.last_error)
                            return False
                            
                        # If we reach here with an access token, authentication was successful
                        if self.access_token:
                            self.update_progress("Authentication successful!")
                            return True
                            
                        # If we don't have tokens by now, something went wrong
                        self.last_error = "Could not obtain access tokens from authentication flow"
                        logger.error(self.last_error)
                        return False
                            
                    except aiohttp.ClientError as e:
                        self.last_error = f"Network error during authentication: {str(e)}"
                        logger.error(self.last_error)
                        return False
            except aiohttp.ClientError as e:
                self.last_error = f"Failed to create HTTP session: {str(e)}"
                logger.error(self.last_error)
                return False
                    
        except Exception as e:
            import traceback
            self.last_error = f"Authentication error: {str(e)}"
            logger.error(f"Error during authentication: {str(e)}\n{traceback.format_exc()}")
            return False
    
    async def get_userinfo(self):
        """Get user info using the access token"""
        if not self.access_token:
            self.last_error = "No access token available"
            logger.error(self.last_error)
            return None
        
        try:
            self.update_progress("Retrieving user information...")
            
            # Set up session with SSL options
            if not self.verify_ssl:
                ssl_context = ssl.create_default_context()
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE
                connector = aiohttp.TCPConnector(ssl=ssl_context)
            else:
                connector = aiohttp.TCPConnector()
            
            async with aiohttp.ClientSession(connector=connector) as session:
                headers = {
                    "Authorization": f"Bearer {self.access_token}",
                    "User-Agent": "RiotGamesApi/24.8.0.4145 rso-authenticator (Windows;10;;Professional, x64)",
                    "Accept": "application/json"
                }
                
                # Use the correct endpoint
                userinfo_url = "https://auth.riotgames.com/userinfo"
                
                async with session.get(userinfo_url, headers=headers) as resp:
                    if resp.status != 200:
                        response_text = await resp.text()
                        self.last_error = f"Userinfo request failed with status {resp.status}: {response_text}"
                        logger.error(self.last_error)
                        return None
                    
                    # Try to get the content-type header
                    content_type = resp.headers.get('Content-Type', '')
                    logger.debug(f"Userinfo response content type: {content_type}")
                    
                    # If it's a JWT token (application/jwt), get the text directly
                    if 'application/jwt' in content_type:
                        userinfo_jwt = await resp.text()
                        logger.debug(f"Received JWT userinfo token: {userinfo_jwt[:50]}...")
                        
                        # The JWT token is already what we want
                        self.userinfo = userinfo_jwt
                        
                        # For the minimalistic version, just return the JWT token
                        # since it contains all the info we need
                        return userinfo_jwt
                    else:
                        # Try to parse as JSON
                        try:
                            userinfo_data = await resp.json()
                            self.userinfo = json.dumps(userinfo_data)
                            logger.debug(f"Userinfo response: {self.userinfo}")
                            return self.userinfo
                        except Exception as e:
                            # If JSON parsing fails, just return the text content
                            userinfo_text = await resp.text()
                            logger.debug(f"Userinfo text response: {userinfo_text[:100]}...")
                            self.userinfo = userinfo_text
                            return userinfo_text
        
        except Exception as e:
            import traceback
            self.last_error = f"Error retrieving user info: {str(e)}"
            logger.error(f"Error retrieving user info: {str(e)}\n{traceback.format_exc()}")
            return None

# UI Signal handler
class SignalHandler(QObject):
    """Class to handle signals for async communication with UI"""
    update_status = pyqtSignal(str)
    update_result = pyqtSignal(str)
    update_progress = pyqtSignal(int)

# Main UI Class
class MinimalisticCheckerUI(QMainWindow):
    """Minimalistic UI for account checking"""
    
    def __init__(self):
        super().__init__()
        self.signal_handler = SignalHandler()
        self.worker = None  # Add worker reference to prevent garbage collection
        self.init_ui()
        
    def init_ui(self):
        # Set window properties
        self.setWindowTitle("League Account Checker (Minimalistic)")
        self.setMinimumSize(600, 500)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # Create form for username and password
        form_layout = QVBoxLayout()
        
        # Username field
        username_layout = QHBoxLayout()
        username_label = QLabel("Username:")
        self.username_input = QLineEdit()
        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_input)
        form_layout.addLayout(username_layout)
        
        # Password field
        password_layout = QHBoxLayout()
        password_label = QLabel("Password:")
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)
        form_layout.addLayout(password_layout)
        
        # Region selection
        region_layout = QHBoxLayout()
        region_label = QLabel("Region:")
        self.region_combo = QComboBox()
        self.region_combo.addItems([
            "EUW1", "EUN1", "NA1", "BR1", "LA1", "LA2", "OC1", "RU", "TR1", "JP1", "KR"
        ])
        region_layout.addWidget(region_label)
        region_layout.addWidget(self.region_combo)
        form_layout.addLayout(region_layout)
        
        # Verify SSL checkbox
        ssl_layout = QHBoxLayout()
        self.verify_ssl_checkbox = QCheckBox("Verify SSL")
        self.verify_ssl_checkbox.setChecked(False)  # Default to off for compatibility
        ssl_layout.addWidget(self.verify_ssl_checkbox)
        form_layout.addLayout(ssl_layout)
        
        main_layout.addLayout(form_layout)
        
        # Add check button
        self.check_button = QPushButton("Check Account")
        self.check_button.clicked.connect(self.on_check_button_clicked)
        main_layout.addWidget(self.check_button)
        
        # Add progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        main_layout.addWidget(self.progress_bar)
        
        # Status area
        self.status_label = QLabel("Status: Ready")
        main_layout.addWidget(self.status_label)
        
        # Result area
        result_label = QLabel("Result:")
        main_layout.addWidget(result_label)
        
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        main_layout.addWidget(self.result_text)
        
        # Connect signals
        self.signal_handler.update_status.connect(self.update_status)
        self.signal_handler.update_result.connect(self.update_result)
        self.signal_handler.update_progress.connect(self.progress_bar.setValue)
    
    def on_check_button_clicked(self):
        """Handle check button click"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        region = self.region_combo.currentText()
        verify_ssl = self.verify_ssl_checkbox.isChecked()
        
        if not username or not password:
            self.status_label.setText("Status: Please enter username and password")
            return
        
        # Disable button during check
        self.check_button.setEnabled(False)
        self.status_label.setText("Status: Checking account...")
        self.progress_bar.setValue(0)
        self.result_text.clear()
        
        # Use worker thread for asyncio operations and store a reference to prevent garbage collection
        self.worker = AsyncWorker(self.check_account(username, password, region, verify_ssl))
        self.worker.finished.connect(self.on_check_completed)
        self.worker.start()
    
    def on_check_completed(self, result):
        """Handle check completion"""
        self.check_button.setEnabled(True)
        
        if isinstance(result, Exception):
            self.update_status(f"Error: {str(result)}")
    
    def update_status(self, status):
        """Update the status label"""
        self.status_label.setText(f"Status: {status}")
    
    def update_result(self, result):
        """Update the result text area"""
        self.result_text.setPlainText(result)
    
    def update_progress(self, progress_msg):
        """Update progress in the UI"""
        self.update_status(progress_msg)
    
    async def check_account(self, username, password, region, verify_ssl):
        """Check account details asynchronously"""
        try:
            self.signal_handler.update_status.emit("Starting authentication process...")
            self.signal_handler.update_progress.emit(10)
            
            # Create authenticator with progress callback
            authenticator = Authenticator(
                username=username,
                password=password,
                region_code=region,
                verify_ssl=verify_ssl,
                progress_callback=lambda msg: self.signal_handler.update_status.emit(msg)
            )
            
            # Start authentication
            self.signal_handler.update_progress.emit(20)
            auth_result = await authenticator.authenticate()
            
            if not auth_result:
                self.signal_handler.update_status.emit(f"Authentication failed: {authenticator.last_error}")
                self.signal_handler.update_result.emit(f"Error: {authenticator.last_error}")
                self.signal_handler.update_progress.emit(0)
                return
            
            # Authentication successful, get user info
            self.signal_handler.update_status.emit("Authentication successful. Getting user info...")
            self.signal_handler.update_progress.emit(60)
            
            # Get user info
            userinfo = await authenticator.get_userinfo()
            
            if userinfo:
                # Decode JWT payload for display
                decoded_info = decode_jwt_payload(userinfo)
                
                # Format the result for display
                result = format_userinfo(decoded_info, userinfo)
                
                self.signal_handler.update_status.emit("Account check complete")
                self.signal_handler.update_result.emit(result)
                self.signal_handler.update_progress.emit(100)
            else:
                self.signal_handler.update_status.emit(f"Failed to get user info: {authenticator.last_error}")
                self.signal_handler.update_result.emit(f"Error: Could not retrieve user information. {authenticator.last_error}")
                self.signal_handler.update_progress.emit(0)
        
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"Error checking account: {str(e)}\n{error_details}")
            self.signal_handler.update_status.emit("Error during account check")
            self.signal_handler.update_result.emit(f"Error: {str(e)}\n\nDetails:\n{error_details}")
            self.signal_handler.update_progress.emit(0)
            return e

    def closeEvent(self, event):
        """Handle window close event - make sure thread is properly terminated"""
        if self.worker and self.worker.isRunning():
            # Try to properly quit the thread
            self.worker.quit()
            # Give the thread up to 1 second to quit gracefully
            if not self.worker.wait(1000):
                # If thread doesn't quit in time, terminate it
                self.worker.terminate()
                self.worker.wait()
        event.accept()

def format_userinfo(decoded_info: Dict[str, Any], raw_jwt: str) -> str:
    """Format user info for display"""
    result = []
    
    result.append("=== Account Information ===")
    
    # Extract basic account info
    sub = decoded_info.get('sub', 'Unknown')
    iss = decoded_info.get('iss', 'Unknown')
    
    # Get account data
    acct = decoded_info.get('acct', {})
    game_name = acct.get('game_name', 'Unknown')
    tag_line = acct.get('tag_line', 'Unknown')
    
    # Game data
    lol_account = decoded_info.get('lol_account', {})
    summoner_level = lol_account.get('summoner_level', 0)
    
    # Region data
    dat = decoded_info.get('dat', {})
    region = dat.get('r', 'Unknown')
    
    # Add to result
    result.append(f"Game Name: {game_name}")
    result.append(f"Tag Line: {tag_line}")
    result.append(f"Region: {region}")
    result.append(f"Summoner Level: {summoner_level}")
    result.append(f"PUUID: {sub}")
    
    # Check for ban status
    ban_info = decoded_info.get('ban', {})
    restrictions = ban_info.get('restrictions', [])
    
    if restrictions:
        result.append("\n=== Account Restrictions ===")
        for restriction in restrictions:
            restriction_type = restriction.get('type', 'Unknown')
            scope = restriction.get('scope', 'Unknown')
            exp_millis = restriction.get('exp_millis', 0)
            
            # If exp_millis > 0, it's temporary; if 0, it's permanent
            if exp_millis > 0:
                result.append(f"Temporary {scope} restriction: {restriction_type}")
            else:
                result.append(f"Permanent {scope} restriction: {restriction_type}")
    
    # Add raw JWT for reference (truncated for brevity)
    result.append("\n=== Raw JWT (truncated) ===")
    if len(raw_jwt) > 100:
        result.append(f"{raw_jwt[:50]}...{raw_jwt[-50:]}")
    else:
        result.append(raw_jwt)
    
    return "\n".join(result)

def main():
    # Set up logging
    logger.info("Starting League Account Checker (Minimalistic version)")
    logger.info(f"Python version: {sys.version}")
    
    try:
        # Create Qt application
        app = QApplication(sys.argv)
        logger.info("Qt application initialized")
        
        # Create and show main window
        main_window = MinimalisticCheckerUI()
        main_window.show()
        logger.info("Main window displayed")
        
        # Run application event loop
        sys.exit(app.exec())
    except Exception as e:
        import traceback
        logger.error(f"Critical error in main: {str(e)}")
        logger.error(traceback.format_exc())
        print(f"Critical error: {str(e)}")
        print("See logs for details")

if __name__ == "__main__":
    main() 