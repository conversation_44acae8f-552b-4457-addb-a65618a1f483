"""
Account manager utility functions
"""
from accounts.account import Account

def create_account_from_data(account_data, db_manager=None):
    """
    Create an Account object from account data
    
    Args:
        account_data: Dictionary of account data
        db_manager: Optional database manager to associate with the account
        
    Returns:
        Account object
    """
    # Extract basic account info
    username = account_data.get('username', '')
    password = account_data.get('password', '')
    region = account_data.get('region', '')
    
    # Create kwargs with all other account data
    kwargs = {
        'summoner_level': account_data.get('summoner_level', 0),
        'blue_essence': account_data.get('blue_essence', 0),
        'riot_points': account_data.get('riot_points', 0),
        'game_name': account_data.get('game_name', ''),
        'tag_line': account_data.get('tag_line', ''),
        'is_banned': bool(account_data.get('is_banned', False)),
        'ban_info': account_data.get('ban_info', ''),
        'created_at': account_data.get('created_at', ''),
        'creation_region': account_data.get('creation_region', ''),
        'country': account_data.get('country', ''),
        'last_checked': account_data.get('last_checked', ''),
        'penalty_minutes': account_data.get('penalty_minutes', 0),
        'champions': account_data.get('champions', []),
        'skins': account_data.get('skins', []),
        'season_history': account_data.get('season_history', []),
        'db_manager': db_manager
    }
    
    # Create and return account object
    return Account(username, password, region, **kwargs) 