#!/usr/bin/env python3
"""
Test script to verify chroma detection improvements
"""

import sys
import os
import json

# Add the accounts directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'accounts'))

from html_gui import HTMLGenerator

def test_chroma_detection():
    """Test the improved chroma detection"""
    print("=== Testing Improved Chroma Detection ===")
    
    # Create an HTMLGenerator instance
    generator = HTMLGenerator()
    
    # Test cases from the logs - these should be detected as chromas
    test_cases = [
        (105005, "Fizz (Scorch)"),  # Fizz Scorch chroma
        (107019, "Pretty Kitty Rengar (Chroma)"),  # Pretty Kitty Rengar chroma
        (19018, "<PERSON> Warwick (Chroma)"),  # Grey Warwick chroma
        (37057, "Victorious Sona (Bronze)"),  # Victorious Sona Bronze
        (37058, "Victorious Sona (Silver)"),  # Victorious Sona Silver
        (37059, "Victorious Sona (Gold)"),  # Victorious Sona Gold
        (55015, "Battle Academia Katarina (Chroma)"),  # Battle Academia Katarina chroma
        (55019, "Battle Academia Katarina (Chroma)"),  # Battle Academia Katarina chroma
        (29021, "Medieval Twitch (Chroma)"),  # Medieval Twitch chroma
        (29023, "Medieval Twitch (Chroma)"),  # Medieval Twitch chroma
        (29024, "Medieval Twitch (Chroma)"),  # Medieval Twitch chroma
        (19023, "Grey Warwick (Chroma)"),  # Grey Warwick chroma
        (19025, "Grey Warwick (Chroma)"),  # Grey Warwick chroma
    ]
    
    # Test cases that should NOT be detected as chromas
    non_chroma_cases = [
        (98051, "Aatrox Skin"),  # Regular skin
        (80004, "Akali Skin"),  # Regular skin
        (107015, "Rengar Skin"),  # Regular skin
        (37056, "Victorious Sona"),  # Main Victorious Sona skin
    ]
    
    print("\n--- Testing Chromas (should return True) ---")
    for skin_id, expected_name in test_cases:
        skin_name = generator._get_skin_name(skin_id)
        is_chroma = generator._is_chroma(skin_id, skin_name)
        
        print(f"ID: {skin_id:6} | Name: {skin_name:35} | Is Chroma: {is_chroma:5} | Expected: True")
        
        if not is_chroma:
            print(f"  ❌ ERROR: {skin_id} should be detected as chroma!")
    
    print("\n--- Testing Regular Skins (should return False) ---")
    for skin_id, expected_name in non_chroma_cases:
        skin_name = generator._get_skin_name(skin_id)
        is_chroma = generator._is_chroma(skin_id, skin_name)
        
        print(f"ID: {skin_id:6} | Name: {skin_name:35} | Is Chroma: {is_chroma:5} | Expected: False")
        
        if is_chroma:
            print(f"  ❌ ERROR: {skin_id} should NOT be detected as chroma!")
    
    print("\n--- Summary ---")
    print("If no ERROR messages appeared above, the chroma detection is working correctly!")

if __name__ == "__main__":
    test_chroma_detection()
