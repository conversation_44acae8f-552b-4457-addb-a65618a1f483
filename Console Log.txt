Microsoft Windows [Version 10.0.19045.5965]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\Desktop\sources\League Acc Checker>py main.py
←[97m[2025-07-02 23:23:00] [←[0m←[92m$←[0m←[97m] Created DatabaseManagerWrapper with path accounts.db
←[0m←[97m[2025-07-02 23:23:00] [←[0m←[92m$←[0m←[97m] Wrapped db_manager with DatabaseManagerWrapper in AccountPool
←[0m←[97m[2025-07-02 23:23:00] [←[0m←[92m$←[0m←[97m] Loaded 1941 skin entries from JSON database
←[0mDEBUG: Created CustomWebEngineView
DEBUG: Created web view with custom context menu handling
Loaded 1941 skin entries from JSON database
DEBUG: Sharing champion database with 171 entries to PyQtBridge and AccountPool
←[97m[2025-07-02 23:23:02] [←[0m←[92m$←[0m←[97m] Loading account eossuper122 from database
←[0m←[97m[2025-07-02 23:23:02] [←[0m←[92m$←[0m←[97m] Account details: BE=725, RP=0, Level=33
←[0m←[97m[2025-07-02 23:23:02] [←[0m←[92m$←[0m←[97m] Account info: created_at=19/06/2020 13:04:16, creation_region=EUW, country=egy
←[0m←[97m[2025-07-02 23:23:02] [←[0m←[92m$←[0m←[97m] Game info: game_name=xXMultiBushesXx, tag_line=EUW
←[0m←[97m[2025-07-02 23:23:02] [←[0m←[92m$←[0m←[97m] Checking JWT token for region information for eossuper122
←[0m←[97m[2025-07-02 23:23:02] [←[0m←[92m$←[0m←[97m] JWT region debugging for eossuper122:
←[0m←[97m[2025-07-02 23:23:02] [←[0m←[92m$←[0m←[97m]   lol field: [{'cuid': ****************, 'cpid': 'EUW1', 'uid': ****************, 'uname': 'Eossuper122', 'ptrid': None, 'pid': 'EUW1', 'state': 'ENABLED'}]
←[0m←[97m[2025-07-02 23:23:02] [←[0m←[92m$←[0m←[97m]   region field: {}
←[0m←[97m[2025-07-02 23:23:02] [←[0m←[92m$←[0m←[97m]   original_platform_id:
←[0m←[97m[2025-07-02 23:23:02] [←[0m←[92m$←[0m←[97m]   lol_region: [{'active': True, 'cpid': 'EUW1', 'cuid': ****************, 'lp': False, 'pid': 'EUW1', 'uid': ****************}]
←[0m←[97m[2025-07-02 23:23:02] [←[0m←[92m$←[0m←[97m]   dat field: {}
←[0m←[97m[2025-07-02 23:23:02] [←[0m←[92m$←[0m←[97m] Using lol_region[].cpid to determine region for eossuper122: EUW
←[0m←[97m[2025-07-02 23:23:02] [←[0m←[92m$←[0m←[97m] Found region in JWT for eossuper122: EUW
←[0m←[97m[2025-07-02 23:23:02] [←[0m←[92m$←[0m←[97m] Loaded 1 season history entries for eossuper122
←[0m←[97m[2025-07-02 23:23:02] [←[0m←[92m$←[0m←[97m] Using region=EUW, region_code=EUW for account eossuper122
←[0m←[97m[2025-07-02 23:23:02] [←[0m←[92m$←[0m←[97m] Loaded 1 accounts from database
←[0m←[97m[2025-07-02 23:23:06] [←[0m←[92m$←[0m←[97m] Starting to recheck account: eossuper122
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Checking JWT token for region during recheck for eossuper122
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Using lol_region[].cpid from JWT to determine region: EUW
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Updated region to EUW based on JWT token for account eossuper122
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Initializing authenticator for account: eossuper122
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Extracted JWT payload for eossuper122, keys: ['sub', 'country', 'country_at', 'amr', 'iss', 'lol', 'phone_number_verified', 'locale', 'nonce', 'account_verified', 'aud', 'acr', 'lol_region', 'player_locale', 'exp', 'iat', 'acct', 'age', 'jti', 'login_country']
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] JWT region debugging for eossuper122:
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m]   lol field: [{'cuid': ****************, 'cpid': 'EUW1', 'uid': ****************, 'uname': 'Eossuper122', 'ptrid': None, 'pid': 'EUW1', 'state': 'ENABLED'}]
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m]   region field: {}
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m]   original_platform_id:
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m]   lol_region: [{'active': True, 'cpid': 'EUW1', 'cuid': ****************, 'lp': False, 'pid': 'EUW1', 'uid': ****************}]
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m]   dat field: {}
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Using lol_region[].cpid to determine region for eossuper122: EUW
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Found region in JWT for eossuper122: EUW
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Initializing authenticator for eossuper122 with region EUW
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Authenticator initialized for eossuper122 in region EUW
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Set existing tokens from account object for account: eossuper122
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Using refresh token for eossuper122: yes
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Authenticator initialized for eossuper122 in region EUW
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Set existing tokens from account object for account: eossuper122
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Using refresh token for eossuper122: yes
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Rechecking account: eossuper122
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Starting check for account eossuper122 with region EUW
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Getting userinfo for eossuper122
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Using access token for userinfo: eyJraWQiOi...m86NV4IkIw
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Access token length: 1233
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Userinfo request URL: https://auth.riotgames.com/userinfo
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Userinfo request headers: Authorization: Bearer eyJraWQiOi...m86NV4IkIw, Accept: application/json, Content-Type: application/json
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[93m!←[0m←[97m] Error getting userinfo: 401 Unauthorized - Token is likely expired or invalid. Response:
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[93m!←[0m←[97m] Full response headers: Headers({'date': 'Wed, 02 Jul 2025 21:23:08 GMT', 'content-length': '0', 'connection': 'keep-alive', 'x-rsorouterclusterid': 'ec1', 'x-rso-error-id': '40101121', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=3CiuEelket7mC1sXP67PQYd7Em2ySELFBZhF4wrtK94-1751491388-*******-9nlyW_W3CXyV7oYq0g0XKoU1N1Za8I5jrVDUsCLtsHFB7vTeCle1UST5qI.mRGIGDWEC7Y6yHRyaXnTfyYpWEkKb3G3WO4yv5dlqwirR48s; path=/; expires=Wed, 02-Jul-25 21:53:08 GMT; domain=.riotgames.com; HttpOnly; Secure; SameSite=None', 'x-riotgames-cdn': 'Cloudflare', 'server': 'cloudflare', 'cf-ray': '959133dabd2fcb22-DUS'})
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[93m!←[0m←[97m] Empty userinfo response for eossuper122, attempting re-auth
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Got account info for eossuper122: {'username': 'eossuper122', 'password': 'i&%nOf*6hQqh$jra', 'region': 'EUW', 'refresh_token': 'eyJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwiYWxnIjoiZGlyIn0..5VE69wZEpjFABnN7ESxbfA.nZKwtG-lGg4tQgCwVXCMev6EtNtqFccfWzdFFQWhNgXV3MUKyrcopGyZ8Cp-hhGNyaW_V0ZWk7OA-W1CzjEWujEbsMkgDGmj0qt91JaFjEkYdZX7iueKsAGdBoV8tZZa2IEcRy5BC0_tZJPIq5RSA2U9emAsSCu8NiwNnrcQl_3Vn8Hn9ZJWpvyE71xlQ_nf3l-73RUwRWXC-gy20FwRjorS57M_ylnqPspAxVwZIH4_TcC1sinn-tjN5vlB8RhzsjlaX-NlPWA5kHhSdtrPaFKn25ev-8Iczb8hEQAkxEsDbL0r2EchoKGVyZpKnOHqN5E0uY1sCmMsa_C_pNdumsyhXi91ec5ycCFKZSUaVbANYmlouskPW98JjA0Jh9b4nItVOn8J_7vT2Uos2WDjo6v-MkjURYzzw1sSZ1nCm0GJodIqmEpr-aBniQ15ra-zeI4OwvWBipIpeUUrcVHyYSfXN3KKQSEiFF-bm33yXpTWBinU9InbMsHQzX-jBN5OEduJ5crvicwlWmDbw5Jb6yuF9w9GN9qyObb8bA3SiHJJRbbNm5BAJXkBGSna2ZfdpBAosTw8lBA36Ft6vRMxmY0yCn7rgTwEUCGubJs0SJsmTk7U0WgbF12VL4uEAxX-RiuDKelMGF65Jq_864F0csRgE6Y4Fyg_BEtAX2gkT2mBpReuV8VnDVNNFP1jAIEElwoCOI_vZJtBvl_SiRh56VQQZjZeFGfSFRgIAE5hXcagLSxFv7_Vhf7oX7CInjRrLj5mjwjhEwLbosB5UZbn7mGQfSJkj2_1yRN0ZjESXvkrsUvuU8GtTiR4NGuNlw1Nmz4yXL7U1lO3F_po1yD_tRFNq38I4fpNgBfgTlNp5QvWaoWNFBY7zHB8TGD9fXcdt2u8cxSJVU32pecu5XA88ZBBT7X9VXzbXHinMi2MSGV8TENwfRuCFfF4EdWvqMGicMrtjJ0usfCGSx80E-wHQA.lLm3SDn4RnnakcSz-mW-fA', 'access_token': 'eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwiYWxnIjoiUlMyNTYifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YIjxTjN7JYRvZIP2Gx7pPQxjH2iZAglVHyktoKkWanr5DSauxdjs9-LzSE5AO80fzLymXJDkQKguYhmm5JlfDAFAF9nnjHsfjp80tP80w6OoM-gigq8wmtCKvG9Rv0T1j9xhHTIjpX-IcRNRJ9G2kuOxZVuwKmEabcKBG2eKx3un5BKH-X2XMQXs8K3AQgfb98MVeAMbKsd0oGUkB8DLfYUVbE0SQPD7gqyO0CgJDGydfvn1XxREBjrc9R01mG7FG3C3vOb973Z6jWwiXlTIOVPwn2HuN3j18kcmtYYB_NuXnTwkMD56BWUG1ETNoFbqGyIJMg45TrE_m86NV4IkIw', 'id_token': 'eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwidHlwIjoiaWRfdG9rZW4rand0IiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZbmfnQ1jEfHdxw4lT0ACJV7LFi8tkQ_jq8wisO71Cv36d1MLtqUtHYTwWUxEgM_mZLj_HrVFtRoakcRYq3VM2wZIAQyMWVLbFZ1C9o7U3HGBRJxKewsfPSinFyNhP9H-7af4cWWh-SJ_wFORn3K_OW86eLix1tJXzSBFCpLsHkuh26uPNpntCeA6vm1LBSh51_kGJeymUzpj0jHuYMsHGw-1d7B9eXi-6MR5A9KtMDF92qFqwa-BST-3o5ql55Pb0gL-GmsX78ErJunVYZQwldMzG6vZc7hnxtGwTLSgsWfqvlSzz8A7vU7O32ilTeLcGlbVqS8eN6ZC7pCcsVVEww', 'entitlements_token': None, 'userinfo': None, 'timestamp': 261, 'puuid': None, 'account_id': None, 'game_name': 'xXMultiBushesXx', 'tag_line': 'EUW', 'champion_count': 37, 'blue_essence': 725, 'riot_points': 0, 'summoner_level': 33, 'created_at': '19/06/2020 13:04:16', 'last_checked': '2025-07-02 21:51:45', 'penalty_minutes': 0, 'ranked_games_remaining': 3, 'is_banned': 0, 'ban_info': 'Glitch Account - Appeared banned but working: TEXT_CHAT_RESTRICTION - INAPPROPRIATE_TEXT, Expires: 2025-07-09 05:59:23, REPUTATION_LIMIT - INAPPROPRIATE_TEXT, Expires: 1970-01-01 01:00:00', 'country': 'egy', 'creation_region': 'EUW', 'solo_tier': 'UNRANKED', 'solo_division': '', 'solo_lp': 0, 'solo_wins': 0, 'solo_losses': 0, 'solo_previous_tier': 'SILVER', 'solo_previous_division': 'II', 'flex_tier': 'UNRANKED', 'flex_division': '', 'flex_lp': 0, 'flex_wins': 0, 'flex_losses': 0, 'flex_previous_tier': 'UNRANKED', 'flex_previous_division': '', 'chat_restrictions': 'TEXT_CHAT_RESTRICTION - INAPPROPRIATE_TEXT (expires: *************)', 'is_glitch_account': 1, 'region_code': 'EUW', 'rank_info': {'solo': {'tier': 'UNRANKED', 'division': '', 'lp': 0, 'wins': 0, 'losses': 0, 'previous_tier': 'SILVER', 'previous_division': 'II'}, 'flex': {'tier': 'UNRANKED', 'division': '', 'lp': 0, 'wins': 0, 'losses': 0, 'previous_tier': 'UNRANKED', 'previous_division': ''}, 'season_history': [{'season': 10, 'split': None, 'queue_type': 'SOLO_DUO', 'peak_tier': 'SILVER', 'peak_division': 'II', 'end_tier': 'SILVER', 'end_division': 'II'}]}}
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Using refresh token for eossuper122: yes
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] Attempting to use refresh token for eossuper122
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] REFRESH DEBUG - Payload: {'client_id': 'lol', 'grant_type': 'refresh_token', 'refresh_token': 'eyJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwiYWxnIjoiZGlyIn0..5VE69wZEpjFABnN7ESxbfA.nZKwtG-lGg4tQgCwVXCMev6EtNtqFccfWzdFFQWhNgXV3MUKyrcopGyZ8Cp-hhGNyaW_V0ZWk7OA-W1CzjEWujEbsMkgDGmj0qt91JaFjEkYdZX7iueKsAGdBoV8tZZa2IEcRy5BC0_tZJPIq5RSA2U9emAsSCu8NiwNnrcQl_3Vn8Hn9ZJWpvyE71xlQ_nf3l-73RUwRWXC-gy20FwRjorS57M_ylnqPspAxVwZIH4_TcC1sinn-tjN5vlB8RhzsjlaX-NlPWA5kHhSdtrPaFKn25ev-8Iczb8hEQAkxEsDbL0r2EchoKGVyZpKnOHqN5E0uY1sCmMsa_C_pNdumsyhXi91ec5ycCFKZSUaVbANYmlouskPW98JjA0Jh9b4nItVOn8J_7vT2Uos2WDjo6v-MkjURYzzw1sSZ1nCm0GJodIqmEpr-aBniQ15ra-zeI4OwvWBipIpeUUrcVHyYSfXN3KKQSEiFF-bm33yXpTWBinU9InbMsHQzX-jBN5OEduJ5crvicwlWmDbw5Jb6yuF9w9GN9qyObb8bA3SiHJJRbbNm5BAJXkBGSna2ZfdpBAosTw8lBA36Ft6vRMxmY0yCn7rgTwEUCGubJs0SJsmTk7U0WgbF12VL4uEAxX-RiuDKelMGF65Jq_864F0csRgE6Y4Fyg_BEtAX2gkT2mBpReuV8VnDVNNFP1jAIEElwoCOI_vZJtBvl_SiRh56VQQZjZeFGfSFRgIAE5hXcagLSxFv7_Vhf7oX7CInjRrLj5mjwjhEwLbosB5UZbn7mGQfSJkj2_1yRN0ZjESXvkrsUvuU8GtTiR4NGuNlw1Nmz4yXL7U1lO3F_po1yD_tRFNq38I4fpNgBfgTlNp5QvWaoWNFBY7zHB8TGD9fXcdt2u8cxSJVU32pecu5XA88ZBBT7X9VXzbXHinMi2MSGV8TENwfRuCFfF4EdWvqMGicMrtjJ0usfCGSx80E-wHQA.lLm3SDn4RnnakcSz-mW-fA', 'scope': 'openid offline_access lol ban lol_region'}
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] REFRESH DEBUG - Headers: {'Content-Type': 'application/x-www-form-urlencoded', 'User-Agent': 'RiotClient/63.0.9.4909983.4789131 rso-auth (Windows;10;;Professional, x64)', 'Accept': 'application/json'}
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] REFRESH DEBUG - URL: https://auth.riotgames.com/token
←[0m←[97m[2025-07-02 23:23:07] [←[0m←[92m$←[0m←[97m] REFRESH DEBUG - Method: POST
←[0m←[97m[2025-07-02 23:23:09] [←[0m←[91m-←[0m←[97m] Error refreshing token: [SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate (_ssl.c:1028)
←[0m←[97m[2025-07-02 23:23:09] [←[0m←[91m-←[0m←[97m] Refresh token failed - no access token returned
←[0m←[97m[2025-07-02 23:23:09] [←[0m←[92m$←[0m←[97m] Falling back to password auth for eossuper122
←[0m←[97m[2025-07-02 23:23:09] [←[0m←[92m$←[0m←[97m] Solve captcha starting for eossuper122
←[0m←[97m[2025-07-02 23:23:09] [←[0m←[92m$←[0m←[97m] Initial post payload: {'clientId': 'lol', 'language': 'en_US', 'platform': 'windows', 'remember': False, 'riot_identity': {'state': 'auth'}, 'sdkVersion': '24.8.0.4145', 'type': 'auth'}
←[0m←[97m[2025-07-02 23:23:09] [←[0m←[92m$←[0m←[97m] Post response status: 200
←[0m←[97m[2025-07-02 23:23:09] [←[0m←[92m$←[0m←[97m] Got captcha challenge with sitekey: 019f1553-3845-481c-a6f5-5a60ccf6d830
←[0m←[97m[2025-07-02 23:23:09] [←[0m←[92m$←[0m←[97m] Starting captcha solver subprocess
←[0m←[97m[2025-07-02 23:23:13] [←[0m←[92m$←[0m←[97m] Captcha solver process completed
←[0m←[97m[2025-07-02 23:23:13] [←[0m←[92m$←[0m←[97m] Got captcha token: P1_eyJ0eXAiOiJKV1QiL
←[0m←[97m[2025-07-02 23:23:14] [←[0m←[92m$←[0m←[97m] Extracted login token
←[0m←[97m[2025-07-02 23:23:14] [←[0m←[92m$←[0m←[97m] Extracted authorization code
←[0m←[97m[2025-07-02 23:23:14] [←[0m←[92m$←[0m←[97m] Successfully retrieved tokens for eossuper122
←[0m←[97m[2025-07-02 23:23:14] [←[0m←[92m$←[0m←[97m] Solve captcha result: True
←[0m←[97m[2025-07-02 23:23:14] [←[0m←[92m$←[0m←[97m] Extracted region from JWT lol_region[].cpid: EUW
←[0m←[97m[2025-07-02 23:23:14] [←[0m←[92m$←[0m←[97m] Using access token for userinfo: eyJraWQiOi...e4f0yHuiMg
←[0m←[97m[2025-07-02 23:23:14] [←[0m←[92m$←[0m←[97m] Access token length: 1233
←[0m←[97m[2025-07-02 23:23:14] [←[0m←[92m$←[0m←[97m] Userinfo request URL: https://auth.riotgames.com/userinfo
←[0m←[97m[2025-07-02 23:23:14] [←[0m←[92m$←[0m←[97m] Userinfo request headers: Authorization: Bearer eyJraWQiOi...e4f0yHuiMg, Accept: application/json, Content-Type: application/json
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Raw userinfo response (first 100 chars): eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwidHlwIjoidXNlcmluZm8rand0IiwiYWxnIjoiUlMyNTYifQ.eyJjb3VudHJ5IjoiZ
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Userinfo response length: 2586
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Received JWT token for userinfo
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] JWT token has 3 parts
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Successfully decoded JWT payload with 25 keys
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Ban info in JWT payload: {
  "restrictions": [
    {
      "type": "TEXT_CHAT_RESTRICTION",
      "reason": "INAPPROPRIATE_TEXT",
      "scope": "lol",
      "dat": {
        "expirationMillis": *************,
        "gameData": {
          "productName": "lol",
          "gameLocation": "EUW1",
          "triggerGameId": "**********",
          "additionalGameIds": []
        },
        "gameLocation": "EUW1"
      }
    },
    {
      "type": "REPUTATION_LIMIT",
      "reason": "INAPPROPRIATE_TEXT",
      "scope": "lol",
      "dat": {
        "expirationMillis": 0,
        "gameData": {
          "productName": "lol",
          "gameLocation": "EUW1",
          "triggerGameId": "**********",
          "additionalGameIds": []
        },
        "gameLocation": "EUW1"
      }
    }
  ]
}
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Found 2 ban restrictions
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Ban info: TEXT_CHAT_RESTRICTION - INAPPROPRIATE_TEXT
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Ban info: REPUTATION_LIMIT - INAPPROPRIATE_TEXT
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Successfully decoded JWT payload with 25 keys
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Restriction TEXT_CHAT_RESTRICTION for eossuper122 is temporary, expires at *************
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Found ban info: TEXT_CHAT_RESTRICTION - INAPPROPRIATE_TEXT
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Restriction REPUTATION_LIMIT for eossuper122 is permanent
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Restriction REPUTATION_LIMIT for eossuper122 is a game restriction
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Found ban info: REPUTATION_LIMIT - INAPPROPRIATE_TEXT
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Account eossuper122 is banned: TEXT_CHAT_RESTRICTION - INAPPROPRIATE_TEXT, REPUTATION_LIMIT - INAPPROPRIATE_TEXT
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Successfully authenticated eossuper122 with password
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Using access token for userinfo: eyJraWQiOi...e4f0yHuiMg
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Access token length: 1233
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Userinfo request URL: https://auth.riotgames.com/userinfo
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Userinfo request headers: Authorization: Bearer eyJraWQiOi...e4f0yHuiMg, Accept: application/json, Content-Type: application/json
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Raw userinfo response (first 100 chars): eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwidHlwIjoidXNlcmluZm8rand0IiwiYWxnIjoiUlMyNTYifQ.eyJjb3VudHJ5IjoiZ
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Userinfo response length: 2586
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Received JWT token for userinfo
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] JWT token has 3 parts
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Successfully decoded JWT payload with 25 keys
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Ban info in JWT payload: {
  "restrictions": [
    {
      "type": "TEXT_CHAT_RESTRICTION",
      "reason": "INAPPROPRIATE_TEXT",
      "scope": "lol",
      "dat": {
        "expirationMillis": *************,
        "gameData": {
          "productName": "lol",
          "gameLocation": "EUW1",
          "triggerGameId": "**********",
          "additionalGameIds": []
        },
        "gameLocation": "EUW1"
      }
    },
    {
      "type": "REPUTATION_LIMIT",
      "reason": "INAPPROPRIATE_TEXT",
      "scope": "lol",
      "dat": {
        "expirationMillis": 0,
        "gameData": {
          "productName": "lol",
          "gameLocation": "EUW1",
          "triggerGameId": "**********",
          "additionalGameIds": []
        },
        "gameLocation": "EUW1"
      }
    }
  ]
}
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Found 2 ban restrictions
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Ban info: TEXT_CHAT_RESTRICTION - INAPPROPRIATE_TEXT
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Ban info: REPUTATION_LIMIT - INAPPROPRIATE_TEXT
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Successfully decoded JWT payload with 25 keys
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Restriction TEXT_CHAT_RESTRICTION for eossuper122 is temporary, expires at *************
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Found ban info: TEXT_CHAT_RESTRICTION - INAPPROPRIATE_TEXT
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Restriction REPUTATION_LIMIT for eossuper122 is permanent
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Restriction REPUTATION_LIMIT for eossuper122 is a game restriction
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Found ban info: REPUTATION_LIMIT - INAPPROPRIATE_TEXT
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Account eossuper122 is banned: TEXT_CHAT_RESTRICTION - INAPPROPRIATE_TEXT, REPUTATION_LIMIT - INAPPROPRIATE_TEXT
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Got userinfo for eossuper122, decoding JWT
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Account eossuper122 has restrictions
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Restriction TEXT_CHAT_RESTRICTION for eossuper122 is temporary, expires at *************
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Chat restriction found for eossuper122: TEXT_CHAT_RESTRICTION - INAPPROPRIATE_TEXT (expires: *************)
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Restriction REPUTATION_LIMIT for eossuper122 is permanent
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Game restriction REPUTATION_LIMIT for eossuper122: INAPPROPRIATE_TEXT
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Saved chat restrictions for eossuper122 to database
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Account eossuper122 appears to be banned: TEXT_CHAT_RESTRICTION - INAPPROPRIATE_TEXT, Expires: 2025-07-09 05:59:23, REPUTATION_LIMIT - INAPPROPRIATE_TEXT, Expires: 1970-01-01 01:00:00
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Testing next request for eossuper122 to check if it's a glitch account
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Getting tokens for eossuper122
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Invalid JSON in userinfo for get_queue_token: Expecting value: line 1 column 1 (char 0)
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - URL: https://euc1-red.pp.sgp.pvp.net/login-queue/v2/login/products/lol/regions/EUW1
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Region: EUW, Region Upper: EUW1
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Player Platform Edge URL: https://euc1-red.pp.sgp.pvp.net
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Headers: {"Authorization": "Bearer eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwiYWxnIjoiUlMyNTYifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Hh7Ax5Dh9vIrLBh3CuJk3xEJpZJfNt-ah_rwneO03fk9CXF8gw0iTmT-2qyrYQPWgvFmNUCOzuMLGb5fquw1CrsZpVzVa9rzRlE2iBBwLErOU8aArJ0cJhBh9GbuP8AttTs_3wguMsZQqXmm8uxrCjGpYDf9go9sZy7S-MgtsbsDB8ngmDyMs1IFhEJrjElt2KT0mNhJ4YdSSIcaMVn8xAo4o7F_-KdLtA-wADutK4p8Ju_O4o0u4hH3zYOesZ2H3z-7PEapZ6fBxCG68wgy4eeA88J0rdES-CtuYmEIEmWKhYn-g3HbnJjHr2wGTDuRapqu1RnidcjBe4f0yHuiMg", "Accept": "application/json", "Content-Type": "application/json"}
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Body: {"clientName": "lcu", "entitlements": "*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "userinfo": "eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwidHlwIjoidXNlcmluZm8rand0IiwiYWxnIjoiUlMyNTYifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.KEpzP22bnXKD86i3iycmOhSF5A0rw3QWGbo9LoeqrKrFHZDs2C8CgC0ruPM8ymUsOfyf02cHiGxGt8X7uMbQz8jUjotMo6j7sIzzs6LXuhuqdQIczunJnC6YFmGXfQJ7qgTgJGj36vEmi9KaA4hmg3iIRnpmqIhOc8K8-UFH7VFPFHDt939s_e-Bcto-M_nMF1zDOmswy3bFqxd_avBNBl3E33fljt-PCFVvHjrRYUI0fP2bnlB19S_nxSWxg9Kq2d0p3-ZgMnN_lW9OnK4uV3RyG5pEdLEu6AEhI4JVcKc1UkPcmyuaRr7hm48oNf-L-IiixttQaeSvbwv9HoDwVw"}
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Status: 200
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Headers: {"date": "Wed, 02 Jul 2025 21:23:16 GMT", "content-type": "application/json;charset=utf-8", "transfer-encoding": "chunked", "connection": "keep-alive", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "expires": "0", "pragma": "no-cache", "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers, accept-encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-riot-edge-trace-id": "7f7040da-cb7b-4cca-aa94-9ab74c85f515", "x-xss-protection": "0", "content-encoding": "gzip", "access-control-allow-origin": "*", "access-control-allow-methods": "GET, PUT, DELETE, POST, OPTIONS", "access-control-allow-headers": "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range", "access-control-expose-headers": "Content-Length,Content-Range", "cf-cache-status": "DYNAMIC", "set-cookie": "__cf_bm=Y_Eqwt247He45EOQTnKfadahrwaAprsXxrggQ9xI6jE-**********-*******-vlsiI0OA7fSshFf7xHOn5wv1fBf83LOoC0883v_mmt73Sk3taGLTOQ1kLW1Udib7jNrvUifpkmeZeo8YGdUbggtkvUuUxK56TDvTLvMVilg; path=/; expires=Wed, 02-Jul-25 21:53:16 GMT; domain=.sgp.pvp.net; HttpOnly; Secure; SameSite=None", "server": "cloudflare", "cf-ray": "9591340c6d46160d-DUS"}
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Body: {"token": "eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.N0rhqwhBR7W_zbl3J7rDTXPgl6I4pA90Eo_37zpH6odG-eDaF4NAXzXf2QT_hyRbhzPVV3pND0-Fl0Qh1EQYVrl6Ln81F1Jn6ba7vzJFY5bJuWcmDx9T89woTjsRa1c4al1s7lhjdsALU3q5_-XONM-jInqjr4TFpGwqdE4FDMdcIzvP45vuI9f5QoZM5S8Ts4VWtnorpfzGNfLQ6x4ki9hTW4oIMREVo5a7-8h39hgEUte7aZTrsRJJLg0z5w9Rg8lDHLsX85PmYqwS85Mi4sm8pp_nnBiAmyXUvCxBtEgmQjBtcgb7QX6-CKqkv-uIsaYUcIKms7rLDKQRgPFaNQ", "type": "LOGIN"}
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Invalid JSON in userinfo for get_session_token: Expecting value: line 1 column 1 (char 0)
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Got JWT token from queue_token
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Parsed access token, PUUID: 3928514f-82f7-5b72-9590-efa3e6893ce7
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - URL: https://euc1-red.pp.sgp.pvp.net/session-external/v1/session/create
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Region: EUW, Region Upper: EUW1
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Player Platform Edge URL: https://euc1-red.pp.sgp.pvp.net
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Headers: {"Authorization": "Bearer eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.N0rhqwhBR7W_zbl3J7rDTXPgl6I4pA90Eo_37zpH6odG-eDaF4NAXzXf2QT_hyRbhzPVV3pND0-Fl0Qh1EQYVrl6Ln81F1Jn6ba7vzJFY5bJuWcmDx9T89woTjsRa1c4al1s7lhjdsALU3q5_-XONM-jInqjr4TFpGwqdE4FDMdcIzvP45vuI9f5QoZM5S8Ts4VWtnorpfzGNfLQ6x4ki9hTW4oIMREVo5a7-8h39hgEUte7aZTrsRJJLg0z5w9Rg8lDHLsX85PmYqwS85Mi4sm8pp_nnBiAmyXUvCxBtEgmQjBtcgb7QX6-CKqkv-uIsaYUcIKms7rLDKQRgPFaNQ", "Accept": "application/json", "Content-Type": "application/json"}
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Body: {"claims": {"cname": "lcu"}, "product": "lol", "puuid": "3928514f-82f7-5b72-9590-efa3e6893ce7", "region": "EUW1"}
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Status: 200
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Headers: {"date": "Wed, 02 Jul 2025 21:23:16 GMT", "content-type": "application/json;charset=utf-8", "transfer-encoding": "chunked", "connection": "keep-alive", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "expires": "0", "pragma": "no-cache", "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers, accept-encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-riot-edge-trace-id": "2107af62-7016-4bb3-ae96-fedffc994921", "x-xss-protection": "0", "content-encoding": "gzip", "access-control-allow-origin": "*", "access-control-allow-methods": "GET, PUT, DELETE, POST, OPTIONS", "access-control-allow-headers": "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range", "access-control-expose-headers": "Content-Length,Content-Range", "cf-cache-status": "DYNAMIC", "server": "cloudflare", "cf-ray": "9591340ccd73160d-DUS"}
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Text: "eyJraWQiOiJzMSIsImFsZyI6IlJTMjU2In0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.lPYiVD186EGN8_hxqzHu7Rifc0OmCHIdPSh8QQ44WTAE5vypnt6-UIXQ5S6EEOQkgJ9En6ZvZaaPYUYcorY_UeWSexDZjKhAL0fJsO5LwfOvUwg8GGfPNQ2lE3AOi0F08_xZNpg35SMJorwWWwlQd7dHc50mR3jC9hLb9OUVnFGKjZ0bQaZdyqNWfNzP8wiJWNPLjb4KPOjBmcGtMKWyujt2_asSrHOtwd3MWxyuOpwlc-6y7YHMMSkjax_Xd1jHexgtWHCKAhWZVdUNScvSiNo3Q0_n9h3iO_c1tarmVXGs-QD-aFyIWmQ2dMPaadP9T8ivVslccdGlJlp8mE6RwA"
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Got all tokens for eossuper122
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Account eossuper122 appeared banned but tokens work - it's a glitch account, continuing check
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Importing get_owned_champions for eossuper122
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Using existing region code for champion data retrieval: EUW for eossuper122
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Calling get_owned_champions for eossuper122 with region EUW
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Getting owned champions for region: EUW
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Headers set up: Authorization and Entitlements tokens configured
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] TCP Connector created with IPv4 family and SSL disabled
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Trying URL pattern 1/4
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Using base URL: https://euw-red.lol.sgp.pvp.net
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Inventory URL: https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v2/inventoriesWithLoyalty
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Wallet URL: https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v1/walletsbalances
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Location format: lolriot.aws-euc1-prod.euw
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Inventory params: {'puuid': '3928514f-82f7-5b72-9590-efa3e6893ce7', 'accountId': '****************', 'inventoryTypes': 'CHAMPION', 'location': 'lolriot.aws-euc1-prod.euw'}
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Attempting to resolve hostname: euw-red.lol.sgp.pvp.net
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Successfully resolved euw-red.lol.sgp.pvp.net to *************
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Successfully retrieved inventory data with pattern 1
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Found 37 owned champions
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Requesting wallet data from: https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v1/walletsbalances
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Wallet params: {'puuid': '3928514f-82f7-5b72-9590-efa3e6893ce7', 'location': 'lolriot.aws-euc1-prod.euw', 'accountId': '****************', 'currencyTypes': ['RP', 'lol_blue_essence', 'tft_star_fragments']}
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Successfully retrieved wallet data with pattern 1
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Successfully retrieved all data with pattern 1
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[90m@←[0m←[97m] Session closed properly
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Returned from get_owned_champions for eossuper122
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Extracted creation date for eossuper122: 19/06/2020 13:04:16 from timestamp *************
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Extracted creation region for eossuper122: EUW from tag euw
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Saving comprehensive account info for eossuper122: BE=725, RP=0, Level=33
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Saving 37 champions for eossuper122
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Getting skin data for eossuper122
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Using existing region code for skin data retrieval: EUW for eossuper122
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Getting owned skins for account 3928514f-82f7-5b72-9590-efa3e6893ce7
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Restriction TEXT_CHAT_RESTRICTION for eossuper122 is temporary, expires at *************
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Found ban info: TEXT_CHAT_RESTRICTION - INAPPROPRIATE_TEXT
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Restriction REPUTATION_LIMIT for eossuper122 is permanent
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Restriction REPUTATION_LIMIT for eossuper122 is a game restriction
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Found ban info: REPUTATION_LIMIT - INAPPROPRIATE_TEXT
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Account eossuper122 is banned: TEXT_CHAT_RESTRICTION - INAPPROPRIATE_TEXT, REPUTATION_LIMIT - INAPPROPRIATE_TEXT
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Found account for puuid 3928514f-82f7-5b72-9590-efa3e6893ce7
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Restriction TEXT_CHAT_RESTRICTION for eossuper122 is temporary, expires at *************
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Found ban info: TEXT_CHAT_RESTRICTION - INAPPROPRIATE_TEXT
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Restriction REPUTATION_LIMIT for eossuper122 is permanent
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Restriction REPUTATION_LIMIT for eossuper122 is a game restriction
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Found ban info: REPUTATION_LIMIT - INAPPROPRIATE_TEXT
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Account eossuper122 is banned: TEXT_CHAT_RESTRICTION - INAPPROPRIATE_TEXT, REPUTATION_LIMIT - INAPPROPRIATE_TEXT
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Extracted account ID **************** from userinfo token
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Invalid JSON in userinfo for get_queue_token: Expecting value: line 1 column 1 (char 0)
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - URL: https://euc1-red.pp.sgp.pvp.net/login-queue/v2/login/products/lol/regions/EUW1
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Region: EUW, Region Upper: EUW1
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Player Platform Edge URL: https://euc1-red.pp.sgp.pvp.net
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Headers: {"Authorization": "Bearer eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwiYWxnIjoiUlMyNTYifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Hh7Ax5Dh9vIrLBh3CuJk3xEJpZJfNt-ah_rwneO03fk9CXF8gw0iTmT-2qyrYQPWgvFmNUCOzuMLGb5fquw1CrsZpVzVa9rzRlE2iBBwLErOU8aArJ0cJhBh9GbuP8AttTs_3wguMsZQqXmm8uxrCjGpYDf9go9sZy7S-MgtsbsDB8ngmDyMs1IFhEJrjElt2KT0mNhJ4YdSSIcaMVn8xAo4o7F_-KdLtA-wADutK4p8Ju_O4o0u4hH3zYOesZ2H3z-7PEapZ6fBxCG68wgy4eeA88J0rdES-CtuYmEIEmWKhYn-g3HbnJjHr2wGTDuRapqu1RnidcjBe4f0yHuiMg", "Accept": "application/json", "Content-Type": "application/json"}
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Body: {"clientName": "lcu", "entitlements": "*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "userinfo": "eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwidHlwIjoidXNlcmluZm8rand0IiwiYWxnIjoiUlMyNTYifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.KEpzP22bnXKD86i3iycmOhSF5A0rw3QWGbo9LoeqrKrFHZDs2C8CgC0ruPM8ymUsOfyf02cHiGxGt8X7uMbQz8jUjotMo6j7sIzzs6LXuhuqdQIczunJnC6YFmGXfQJ7qgTgJGj36vEmi9KaA4hmg3iIRnpmqIhOc8K8-UFH7VFPFHDt939s_e-Bcto-M_nMF1zDOmswy3bFqxd_avBNBl3E33fljt-PCFVvHjrRYUI0fP2bnlB19S_nxSWxg9Kq2d0p3-ZgMnN_lW9OnK4uV3RyG5pEdLEu6AEhI4JVcKc1UkPcmyuaRr7hm48oNf-L-IiixttQaeSvbwv9HoDwVw"}
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Status: 200
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Headers: {"date": "Wed, 02 Jul 2025 21:23:16 GMT", "content-type": "application/json;charset=utf-8", "transfer-encoding": "chunked", "connection": "keep-alive", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "expires": "0", "pragma": "no-cache", "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers, accept-encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-riot-edge-trace-id": "96f7c892-2a2f-4cef-bc17-405f65448548", "x-xss-protection": "0", "content-encoding": "gzip", "access-control-allow-origin": "*", "access-control-allow-methods": "GET, PUT, DELETE, POST, OPTIONS", "access-control-allow-headers": "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range", "access-control-expose-headers": "Content-Length,Content-Range", "cf-cache-status": "DYNAMIC", "server": "cloudflare", "cf-ray": "9591340ece67160d-DUS"}
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Body: {"token": "eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.a55xSWG4qTn3Seh5PIediEovFqQkqnQVITMOYA4EPUPq4ECYz0jixW4_M1alsXHc0mgc3bENdrY63w50UmJ13I2eC-xR0Yfo1-EzLHMIgHL9BF792D0ql-1TT0c-jSrt9rXknAZPynLxMp3mH2V2YgB955oAepsP7mUMNQ1NbGVq0c_jf195tbf2iIZAPkftwetC_k-BXqYi2yr8mk7cz7v3XN-0Vgj6tkpzUGWfIZ6QU1Mv30kpDAV9Vb3NIdwuMc2E5-6-XC60gDfOK-hIUJmYgaGCQbro4FYOjlF7050ulBXM6F-IZLdKdjW2tQ4_tsX-Bza04noaQUEh-D2psA", "type": "LOGIN"}
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[93m!←[0m←[97m] Invalid JSON in userinfo for get_session_token: Expecting value: line 1 column 1 (char 0)
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Got JWT token from queue_token
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Parsed access token, PUUID: 3928514f-82f7-5b72-9590-efa3e6893ce7
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - URL: https://euc1-red.pp.sgp.pvp.net/session-external/v1/session/create
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Region: EUW, Region Upper: EUW1
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Player Platform Edge URL: https://euc1-red.pp.sgp.pvp.net
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Headers: {"Authorization": "Bearer eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.a55xSWG4qTn3Seh5PIediEovFqQkqnQVITMOYA4EPUPq4ECYz0jixW4_M1alsXHc0mgc3bENdrY63w50UmJ13I2eC-xR0Yfo1-EzLHMIgHL9BF792D0ql-1TT0c-jSrt9rXknAZPynLxMp3mH2V2YgB955oAepsP7mUMNQ1NbGVq0c_jf195tbf2iIZAPkftwetC_k-BXqYi2yr8mk7cz7v3XN-0Vgj6tkpzUGWfIZ6QU1Mv30kpDAV9Vb3NIdwuMc2E5-6-XC60gDfOK-hIUJmYgaGCQbro4FYOjlF7050ulBXM6F-IZLdKdjW2tQ4_tsX-Bza04noaQUEh-D2psA", "Accept": "application/json", "Content-Type": "application/json"}
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Body: {"claims": {"cname": "lcu"}, "product": "lol", "puuid": "3928514f-82f7-5b72-9590-efa3e6893ce7", "region": "EUW1"}
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Status: 200
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Headers: {"date": "Wed, 02 Jul 2025 21:23:17 GMT", "content-type": "application/json;charset=utf-8", "transfer-encoding": "chunked", "connection": "keep-alive", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "expires": "0", "pragma": "no-cache", "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers, accept-encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-riot-edge-trace-id": "b5ea4ca5-847d-4681-88f8-41e86ca93759", "x-xss-protection": "0", "content-encoding": "gzip", "access-control-allow-origin": "*", "access-control-allow-methods": "GET, PUT, DELETE, POST, OPTIONS", "access-control-allow-headers": "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range", "access-control-expose-headers": "Content-Length,Content-Range", "cf-cache-status": "DYNAMIC", "server": "cloudflare", "cf-ray": "9591340f2e91160d-DUS"}
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Text: "eyJraWQiOiJzMSIsImFsZyI6IlJTMjU2In0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bM53uhuvdIVtVHWqZi0bpt8U-kxj1G1cIyKYkGZuMQGFjbAcdzsE0BrhsA_MuXbgdk5BaJajCAnoRyKuGI9ArdsloXfUuKHi09sVYT4Eb645aGg3usbHXSja2oD8t_WPhXEbpMRoHOMfAPcQ1ctHjLvvt4Dx-neYLitPiV2MpgxsS6BbPuz1kOCsDowW94WCuwP5CUBgagRDZLqoQ3rZzo1QO7QUec2BAvCMPKdMsxMEESEfKFSQqhkS7O7GTFzaYMFSW_ltIW0r3hFKW3tc9orBEiHZus-DlUbWGAqyk71-t_RBPVsbC2JUtj1KQ3qdMnGwunf1WCoOrN62bqo0fw"
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Successfully retrieved all tokens for skin retrieval
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Making request to https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v2/inventoriesWithLoyalty for skin data
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Successfully retrieved skin data response
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Found items in skin data response: {'CHAMPION_SKIN': [{'itemId': 20002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200710T144752.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200710T152143.000Z', 'entitlementId': '31336166-6439-6466-2d30-3939612d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '17ac24ea-d9f3-44f5-8f35-21358c928254', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}]}
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Found CHAMPION_SKIN in items: [{'itemId': 20002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200710T144752.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200710T152143.000Z', 'entitlementId': '31336166-6439-6466-2d30-3939612d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '17ac24ea-d9f3-44f5-8f35-21358c928254', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}]
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] CHAMPION_SKIN is a list with 1 items
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Loaded 1941 skin entries from JSON database
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Loaded skin database with 1941 entries
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 20002
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Categorized skin 20002 as: skin - Workshop Nunu & Willump
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Extracted 1 skins from response
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 20002
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Categorized skin 20002 as: skin - Workshop Nunu & Willump
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Saving 1 skins for eossuper122
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Got skin data for eossuper122
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Checking penalties for eossuper122
←[0m←[97m[2025-07-02 23:23:15] [←[0m←[92m$←[0m←[97m] Checking penalties for EUW using URL: https://euw-red.lol.sgp.pvp.net/leaverbuster-ledge/restrictionInfo
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Penalty check response: {'puuid': '3928514f-82f7-5b72-9590-efa3e6893ce7', 'rankedRestrictionEntryDto': {'puuid': '3928514f-82f7-5b72-9590-efa3e6893ce7', 'version': 2, 'restrictedGamesRemaining': 3, 'restrictedGamesOriginal': 3, 'rankedRestrictionPenaltyId': 'f07c204d-56f8-11f0-b735-e7a42cba5f39', 'punishmentIncurredGameId': **********, 'punishmentIncurredTimeMillis': 1751428762508, 'rankedRestrictionAckNeeded': False, 'penaltyOrigin': 'PDS'}, 'leaverBusterEntryDto': {'puuid': '3928514f-82f7-5b72-9590-efa3e6893ce7', 'version': 4, 'tainted': True, 'preLockoutAckNeeded': False, 'onLockoutAckNeeded': False, 'leaverScore': 0, 'leaverLevel': 0, 'punishedGamesRemaining': 0, 'currentPunishmentStep': 0, 'leaverPenalty': {'puuid': '3928514f-82f7-5b72-9590-efa3e6893ce7', 'hasActivePenalty': False, 'delayTime': 0, 'queueLockoutTimerExpiryUtcMillis': 0, 'punishmentTimerType': 'NO_PENALTY', 'rankRestrictedGamesRemaining': 3, 'rankRestrictedTimerExpiryUtcMillis': 0, 'rankRestricted': True}, 'warnSentMillis': 1594766445829, 'warnAckedMillis': 1594766473380, 'lastUpdatedMillis': 1751428649964, 'totalPunishedGamesPlayed': 0, 'lastBustedGameId': 0, 'lastBustedTimeMillis': 0, 'lastPunishmentIncurredGameId': 0, 'lastPunishmentIncurredTimeMillis': 0, 'processedGameIdHistoryString': '0,7448777056,7448795090,**********'}}
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] No active penalty
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Found ranked restriction: 3 games remaining
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Got penalty info for eossuper122: 0 minutes, 3 games
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Checking rank for xXMultiBushesXx#EUW
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Using region code EUW for rank info retrieval
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[90m@←[0m←[97m] get_rank_info called with region_code: EUW, type: <class 'str'>
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[90m@←[0m←[97m] Getting rank info for xXMultiBushesXx#EUW in region EUW
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Decoded userinfo: {
  "country": "egy",
  "sub": "3928514f-82f7-5b72-9590-efa3e6893ce7",
  "pw": {
    "cng_at": *************,
    "reset": false,
    "must_reset": false
  },
  "lol": {
    "cuid": ****************,
    "cpid": "EUW1",
    "uid": ****************,
    "pid": "EUW1",
    "apid": null,
    "ploc": "en",
    "lp": false,
    "active": true
  },
  "original_platform_id": "EUW1",
  "iss": "https://auth.riotgames.com",
  "original_account_id": ****************,
  "preferred_username": "Eossuper122",
  "ban": {
    "restrictions": [
      {
        "type": "TEXT_CHAT_RESTRICTION",
        "reason": "INAPPROPRIATE_TEXT",
        "scope": "lol",
        "dat": {
          "expirationMillis": *************,
          "gameData": {
            "productName": "lol",
            "gameLocation": "EUW1",
            "triggerGameId": "**********",
            "additionalGameIds": []
          },
          "gameLocation": "EUW1"
        }
      },
      {
        "type": "REPUTATION_LIMIT",
        "reason": "INAPPROPRIATE_TEXT",
        "scope": "lol",
        "dat": {
          "expirationMillis": 0,
          "gameData": {
            "productName": "lol",
            "gameLocation": "EUW1",
            "triggerGameId": "**********",
            "additionalGameIds": []
          },
          "gameLocation": "EUW1"
        }
      }
    ]
  },
  "player_locale": "en",
  "iat": **********,
  "jti": "9icjDZkYDP0",
  "lol_account": {
    "summoner_id": *********,
    "profile_icon": 3543,
    "summoner_level": 33,
    "summoner_name": ""
  },
  "email_verified": true,
  "player_plocale": null,
  "country_at": *************,
  "phone_number_verified": false,
  "photo": "https://avatar.leagueoflegends.com/euw/.png",
  "ppid": null,
  "aud": "lol",
  "lol_region": [
    {
      "cuid": ****************,
      "cpid": "EUW1",
      "uid": ****************,
      "pid": "EUW1",
      "lp": false,
      "active": true
    }
  ],
  "pvpnet_account_id": ****************,
  "region": {
    "locales": [
      "de_DE",
      "en_GB",
      "es_ES",
      "fr_FR",
      "it_IT"
    ],
    "id": "EUW1",
    "tag": "euw"
  },
  "acct": {
    "type": 0,
    "state": "ENABLED",
    "adm": false,
    "game_name": "xXMultiBushesXx",
    "tag_line": "EUW",
    "created_at": *************
  },
  "username": "Eossuper122"
}
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Found country from userinfo: egy
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Current rank data response: {'queues': [{'queueType': 'RANKED_SOLO_5x5', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_FLEX_SR', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_TFT', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_TFT_TURBO', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_TFT_DOUBLE_UP', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}], 'earnedRegaliaRewardIds': [], 'splitsProgress': {}, 'seasons': {'RANKED_TFT': {'currentSeasonId': 29, 'currentSeasonEnd': 1753848001000, 'nextSeasonStart': 0}, 'RANKED_TFT_TURBO': {'currentSeasonId': 29, 'currentSeasonEnd': 1753848001000, 'nextSeasonStart': 0}, 'RANKED_FLEX_SR': {'currentSeasonId': 19, 'currentSeasonEnd': 1756249199000, 'nextSeasonStart': 0}, 'RANKED_TFT_DOUBLE_UP': {'currentSeasonId': 29, 'currentSeasonEnd': 1753848001000, 'nextSeasonStart': 0}, 'RANKED_SOLO_5x5': {'currentSeasonId': 19, 'currentSeasonEnd': 1756249199000, 'nextSeasonStart': 0}}, 'jwt': '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'}
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[90m@←[0m←[97m] scrape_account_info called with region_code: EUW, type: <class 'str'>
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[90m@←[0m←[97m] Scraping account info from: https://www.leagueofgraphs.com/summoner/euw/xXMultiBushesXx-EUW
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[90m@←[0m←[97m] Got HTML response length: 601334
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Found 1 season tags
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Processing tag: S10 Silver with tooltip: <itemname class='tagTitle brown'>S10 Silver</itemname><br/><span class='tagDescription'><highlight>Ranked Solo/Duo</highlight><br/>This player reached Silver II during Season 10. At the end of the season, this player was Silver II.<br/></span>
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Found previous season rank from LeagueOfGraphs: {'tier': 'SILVER', 'division': 'II'}
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Stored complete season history with 1 entries
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Processed rank info: {'solo': {'tier': 'UNRANKED', 'division': '', 'lp': 0, 'wins': 0, 'losses': 0, 'previous_tier': 'SILVER', 'previous_division': 'II'}, 'flex': {'tier': 'UNRANKED', 'division': '', 'lp': 0, 'wins': 0, 'losses': 0, 'previous_tier': 'UNRANKED', 'previous_division': ''}, 'country': 'egy', 'creation_region': 'EUW', 'season_history': [{'season': 10, 'split': None, 'queue_type': 'SOLO_DUO', 'peak_tier': 'SILVER', 'peak_division': 'II', 'end_tier': 'SILVER', 'end_division': 'II'}]}
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Got rank info for eossuper122
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Including 1 season history entries for eossuper122
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Saving rank info for eossuper122: Solo=UNRANKED , Flex=UNRANKED
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Successfully rechecked account: eossuper122
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Loading account eossuper122 from database
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Account details: BE=725, RP=0, Level=33
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Account info: created_at=19/06/2020 13:04:16, creation_region=EUW, country=egy
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Game info: game_name=xXMultiBushesXx, tag_line=EUW
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Checking JWT token for region information for eossuper122
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] JWT region debugging for eossuper122:
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m]   lol field: [{'cuid': ****************, 'cpid': 'EUW1', 'uid': ****************, 'uname': 'Eossuper122', 'ptrid': None, 'pid': 'EUW1', 'state': 'ENABLED'}]
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m]   region field: {}
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m]   original_platform_id:
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m]   lol_region: [{'active': True, 'cpid': 'EUW1', 'cuid': ****************, 'lp': False, 'pid': 'EUW1', 'uid': ****************}]
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m]   dat field: {}
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Using lol_region[].cpid to determine region for eossuper122: EUW
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Found region in JWT for eossuper122: EUW
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Loaded 1 season history entries for eossuper122
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Using region=EUW, region_code=EUW for account eossuper122
←[0m←[97m[2025-07-02 23:23:16] [←[0m←[92m$←[0m←[97m] Loaded 1 accounts from database
←[0m