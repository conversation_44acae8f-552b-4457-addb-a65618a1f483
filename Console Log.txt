
C:\Users\<USER>\Desktop\sources\League Acc Checker>py main.py
←[97m[2025-07-02 05:55:50] [←[0m←[92m$←[0m←[97m] Created DatabaseManagerWrapper with path accounts.db
←[0m←[97m[2025-07-02 05:55:50] [←[0m←[92m$←[0m←[97m] Wrapped db_manager with DatabaseManagerWrapper in AccountPool
←[0m←[97m[2025-07-02 05:55:50] [←[0m←[92m$←[0m←[97m] Loaded 1941 skin entries from JSON database
←[0mDEBUG: Created CustomWebEngineView
DEBUG: Created web view with custom context menu handling
Loaded 1941 skin entries from JSON database
DEBUG: Sharing champion database with 171 entries to PyQtBridge and AccountPool
←[97m[2025-07-02 05:55:51] [←[0m←[92m$←[0m←[97m] Loaded 0 accounts from database
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Importing accounts from clipboard
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Loading account spanglebob from database
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Account details: BE=0, RP=0, Level=0
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Account info: created_at=None, creation_region=None, country=None
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Game info: game_name=None, tag_line=None
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Loaded 0 season history entries for spanglebob
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] No region information found for spanglebob, leaving region as None
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Using region=None, region_code=None for account spanglebob
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Loaded 1 accounts from database
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Successfully imported 1 accounts from clipboard
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Loading account spanglebob from database
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Account details: BE=0, RP=0, Level=0
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Account info: created_at=None, creation_region=None, country=None
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Game info: game_name=None, tag_line=None
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Loaded 0 season history entries for spanglebob
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] No region information found for spanglebob, leaving region as None
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Using region=None, region_code=None for account spanglebob
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Loaded 1 accounts from database
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Starting to recheck account: spanglebob
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Initializing authenticator for account: spanglebob
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Initializing authenticator for spanglebob with region None
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[93m!←[0m←[97m] No region code provided for spanglebob, authentication may fail
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Authenticator initialized for spanglebob in region
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Authenticator initialized for spanglebob in region None
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] No refresh token available for spanglebob
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Rechecking account: spanglebob
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Starting check for account spanglebob with region None
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Getting userinfo for spanglebob
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Got account info for spanglebob: {'username': 'spanglebob', 'password': 'Wolves10', 'region': '', 'refresh_token': '', 'access_token': None, 'id_token': None, 'entitlements_token': None, 'userinfo': None, 'timestamp': 1289, 'puuid': None, 'account_id': None, 'game_name': None, 'tag_line': None, 'champion_count': 0, 'blue_essence': 0, 'riot_points': 0, 'summoner_level': 0, 'created_at': None, 'last_checked': '2025-07-02 03:55:53', 'penalty_minutes': 0, 'ranked_games_remaining': 0, 'is_banned': 0, 'ban_info': None, 'country': None, 'creation_region': None, 'solo_tier': 'UNRANKED', 'solo_division': '', 'solo_lp': 0, 'solo_wins': 0, 'solo_losses': 0, 'solo_previous_tier': 'UNRANKED', 'solo_previous_division': '', 'flex_tier': 'UNRANKED', 'flex_division': '', 'flex_lp': 0, 'flex_wins': 0, 'flex_losses': 0, 'flex_previous_tier': 'UNRANKED', 'flex_previous_division': '', 'chat_restrictions': None, 'is_glitch_account': 0, 'region_code': None}
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Using refresh token for spanglebob: no
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Falling back to password auth for spanglebob
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Solve captcha starting for spanglebob
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Initial post payload: {'clientId': 'lol', 'language': 'en_US', 'platform': 'windows', 'remember': False, 'riot_identity': {'state': 'auth'}, 'sdkVersion': '24.8.0.4145', 'type': 'auth'}
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Post response status: 200
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Got captcha challenge with sitekey: 019f1553-3845-481c-a6f5-5a60ccf6d830
←[0m←[97m[2025-07-02 05:55:53] [←[0m←[92m$←[0m←[97m] Starting captcha solver subprocess
←[0m←[97m[2025-07-02 05:55:58] [←[0m←[92m$←[0m←[97m] Captcha solver process completed
←[0m←[97m[2025-07-02 05:55:58] [←[0m←[92m$←[0m←[97m] Got captcha token: P1_eyJ0eXAiOiJKV1QiL
←[0m←[97m[2025-07-02 05:55:58] [←[0m←[92m$←[0m←[97m] Extracted login token
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Extracted authorization code
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Successfully retrieved tokens for spanglebob
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Solve captcha result: True
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Extracted region from JWT lol_region[].cpid: EUW
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Updating region from  to EUW based on JWT token
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Using access token for userinfo: eyJraWQiOi...06-mrSfRMw
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Access token length: 1224
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Userinfo request URL: https://auth.riotgames.com/userinfo
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Userinfo request headers: Authorization: Bearer eyJraWQiOi...06-mrSfRMw, Accept: application/json, Content-Type: application/json
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Raw userinfo response (first 100 chars): eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwidHlwIjoidXNlcmluZm8rand0IiwiYWxnIjoiUlMyNTYifQ.eyJjb3VudHJ5IjoiZ
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Userinfo response length: 2260
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Received JWT token for userinfo
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] JWT token has 3 parts
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Successfully decoded JWT payload with 25 keys
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[93m!←[0m←[97m] Ban info in JWT payload: {
  "restrictions": [
    {
      "type": "QUEUE_LOCKOUT",
      "reason": "QUEUE_DODGE",
      "scope": "lol",
      "dat": {
        "expirationMillis": *************,
        "gameData": {
          "productName": "lol",
          "gameLocation": "EUW1",
          "triggerGameId": "7449412788",
          "additionalGameIds": []
        },
        "gameLocation": "EUW1"
      }
    }
  ]
}
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[93m!←[0m←[97m] Found 1 ban restrictions
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[93m!←[0m←[97m] Ban info: QUEUE_LOCKOUT - QUEUE_DODGE
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Successfully decoded JWT payload with 25 keys
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Restriction QUEUE_LOCKOUT for spanglebob is temporary, expires at *************
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[93m!←[0m←[97m] Restriction QUEUE_LOCKOUT for spanglebob is a game restriction
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[93m!←[0m←[97m] Found ban info: QUEUE_LOCKOUT - QUEUE_DODGE
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[93m!←[0m←[97m] Account spanglebob is banned: QUEUE_LOCKOUT - QUEUE_DODGE
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Successfully authenticated spanglebob with password
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Restriction QUEUE_LOCKOUT for spanglebob is temporary, expires at *************
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[93m!←[0m←[97m] Restriction QUEUE_LOCKOUT for spanglebob is a game restriction
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[93m!←[0m←[97m] Found ban info: QUEUE_LOCKOUT - QUEUE_DODGE
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[93m!←[0m←[97m] Account spanglebob is banned: QUEUE_LOCKOUT - QUEUE_DODGE
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Got userinfo for spanglebob, decoding JWT
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[91m-←[0m←[97m] Error decoding JWT payload: 'utf-8' codec can't decode bytes in position 1248-1249: invalid continuation byte
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Getting tokens for spanglebob
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[91m-←[0m←[97m] Error parsing userinfo string in get_queue_token: Expecting value: line 1 column 1 (char 0)
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - URL: https://euc1-red.pp.sgp.pvp.net/login-queue/v2/login/products/lol/regions/EUW1
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Region: EUW, Region Upper: EUW1
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Player Platform Edge URL: https://euc1-red.pp.sgp.pvp.net
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Headers: {"Authorization": "Bearer eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwiYWxnIjoiUlMyNTYifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.gseG6-aBP9WxKh3GRW4QmlYbDlKupgcr-FRsrfCZqnSNCkIhk8N1kvPLit2_7AhUcBL2hCjVh7j3_LwW0TFGHoQxIwowt44e2QmVBrlERGiBtlXBJvD22iGagtDxKbLIApByfkaDyfKj67Wnbv8PfVT9dSF1jdMVJQBhhXAwtNVvgpEH3mIREmd7cohE9K0DEEXxd10enHQqmPu2pXtb_25SpF6-e-r0RD98d-Sqm-liw8NZnhzUbDqeiBqFcS1h4Tv8Ir9swZF72-zuDmsVCr0InzJZdfb2HxZGNhj3eayF1rNvclWgkx4Nxpp08rq_eLlan7ys2uWt06-mrSfRMw", "Accept": "application/json", "Content-Type": "application/json"}
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Body: {"clientName": "lcu", "entitlements": "*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "userinfo": "eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwidHlwIjoidXNlcmluZm8rand0IiwiYWxnIjoiUlMyNTYifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************_ph4_ph4_ph4_ph4_ph4_ph4_ph4_ph4_ph4_ph4_ph4_ph4_ph4_ph4_ph48iLCJ0YWdfbGluZSI6IuWLh-WLh-WLh-WLh-WLhyIsImNyZWF0ZWRfYXQiOjE0ODAyOTEyMDAwMDB9LCJ1c2VybmFtZSI6InNwYW5nbGVib2IifQ.uEj8RmB4X9iHSgopDb14uE0CkT9qCGorr7UcYKh_0gKdfqnCqGn5CPOfcLgd_SwmF-c5Tfx23VcAr1Xcn10Ws2Lnq9W4_ZGbdPXHmCaSNVw6xc9BWjXO7gJNPCdtUckUxDJo3uFpszzOZrX9jniNXUevygL_8vEVs4o6RNU72yv3VDi_t0lg9ifkVmDwQ3Vqcv5G5gWImHOX0xqkmGACwLT7kc5ugjrg7nV1KAiAGv7oCnV0EAaGpxXPhpWVTgRltsp33G8zTHOSsbwVuSF7HQPDw1dTAXb2u3It1qUKTpMhN2y2EM77GzO6S5Je4F9zQYQtvvl29cPvOa-4Ty1_cQ"}
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Status: 200
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Headers: {"date": "Wed, 02 Jul 2025 03:56:00 GMT", "content-type": "application/json;charset=utf-8", "transfer-encoding": "chunked", "connection": "keep-alive", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "expires": "0", "pragma": "no-cache", "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers, accept-encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-riot-edge-trace-id": "1beffe43-2167-473c-b4b8-ccb39541b597", "x-xss-protection": "0", "content-encoding": "gzip", "access-control-allow-origin": "*", "access-control-allow-methods": "GET, PUT, DELETE, POST, OPTIONS", "access-control-allow-headers": "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range", "access-control-expose-headers": "Content-Length,Content-Range", "cf-cache-status": "DYNAMIC", "set-cookie": "__cf_bm=Y8kM8.I6T.DoSR8ULucZUy9HOiHDPdO4zlGaIYtBh4c-1751428560-*******-8ZYwOV98ioczhSSx.t05HqeCtcBj2p6FtPCZDr1B9FVvfojpyoOuzTQPGrkY1e4YrEVxGfL4EMMvjIyb0ONXz75iAZ5vy2uYjJr9AgxYLqg; path=/; expires=Wed, 02-Jul-25 04:26:00 GMT; domain=.sgp.pvp.net; HttpOnly; Secure; SameSite=None", "server": "cloudflare", "cf-ray": "958b35f8daa4f954-DUS"}
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Body: {"token": "eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.JvlOL9UaLutdXVhIiwwHKKyL9n-Dqa7mxDpnx17iwNJMSNbAFkOfyjsPGSTiptffIdzwBPDWhguNIL4OOFEsbbxrN0MWDl8dfinV1o4HOQVJi6Nugv8FPQtSK2Z8bqnX98y93LnpDE0q45sDT2eW_oiyfgC_8P2Gt7iC9m56vaDljyVFTP_qgL45Bh0SAUB3H6m1MQBRTygm-Tpk1-VYIcA6p4Lf-Vi9VanytTKLXK8CgidPcsChotv2Qc1Wsq2b5aFdMavhQyCQyJAG0v_z9sV8kJmSexfjmwXlZZ0vDN91epMP--Avv0fzk7uw-DdnBH_wM16xhZxQUJvBdag_pQ", "type": "LOGIN"}
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[91m-←[0m←[97m] Error parsing userinfo string in get_session_token: Expecting value: line 1 column 1 (char 0)
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Got JWT token from queue_token
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Parsed access token, PUUID: 0e4530dc-e6bd-533d-a26b-ae1fcfe817f8
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - URL: https://euc1-red.pp.sgp.pvp.net/session-external/v1/session/create
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Region: EUW, Region Upper: EUW1
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Player Platform Edge URL: https://euc1-red.pp.sgp.pvp.net
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Headers: {"Authorization": "Bearer eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.JvlOL9UaLutdXVhIiwwHKKyL9n-Dqa7mxDpnx17iwNJMSNbAFkOfyjsPGSTiptffIdzwBPDWhguNIL4OOFEsbbxrN0MWDl8dfinV1o4HOQVJi6Nugv8FPQtSK2Z8bqnX98y93LnpDE0q45sDT2eW_oiyfgC_8P2Gt7iC9m56vaDljyVFTP_qgL45Bh0SAUB3H6m1MQBRTygm-Tpk1-VYIcA6p4Lf-Vi9VanytTKLXK8CgidPcsChotv2Qc1Wsq2b5aFdMavhQyCQyJAG0v_z9sV8kJmSexfjmwXlZZ0vDN91epMP--Avv0fzk7uw-DdnBH_wM16xhZxQUJvBdag_pQ", "Accept": "application/json", "Content-Type": "application/json"}
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Body: {"claims": {"cname": "lcu"}, "product": "lol", "puuid": "0e4530dc-e6bd-533d-a26b-ae1fcfe817f8", "region": "EUW1"}
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Status: 200
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Headers: {"date": "Wed, 02 Jul 2025 03:56:00 GMT", "content-type": "application/json;charset=utf-8", "transfer-encoding": "chunked", "connection": "keep-alive", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "expires": "0", "pragma": "no-cache", "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers, accept-encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-riot-edge-trace-id": "f382ade0-a813-48ea-b426-464111558d10", "x-xss-protection": "0", "content-encoding": "gzip", "access-control-allow-origin": "*", "access-control-allow-methods": "GET, PUT, DELETE, POST, OPTIONS", "access-control-allow-headers": "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range", "access-control-expose-headers": "Content-Length,Content-Range", "cf-cache-status": "DYNAMIC", "server": "cloudflare", "cf-ray": "958b35f92acbf954-DUS"}
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Text: "eyJraWQiOiJzMSIsImFsZyI6IlJTMjU2In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.K1rBHmePain6glPEFROVe-NsKkfpXborZGnUUFDf5JeAhEDvHMumCjH4sHiagCiw4KckYxuDitFJblDmTKFOSyIuuKRJmZ7XGt_h6IUItrqG8DKYuSKiql4AcyUQLb0Z4W0RVFIEeDVuqC4_JZdTHzt5UniqKrUeMb7QzfYhXmXK4Sgqqc8-LdNzupfITmmwruRzqf2O93IVkRMOvwrT9iC2iWL-htD45lUbMQSygQNaz6irHUcZH3HwvZ_dJJrWUi7mZf8a3L5I2063ZPkOjboOWp-u5YNYh3WILqoXtEIuz5kmyigfmDUXD_HFjuL5nMjyxUWzb1Jmoww21lKPSA"
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Got all tokens for spanglebob
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Importing get_owned_champions for spanglebob
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[91m-←[0m←[97m] Error decoding JWT payload: 'utf-8' codec can't decode bytes in position 1248-1249: invalid continuation byte
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Using account region for champion data retrieval: None for spanglebob
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Calling get_owned_champions for spanglebob with region None
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Getting owned champions for region: EUW
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Headers set up: Authorization and Entitlements tokens configured
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] TCP Connector created with IPv4 family and SSL disabled
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Trying URL pattern 1/4
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Using base URL: https://euw-red.lol.sgp.pvp.net
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Inventory URL: https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v2/inventoriesWithLoyalty
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Wallet URL: https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v1/walletsbalances
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Location format: lolriot.aws-euc1-prod.euw
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Inventory params: {'puuid': '', 'accountId': '', 'inventoryTypes': 'CHAMPION', 'location': 'lolriot.aws-euc1-prod.euw'}
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Attempting to resolve hostname: euw-red.lol.sgp.pvp.net
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Successfully resolved euw-red.lol.sgp.pvp.net to *************
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Successfully retrieved inventory data with pattern 1
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Found 41 owned champions
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Requesting wallet data from: https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v1/walletsbalances
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Wallet params: {'puuid': '', 'location': 'lolriot.aws-euc1-prod.euw', 'accountId': '*********', 'currencyTypes': ['RP', 'lol_blue_essence', 'tft_star_fragments']}
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Successfully retrieved wallet data with pattern 1
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Successfully retrieved all data with pattern 1
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[90m@←[0m←[97m] Session closed properly
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Returned from get_owned_champions for spanglebob
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Saving comprehensive account info for spanglebob: BE=5705, RP=0, Level=0
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Saving 41 champions for spanglebob
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Getting skin data for spanglebob
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[91m-←[0m←[97m] Error decoding JWT payload: 'utf-8' codec can't decode bytes in position 1248-1249: invalid continuation byte
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Using account region for skin data retrieval: None for spanglebob
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Getting owned skins for account
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Restriction QUEUE_LOCKOUT for spanglebob is temporary, expires at *************
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[93m!←[0m←[97m] Restriction QUEUE_LOCKOUT for spanglebob is a game restriction
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[93m!←[0m←[97m] Found ban info: QUEUE_LOCKOUT - QUEUE_DODGE
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[93m!←[0m←[97m] Account spanglebob is banned: QUEUE_LOCKOUT - QUEUE_DODGE
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[91m-←[0m←[97m] Error decoding JWT payload: 'utf-8' codec can't decode bytes in position 1248-1249: invalid continuation byte
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[91m-←[0m←[97m] Could not find account for puuid
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[93m!←[0m←[97m] No skins to save for spanglebob
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Got skin data for spanglebob
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Checking penalties for spanglebob
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Checking penalties for None using URL: https://euw-red.lol.sgp.pvp.net/leaverbuster-ledge/restrictionInfo
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Penalty check response: {'puuid': '0e4530dc-e6bd-533d-a26b-ae1fcfe817f8', 'rankedRestrictionEntryDto': {'puuid': '0e4530dc-e6bd-533d-a26b-ae1fcfe817f8', 'version': 7, 'restrictedGamesRemaining': 0, 'restrictedGamesOriginal': 0, 'rankedRestrictionPenaltyId': '309002a1-3017-11ef-ac90-37302662bd49', 'punishmentIncurredGameId': **********, 'punishmentIncurredTimeMillis': *************, 'rankedRestrictionAckNeeded': False, 'penaltyOrigin': 'PDS'}, 'leaverBusterEntryDto': {'puuid': '0e4530dc-e6bd-533d-a26b-ae1fcfe817f8', 'version': 88, 'tainted': True, 'preLockoutAckNeeded': False, 'onLockoutAckNeeded': False, 'leaverScore': 380, 'leaverLevel': 0, 'punishedGamesRemaining': 0, 'currentPunishmentStep': 0, 'leaverPenalty': {'puuid': '0e4530dc-e6bd-533d-a26b-ae1fcfe817f8', 'hasActivePenalty': False, 'delayTime': 0, 'queueLockoutTimerExpiryUtcMillis': 0, 'punishmentTimerType': 'NO_PENALTY', 'rankRestrictedGamesRemaining': 0, 'rankRestrictedTimerExpiryUtcMillis': 0, 'rankRestricted': False}, 'warnSentMillis': 1480470082429, 'warnAckedMillis': 1502533621262, 'lastUpdatedMillis': 1748903308373, 'totalPunishedGamesPlayed': 37, 'lastBustedGameId': 7420410111, 'lastBustedTimeMillis': 1748894191565, 'lastPunishmentIncurredGameId': 7420410111, 'lastPunishmentIncurredTimeMillis': 1748894191565, 'processedGameIdHistoryString': '7414457377,7414478966,7414496771,7414520627,7420100846,7420129683,7420171877,7420205691,7420238414,7420284292,7420410111,7420430755,7420471057,7420524493,7420570609'}}
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] No active penalty
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Got penalty info for spanglebob: 0 minutes, 0 games
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Checking rank for spanglebob#
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[91m-←[0m←[97m] Error decoding JWT payload: 'utf-8' codec can't decode bytes in position 1248-1249: invalid continuation byte
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Using region code EUW for rank info retrieval
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[90m@←[0m←[97m] get_rank_info called with region_code: EUW, type: <class 'str'>
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[90m@←[0m←[97m] Getting rank info for spanglebob# in region EUW
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[91m-←[0m←[97m] Error decoding JWT: module 'jwt' has no attribute 'decode'
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Found country from userinfo: unknown
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Current rank data response: {'queues': [{'queueType': 'RANKED_SOLO_5x5', 'provisionalGameThreshold': 5, 'tier': 'SILVER', 'rank': 'II', 'leaguePoints': -17, 'cumulativeLp': 983, 'wins': 28, 'losses': 7, 'provisionalGamesRemaining': 0, 'highestTier': 'SILVER', 'highestRank': 'II', 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_FLEX_SR', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_TFT', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_TFT_TURBO', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_TFT_DOUBLE_UP', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}], 'earnedRegaliaRewardIds': [], 'splitsProgress': {}, 'seasons': {'RANKED_TFT_TURBO': {'currentSeasonId': 29, 'currentSeasonEnd': 1753848001000, 'nextSeasonStart': 0}, 'RANKED_TFT': {'currentSeasonId': 29, 'currentSeasonEnd': 1753848001000, 'nextSeasonStart': 0}, 'RANKED_FLEX_SR': {'currentSeasonId': 19, 'currentSeasonEnd': 1756249199000, 'nextSeasonStart': 0}, 'RANKED_TFT_DOUBLE_UP': {'currentSeasonId': 29, 'currentSeasonEnd': 1753848001000, 'nextSeasonStart': 0}, 'RANKED_SOLO_5x5': {'currentSeasonId': 19, 'currentSeasonEnd': 1756249199000, 'nextSeasonStart': 0}}, 'jwt': '*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'}
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Processed rank info: {'solo': {'tier': 'SILVER', 'division': 'II', 'lp': -17, 'wins': 28, 'losses': 7, 'previous_tier': 'UNRANKED', 'previous_division': ''}, 'flex': {'tier': 'UNRANKED', 'division': '', 'lp': 0, 'wins': 0, 'losses': 0, 'previous_tier': 'UNRANKED', 'previous_division': ''}, 'country': 'unknown', 'creation_region': 'EUW'}
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Got rank info for spanglebob
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Saving rank info for spanglebob: Solo=SILVER II, Flex=UNRANKED
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Successfully rechecked account: spanglebob
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Loading account spanglebob from database
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Account details: BE=5705, RP=0, Level=0
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Account info: created_at=None, creation_region=None, country=
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Game info: game_name=spanglebob, tag_line=
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Checking JWT token for region information for spanglebob
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] JWT region debugging for spanglebob:
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m]   lol field: [{'cuid': *********, 'cpid': 'EUW1', 'uid': *********, 'uname': 'spanglebob', 'ptrid': None, 'pid': 'EUW1', 'state': 'ENABLED'}]
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m]   region field: {}
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m]   original_platform_id:
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m]   lol_region: [{'active': True, 'cpid': 'EUW1', 'cuid': *********, 'lp': False, 'pid': 'EUW1', 'uid': *********}]
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m]   dat field: {}
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Using lol_region[].cpid to determine region for spanglebob: EUW
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Found region in JWT for spanglebob: EUW
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Loaded 0 season history entries for spanglebob
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Using region=EUW, region_code=EUW for account spanglebob
←[0m←[97m[2025-07-02 05:55:59] [←[0m←[92m$←[0m←[97m] Loaded 1 accounts from database
←[0mDEBUG: PyQtBridge.handle_account_selection called with index: 0
DEBUG: Updated PyQtBridge.selected_account_index to: 0
DEBUG: Updated main_window.selected_account_index via set_selected_account_index
