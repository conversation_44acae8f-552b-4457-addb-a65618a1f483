
C:\Users\<USER>\Desktop\sources\League Acc Checker>py main.py
←[97m[2025-07-02 05:53:31] [←[0m←[92m$←[0m←[97m] Created DatabaseManagerWrapper with path accounts.db
←[0m←[97m[2025-07-02 05:53:31] [←[0m←[92m$←[0m←[97m] Wrapped db_manager with DatabaseManagerWrapper in AccountPool
←[0m←[97m[2025-07-02 05:53:31] [←[0m←[92m$←[0m←[97m] Loaded 1941 skin entries from JSON database
←[0mDEBUG: Created CustomWebEngineView
DEBUG: Created web view with custom context menu handling
Loaded 1941 skin entries from JSON database
DEBUG: Sharing champion database with 171 entries to PyQtBridge and AccountPool
←[97m[2025-07-02 05:53:32] [←[0m←[92m$←[0m←[97m] Loading account pascuine123 from database
←[0m←[97m[2025-07-02 05:53:32] [←[0m←[92m$←[0m←[97m] Account details: BE=8804, RP=0, Level=215
←[0m←[97m[2025-07-02 05:53:32] [←[0m←[92m$←[0m←[97m] Account info: created_at=2019-04-06 18:22:35, creation_region=EUW, country=egy
←[0m←[97m[2025-07-02 05:53:32] [←[0m←[92m$←[0m←[97m] Game info: game_name=Pascuine, tag_line=EUW
←[0m←[97m[2025-07-02 05:53:32] [←[0m←[92m$←[0m←[97m] Checking JWT token for region information for pascuine123
←[0m←[97m[2025-07-02 05:53:32] [←[0m←[92m$←[0m←[97m] JWT region debugging for pascuine123:
←[0m←[97m[2025-07-02 05:53:32] [←[0m←[92m$←[0m←[97m]   lol field: [{'cuid': ****************, 'cpid': 'EUW1', 'uid': ****************, 'uname': 'pascuine123', 'ptrid': None, 'pid': 'EUW1', 'state': 'ENABLED'}]
←[0m←[97m[2025-07-02 05:53:32] [←[0m←[92m$←[0m←[97m]   region field: {}
←[0m←[97m[2025-07-02 05:53:32] [←[0m←[92m$←[0m←[97m]   original_platform_id:
←[0m←[97m[2025-07-02 05:53:32] [←[0m←[92m$←[0m←[97m]   lol_region: [{'active': True, 'cpid': 'EUW1', 'cuid': ****************, 'lp': False, 'pid': 'EUW1', 'uid': ****************}]
←[0m←[97m[2025-07-02 05:53:32] [←[0m←[92m$←[0m←[97m]   dat field: {}
←[0m←[97m[2025-07-02 05:53:32] [←[0m←[92m$←[0m←[97m] Using lol_region[].cpid to determine region for pascuine123: EUW
←[0m←[97m[2025-07-02 05:53:32] [←[0m←[92m$←[0m←[97m] Found region in JWT for pascuine123: EUW
←[0m←[97m[2025-07-02 05:53:32] [←[0m←[92m$←[0m←[97m] Loaded 6 season history entries for pascuine123
←[0m←[97m[2025-07-02 05:53:32] [←[0m←[92m$←[0m←[97m] Using region=EUW, region_code=EUW for account pascuine123
←[0m←[97m[2025-07-02 05:53:32] [←[0m←[92m$←[0m←[97m] Loaded 1 accounts from database
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Starting to recheck account: pascuine123
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Checking JWT token for region during recheck for pascuine123
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Using lol_region[].cpid from JWT to determine region: EUW
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Updated region to EUW based on JWT token for account pascuine123
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Initializing authenticator for account: pascuine123
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Extracted JWT payload for pascuine123, keys: ['sub', 'country', 'player_plocale', 'country_at', 'amr', 'iss', 'lol', 'phone_number_verified', 'locale', 'nonce', 'account_verified', 'aud', 'acr', 'lol_region', 'player_locale', 'exp', 'iat', 'acct', 'age', 'jti', 'login_country']
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] JWT region debugging for pascuine123:
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m]   lol field: [{'cuid': ****************, 'cpid': 'EUW1', 'uid': ****************, 'uname': 'pascuine123', 'ptrid': None, 'pid': 'EUW1', 'state': 'ENABLED'}]
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m]   region field: {}
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m]   original_platform_id:
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m]   lol_region: [{'active': True, 'cpid': 'EUW1', 'cuid': ****************, 'lp': False, 'pid': 'EUW1', 'uid': ****************}]
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m]   dat field: {}
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Using lol_region[].cpid to determine region for pascuine123: EUW
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Found region in JWT for pascuine123: EUW
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Initializing authenticator for pascuine123 with region EUW
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Authenticator initialized for pascuine123 in region EUW
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Set existing tokens from account object for account: pascuine123
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Using refresh token for pascuine123: yes
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Authenticator initialized for pascuine123 in region EUW
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Set existing tokens from account object for account: pascuine123
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Using refresh token for pascuine123: yes
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Rechecking account: pascuine123
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Starting check for account pascuine123 with region EUW
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Getting userinfo for pascuine123
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Using access token for userinfo: eyJraWQiOi...ahb9a1iI6w
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Access token length: 1233
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Userinfo request URL: https://auth.riotgames.com/userinfo
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Userinfo request headers: Authorization: Bearer eyJraWQiOi...ahb9a1iI6w, Accept: application/json, Content-Type: application/json
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Raw userinfo response (first 100 chars): eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwidHlwIjoidXNlcmluZm8rand0IiwiYWxnIjoiUlMyNTYifQ.eyJjb3VudHJ5IjoiZ
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Userinfo response length: 1942
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Received JWT token for userinfo
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] JWT token has 3 parts
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Successfully decoded JWT payload with 25 keys
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[93m!←[0m←[97m] Ban info in JWT payload: {
  "restrictions": []
}
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[93m!←[0m←[97m] Found 0 ban restrictions
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Successfully decoded JWT payload with 25 keys
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Got userinfo for pascuine123, decoding JWT
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Getting tokens for pascuine123
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[91m-←[0m←[97m] Error parsing userinfo string in get_queue_token: Expecting value: line 1 column 1 (char 0)
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - URL: https://euc1-red.pp.sgp.pvp.net/login-queue/v2/login/products/lol/regions/EUW1
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Region: EUW, Region Upper: EUW1
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Player Platform Edge URL: https://euc1-red.pp.sgp.pvp.net
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Headers: {"Authorization": "Bearer eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwiYWxnIjoiUlMyNTYifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QNfZ4Arkm74RvqLayddh5kPgFymZviQZVP9UdN35dqac1D05AuM02SJH0yRsqUvgT04X5x454qLVdUNRFMHpV740zawz0wAAbyRiAAORjFoWIRBSksvCwKEyDusIRc6Z3A5mASRbK0OzaEWp2W1bPNLJVDvKU7aardQ-NMa_DhxA-Fu5Xjyq9CXZ7rGp5jU1I_skWISLFs3KhRY_95o9DuaIUwgDf_IDDAoVjvMU5QudJlL08DqinK9Cypvk0-6aco8EI7rC75lIpygr0QwfaOb9HBZCaCyiXTrUr8ZoSrJpunmzZt3APEBcc7XymZ2GClm2_vmavyTIahb9a1iI6w", "Accept": "application/json", "Content-Type": "application/json"}
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Body: {"clientName": "lcu", "entitlements": "eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.UzpOeTiq-C7UIHbEaeua6MuZI254NiREvDii3A2hgR0a-9RVFNeQIwizNpVJ4C_uxv_uBMRi4ii9ey8R7wjJPkNnixCEmlWWyKoJ-98wVBl_6YKlg36z883YOKhpdyuVLT3nigk8IGpCnemgVmRsmckomTuN5mzANfjUIv0E8oYI_4x55ON2VpdjH6g1cckZcNrwXlKbBYV2cqGdqdB5h0vU6IjtMUQ4qjSDAkhbcw_KWzKsjpsWOHHeVV-b8k6lyRV5wNWLdHiyRlqcwLLgJXVSHXVS73n7VD11qo2TXMk6NqRpmXZdS78VCh1yY35LAFSkukT3IXmGKRA169mNGA", "userinfo": "eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwidHlwIjoidXNlcmluZm8rand0IiwiYWxnIjoiUlMyNTYifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.sDHdmOpYpvRIalKiClcuUl6Wybpr_tOehhcBwIDmC3tcWY-8-zYEF_TR9cWAjluNzXD1rrwNZeaqc2ezY6XlztKR6whXfCdgqaLQRZCUVDpa0ZHR6PQIDrssHgWa0uMcA6CingIos1cf0XidaeiL0VyRYBTww4imisK9kMGpk7cmhjSD6Eh7nXoYjB4kj1FBKjVdel43RERWlFl7OCSxebchIc7OVUFJA_FjUUFr6bZMgVT8U85dfJpx4AQUMJsEp8u83Qq8pCZxbtDmStBUpBQ_erKS8XJAt_EAK0Ort_-YPkRhMUXiniiD1ofrh0aYTlh2zbOU7FKmcmAChbh2LA"}
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Status: 200
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Headers: {"date": "Wed, 02 Jul 2025 03:53:35 GMT", "content-type": "application/json;charset=utf-8", "transfer-encoding": "chunked", "connection": "keep-alive", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "expires": "0", "pragma": "no-cache", "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers, accept-encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-riot-edge-trace-id": "a055cb37-9d1f-412a-bf7d-e2fe51973357", "x-xss-protection": "0", "content-encoding": "gzip", "access-control-allow-origin": "*", "access-control-allow-methods": "GET, PUT, DELETE, POST, OPTIONS", "access-control-allow-headers": "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range", "access-control-expose-headers": "Content-Length,Content-Range", "cf-cache-status": "DYNAMIC", "set-cookie": "__cf_bm=.o9yR9R.7rNAk3j0lw5QFpKesFnOxarn4Uo33tNV5d0-1751428415-*******-jH90lDjxD.rby9VvILo6B4cuX6Zzpk.UXviRJjXHfPZaY31ccr3u2rasMQqWGm60UTKMPkJmsxFHGLetV6Vw0E5uQnHOmwmZD7vSXKFIj08; path=/; expires=Wed, 02-Jul-25 04:23:35 GMT; domain=.sgp.pvp.net; HttpOnly; Secure; SameSite=None", "server": "cloudflare", "cf-ray": "958b326f8e4d7b85-DUS"}
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Body: {"token": "eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.WgKLHfr4FgIMLPbPkWgZj6dcAQuZJQYg5D6yS7B8Gpq5SH7L3B-yhieOdzYkG2tf5QON8HNzY9jvXWTtK6T3AGzHAZfTfkI-pj1y_DHaliFAiFo_fy_tzt3p71fcBrcz2jbA4jpU3i_HlxigPRj6Ut1q2UZ_y-BexVF2X7StW1lX5bZYJrhGQbn1sJ34vVASyK_NfR9b4cNh7T0Zn8KZ4oI32WSyYIVvUR4pmNnN-qMcWR7OPhyLF23_r5svtFLWFlBLgTK8JVKEsStrQ8m0p6NgylIBWB0Z15-4D0iNaFcQ2rTazsHXSag5aluvQx8x0FEsQfKkkABX7AeCN70mMg", "type": "LOGIN"}
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[91m-←[0m←[97m] Error parsing userinfo string in get_session_token: Expecting value: line 1 column 1 (char 0)
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Got JWT token from queue_token
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Parsed access token, PUUID: 894b494f-38d0-5c68-99e0-5b7c3f2e1fb2
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - URL: https://euc1-red.pp.sgp.pvp.net/session-external/v1/session/create
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Region: EUW, Region Upper: EUW1
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Player Platform Edge URL: https://euc1-red.pp.sgp.pvp.net
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Headers: {"Authorization": "Bearer eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.WgKLHfr4FgIMLPbPkWgZj6dcAQuZJQYg5D6yS7B8Gpq5SH7L3B-yhieOdzYkG2tf5QON8HNzY9jvXWTtK6T3AGzHAZfTfkI-pj1y_DHaliFAiFo_fy_tzt3p71fcBrcz2jbA4jpU3i_HlxigPRj6Ut1q2UZ_y-BexVF2X7StW1lX5bZYJrhGQbn1sJ34vVASyK_NfR9b4cNh7T0Zn8KZ4oI32WSyYIVvUR4pmNnN-qMcWR7OPhyLF23_r5svtFLWFlBLgTK8JVKEsStrQ8m0p6NgylIBWB0Z15-4D0iNaFcQ2rTazsHXSag5aluvQx8x0FEsQfKkkABX7AeCN70mMg", "Accept": "application/json", "Content-Type": "application/json"}
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Body: {"claims": {"cname": "lcu"}, "product": "lol", "puuid": "894b494f-38d0-5c68-99e0-5b7c3f2e1fb2", "region": "EUW1"}
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Status: 200
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Headers: {"date": "Wed, 02 Jul 2025 03:53:36 GMT", "content-type": "application/json;charset=utf-8", "transfer-encoding": "chunked", "connection": "keep-alive", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "expires": "0", "pragma": "no-cache", "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers, accept-encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-riot-edge-trace-id": "21dc422d-fb45-4973-b367-099708daaa5b", "x-xss-protection": "0", "content-encoding": "gzip", "access-control-allow-origin": "*", "access-control-allow-methods": "GET, PUT, DELETE, POST, OPTIONS", "access-control-allow-headers": "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range", "access-control-expose-headers": "Content-Length,Content-Range", "cf-cache-status": "DYNAMIC", "server": "cloudflare", "cf-ray": "958b326fde6e7b85-DUS"}
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Text: "eyJraWQiOiJzMSIsImFsZyI6IlJTMjU2In0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.KzStpv-JltpKnkJDmBi2gCVu5MHllUSN_9EejdGFJ2bkBjO5ekmwST2_cvXiL2J9wuVO2VsIKBnztpN-U3Dco7FkurpYnHD8rAiEs_BeWZ6NMcvVuFAAWNf4QQp7kmLpPBOOECENz1yTY7fqK7P3NR-83CRSSp5DJFxB8t5naYgZIe8KTujIo4FtcXF_Nu9Wmsi2bVDYTBjs6Kq1kdmIg3RFoXyoPSG27_B2j-yR8oKEEAQIj-pjwM43Aqg7OEBcN6vMeaov-6JOq5DUxyPIGevMRiMnltQOa0Fiybs4K397l271UJwoEz9MzqhaP3DvkTGkZZM0tZqMi152io8Dkg"
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Got all tokens for pascuine123
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Importing get_owned_champions for pascuine123
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Using existing region code for champion data retrieval: EUW for pascuine123
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Calling get_owned_champions for pascuine123 with region EUW
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Getting owned champions for region: EUW
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Headers set up: Authorization and Entitlements tokens configured
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] TCP Connector created with IPv4 family and SSL disabled
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Trying URL pattern 1/4
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Using base URL: https://euw-red.lol.sgp.pvp.net
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Inventory URL: https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v2/inventoriesWithLoyalty
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Wallet URL: https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v1/walletsbalances
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Location format: lolriot.aws-euc1-prod.euw
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Inventory params: {'puuid': '894b494f-38d0-5c68-99e0-5b7c3f2e1fb2', 'accountId': '****************', 'inventoryTypes': 'CHAMPION', 'location': 'lolriot.aws-euc1-prod.euw'}
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Attempting to resolve hostname: euw-red.lol.sgp.pvp.net
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Successfully resolved euw-red.lol.sgp.pvp.net to *************
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Successfully retrieved inventory data with pattern 1
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Found 134 owned champions
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Requesting wallet data from: https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v1/walletsbalances
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Wallet params: {'puuid': '894b494f-38d0-5c68-99e0-5b7c3f2e1fb2', 'location': 'lolriot.aws-euc1-prod.euw', 'accountId': '****************', 'currencyTypes': ['RP', 'lol_blue_essence', 'tft_star_fragments']}
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Successfully retrieved wallet data with pattern 1
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Successfully retrieved all data with pattern 1
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[90m@←[0m←[97m] Session closed properly
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Returned from get_owned_champions for pascuine123
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Extracted creation date for pascuine123: 2019-04-06 18:22:35 from timestamp *************
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Extracted creation region for pascuine123: EUW from tag euw
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Saving comprehensive account info for pascuine123: BE=8804, RP=0, Level=215
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Saving 134 champions for pascuine123
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Getting skin data for pascuine123
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Using existing region code for skin data retrieval: EUW for pascuine123
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Getting owned skins for account 894b494f-38d0-5c68-99e0-5b7c3f2e1fb2
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Found account for puuid 894b494f-38d0-5c68-99e0-5b7c3f2e1fb2
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] Extracted account ID **************** from userinfo token
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[91m-←[0m←[97m] Error parsing userinfo string in get_queue_token: Expecting value: line 1 column 1 (char 0)
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - URL: https://euc1-red.pp.sgp.pvp.net/login-queue/v2/login/products/lol/regions/EUW1
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Region: EUW, Region Upper: EUW1
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Player Platform Edge URL: https://euc1-red.pp.sgp.pvp.net
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Headers: {"Authorization": "Bearer eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwiYWxnIjoiUlMyNTYifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QNfZ4Arkm74RvqLayddh5kPgFymZviQZVP9UdN35dqac1D05AuM02SJH0yRsqUvgT04X5x454qLVdUNRFMHpV740zawz0wAAbyRiAAORjFoWIRBSksvCwKEyDusIRc6Z3A5mASRbK0OzaEWp2W1bPNLJVDvKU7aardQ-NMa_DhxA-Fu5Xjyq9CXZ7rGp5jU1I_skWISLFs3KhRY_95o9DuaIUwgDf_IDDAoVjvMU5QudJlL08DqinK9Cypvk0-6aco8EI7rC75lIpygr0QwfaOb9HBZCaCyiXTrUr8ZoSrJpunmzZt3APEBcc7XymZ2GClm2_vmavyTIahb9a1iI6w", "Accept": "application/json", "Content-Type": "application/json"}
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Body: {"clientName": "lcu", "entitlements": "eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EkfolTJzeavWIdydStXUi4OvIk8MJWAzXdbQJxm4zJa1co6b8oyyAbgDv_gGg5MuEp58CdRQ4bNKLRehoai1AelG92wPTdzETQIiamPFWuStZX-qovYkfm3tM-qJ6X9EcyMgYciBIaLR-CfjrhmROJakBnOn-VM0FEuoPmtkGhBKe9LtMS65iworvPgQc3ROLgZaR8PI_10gMc0919JHEz_WYA7GvNzXO4KqI-GJNM_1NL2u1cBH-jkG_NtRGPfu_xa6nMq52ASa-vZwrrEp_lULTOQopqVL-unODo86cXMF4rKin2RwZ-DZPNrsEAYls0gAo_bb9LP2_OVkztxnsQ", "userinfo": "eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwidHlwIjoidXNlcmluZm8rand0IiwiYWxnIjoiUlMyNTYifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.sDHdmOpYpvRIalKiClcuUl6Wybpr_tOehhcBwIDmC3tcWY-8-zYEF_TR9cWAjluNzXD1rrwNZeaqc2ezY6XlztKR6whXfCdgqaLQRZCUVDpa0ZHR6PQIDrssHgWa0uMcA6CingIos1cf0XidaeiL0VyRYBTww4imisK9kMGpk7cmhjSD6Eh7nXoYjB4kj1FBKjVdel43RERWlFl7OCSxebchIc7OVUFJA_FjUUFr6bZMgVT8U85dfJpx4AQUMJsEp8u83Qq8pCZxbtDmStBUpBQ_erKS8XJAt_EAK0Ort_-YPkRhMUXiniiD1ofrh0aYTlh2zbOU7FKmcmAChbh2LA"}
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Status: 200
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Headers: {"date": "Wed, 02 Jul 2025 03:53:36 GMT", "content-type": "application/json;charset=utf-8", "transfer-encoding": "chunked", "connection": "keep-alive", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "expires": "0", "pragma": "no-cache", "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers, accept-encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-riot-edge-trace-id": "ef513a50-8cad-419b-9be9-4b56d7a54b71", "x-xss-protection": "0", "content-encoding": "gzip", "access-control-allow-origin": "*", "access-control-allow-methods": "GET, PUT, DELETE, POST, OPTIONS", "access-control-allow-headers": "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range", "access-control-expose-headers": "Content-Length,Content-Range", "cf-cache-status": "DYNAMIC", "server": "cloudflare", "cf-ray": "958b3271df157b85-DUS"}
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Body: {"token": "eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HWzAxhFNVu1zST2VGnybzoRqd6t00Z8B-LCPZqJd6mR_ZkuYmHMrQtAIN0Or1RDzemD4FRoAZlyG2hICrgojvZBz50CHz7irma2TLfm-LQkaX26cOyK44YMSSqy8abmZdx_NBwDfyhhA6x8nm45-4ILJ-gDZPaaR4fjh2ytsDmCX2HTpKXIbx1mCXyjiy_qA-Ue6TGJzDn4NySC_H-daPAMDRtut3nLyInOr8khPuJFgqlQtPqhr94EbEHl_iQWTOaydUb3J0s20b7CjEpLqC5w05HNtBZpBr-lWZzXL1lAVqtTKBoynzxD2C1gNQX-AnuBHC4iBYbykwbg_WtYHjA", "type": "LOGIN"}
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[91m-←[0m←[97m] Error parsing userinfo string in get_session_token: Expecting value: line 1 column 1 (char 0)
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Got JWT token from queue_token
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Parsed access token, PUUID: 894b494f-38d0-5c68-99e0-5b7c3f2e1fb2
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - URL: https://euc1-red.pp.sgp.pvp.net/session-external/v1/session/create
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Region: EUW, Region Upper: EUW1
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Player Platform Edge URL: https://euc1-red.pp.sgp.pvp.net
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Headers: {"Authorization": "Bearer eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HWzAxhFNVu1zST2VGnybzoRqd6t00Z8B-LCPZqJd6mR_ZkuYmHMrQtAIN0Or1RDzemD4FRoAZlyG2hICrgojvZBz50CHz7irma2TLfm-LQkaX26cOyK44YMSSqy8abmZdx_NBwDfyhhA6x8nm45-4ILJ-gDZPaaR4fjh2ytsDmCX2HTpKXIbx1mCXyjiy_qA-Ue6TGJzDn4NySC_H-daPAMDRtut3nLyInOr8khPuJFgqlQtPqhr94EbEHl_iQWTOaydUb3J0s20b7CjEpLqC5w05HNtBZpBr-lWZzXL1lAVqtTKBoynzxD2C1gNQX-AnuBHC4iBYbykwbg_WtYHjA", "Accept": "application/json", "Content-Type": "application/json"}
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Body: {"claims": {"cname": "lcu"}, "product": "lol", "puuid": "894b494f-38d0-5c68-99e0-5b7c3f2e1fb2", "region": "EUW1"}
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Status: 200
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Headers: {"date": "Wed, 02 Jul 2025 03:53:36 GMT", "content-type": "application/json;charset=utf-8", "transfer-encoding": "chunked", "connection": "keep-alive", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "expires": "0", "pragma": "no-cache", "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers, accept-encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-riot-edge-trace-id": "5ab99e17-7e59-40d9-9017-f38f66f0b17d", "x-xss-protection": "0", "content-encoding": "gzip", "access-control-allow-origin": "*", "access-control-allow-methods": "GET, PUT, DELETE, POST, OPTIONS", "access-control-allow-headers": "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range", "access-control-expose-headers": "Content-Length,Content-Range", "cf-cache-status": "DYNAMIC", "server": "cloudflare", "cf-ray": "958b32722f357b85-DUS"}
←[0m←[97m[2025-07-02 05:53:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Text: "eyJraWQiOiJzMSIsImFsZyI6IlJTMjU2In0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.C2SmvcwjP9A1tza3zS0Z-RTRbhMQIORhNiroDAUxDz29EO3VEYWG_l0NKyVpjHhCzYZdOJ8Pot0eMrED_PFK6ZP8wAFib-kwD5XpKsO6rw3IBW2tEv4t5Kc47YYOUU5qex7aWuVXn3BzE_Pt3wlZDcbmF0oQV52VNpF9ZDVnX1iknODz1nQ5wz870LPJcPn9MkwOLZptX9Im-CdRtd63hhsm3WB96DK4eqwYhdMZwVLV6eA9BQWWyutt0n_kvkUGGw0I4IsCwVaMw2XKCAmKwYfIhO-lPM6z_NalXSIhNxwZOsl53RFau2yoN9BDGg1cJSg9d6JXCL6XH0ij0nzP_A"
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Successfully retrieved all tokens for skin retrieval
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Making request to https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v2/inventoriesWithLoyalty for skin data
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Successfully retrieved skin data response
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Found items in skin data response: {'CHAMPION_SKIN': [{'itemId': 60034, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250308T064821.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'bd0f5e4c-3c53-474b-8235-22d286629656', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c0740a3b-5bf2-43b4-ac50-f79542d707bc', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 98051, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250420T015605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '81557dd9-797c-4899-b310-b80c680adc31', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '47c09d19-79f1-4ee0-b796-473c1a8307d5', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 427011, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210223T205126.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30623335-3833-6436-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'cbdbada4-753e-4420-8f16-a15a8166e9fc', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 48006, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240621T184635.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '66441e41-2ed6-4db0-9764-796cd78601b6', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '9a3f0140-b61f-4f16-a70b-2705d2ebbc63', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 166020, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250420T015605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '4e60e76d-2e90-4b39-9dc0-1f1cef6014c2', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c309178c-f9e3-46c1-b073-4d5175dd62c5', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 133002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20201009T221859.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20201010T005800.000Z', 'entitlementId': '30623335-3835-3531-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'e225245a-aecf-46a2-ae57-fa2a9fc0f855', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 133003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210611T135336.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210630T115928.000Z', 'entitlementId': '30623335-3835-3866-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'e7a0cf29-4fa7-4e97-a146-a0470497c1a6', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 245002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20230131T175508.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '22366e20-1f6c-4fbd-96a4-588ff22994ce', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '05c094de-d840-469e-bc33-b6dc933809ea', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 106003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20191225T115229.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200107T185108.000Z', 'entitlementId': '30623335-3832-6237-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b5c684ce-d35f-4a82-89af-b203cd65287c', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 202001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240525T201834.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20250108T160303.000Z', 'entitlementId': '70aa186c-bbd3-41ac-ae95-ba66ef4cbba0', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '585dd20e-6cdd-4311-becb-2c0cd63003b0', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 106006, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200528T221445.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200528T231401.000Z', 'entitlementId': '30623335-3764-3632-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '1f5fc68d-f12b-4b40-a8ad-3d484fd66501', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 58009, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20191225T115248.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200502T144704.000Z', 'entitlementId': '30623335-3765-3535-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '3629369b-d668-4275-90b9-338233256180', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 10009, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220502T190806.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220502T194002.000Z', 'entitlementId': '30623335-3765-3939-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '37a3b9f9-7b44-47d2-9d03-a913b1006205', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 79007, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200403T064950.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200624T132108.000Z', 'entitlementId': '30623335-3834-3130-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'cc284108-5c6d-4456-a580-67793b80653d', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220710T021236.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30623335-3831-3936-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '85bf1b1a-1378-4385-8224-6b9480cbca89', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 164001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240525T205431.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'fa041e2a-7fb5-4760-8de3-86b1c2821008', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '43c279dd-49eb-4216-a822-fb7924fd8bf0', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 20004, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200314T154145.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200314T161310.000Z', 'entitlementId': '30623335-3764-6132-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '29a23f23-849b-4747-bdcc-4aeb3ae98968', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 91049, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240505T140338.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '9b21baa6-a154-43e9-9753-9278935a0071', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '9232d238-fbbe-462c-a1b4-d5391844f19b', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 36010, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20190711T122404.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20190711T145956.000Z', 'entitlementId': '30623335-3832-3061-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '8bde95da-317b-4d5f-9ef5-f2c5d65d3584', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 25003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20190822T000634.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20190822T221349.000Z', 'entitlementId': '30623335-3766-3531-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '4369ee2c-3c0e-4999-b3b3-7c7e9238e50f', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4013, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20230126T004803.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'b34d7492-3b79-442c-92b4-3e71d7b8ad4a', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '943ce6b7-4e61-404a-9976-2ff98cb89df9', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 9009, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200701T210434.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200701T212946.000Z', 'entitlementId': '30623335-3763-6530-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '17aff872-5d68-49b0-9c05-ef26d61be8ea', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 254002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200717T022030.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210423T174427.000Z', 'entitlementId': '30623335-3762-3562-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '079b3431-39b0-421d-9624-448f9bfdd477', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 30002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20190823T155205.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20190823T214756.000Z', 'entitlementId': '30623335-3835-3131-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'e0f54ec3-d9c9-40b5-b5f2-b4c0d0654a5a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 14005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20191023T104218.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20191115T201253.000Z', 'entitlementId': '30623335-3833-3633-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c4234437-95ae-4ad2-8f22-e2998623a86e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 90038, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250420T015605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '72a2225a-48d9-4f76-b5da-f6d7d985816d', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0a45c80e-6c37-4cbd-bf0f-c615cac05c76', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 78006, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20191023T131802.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20191023T170908.000Z', 'entitlementId': '30623335-3762-6266-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '09cc0990-308a-49a0-b7fd-ddd72338cd3f', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 99001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210629T140035.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210927T131249.000Z', 'entitlementId': '30623335-3436-3535-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '028a5545-4273-402c-88b0-432e9692bd9c', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200907T000335.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200907T003750.000Z', 'entitlementId': '30623335-3832-3432-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '996ec966-bfde-4151-8d1b-5b4101219876', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 83003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20191119T190325.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20191119T213355.000Z', 'entitlementId': '30623335-3766-3134-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '4245fd58-9460-45a6-a729-e12630e9280d', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 99004, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20191023T115258.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20191023T174602.000Z', 'entitlementId': '30623335-3830-6162-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '80f1ba40-4fd4-4704-b02d-2662fccc1c66', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 83004, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20191120T161107.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20191120T161519.000Z', 'entitlementId': '30623335-3836-3433-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'f505a0fc-99ee-4da9-a0d9-f87231e24444', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240505T145009.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'a9509099-a1b3-4e4b-962c-3f4b43678244', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '199af8b3-9c5a-4734-b49c-9c442637d940', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 67006, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220502T190930.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20240502T215739.000Z', 'entitlementId': '30623335-3834-3834-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'de1bb55e-3a0b-4631-a893-44d75279bc6c', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19008, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200927T150236.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20201001T035050.000Z', 'entitlementId': '30623335-3830-6538-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '82073838-dcad-4846-bf80-786be6c25342', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 8002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200731T215654.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200731T225515.000Z', 'entitlementId': '30623335-3766-6333-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '559b06c2-543b-421e-903d-f87b10b6e15a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 120003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20191026T131534.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20191026T131658.000Z', 'entitlementId': '30623335-3831-6430-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '88ff45e1-afeb-48d0-9c5e-3a237bedc4fe', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 8005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210331T190905.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210331T203923.000Z', 'entitlementId': '30623335-3831-3562-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '831b8497-03a6-4d94-aee0-f90af5ba6e50', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 8006, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200119T140442.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200430T211540.000Z', 'entitlementId': '30623335-3763-3432-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '141b01a2-6241-437d-9014-aa8b0b3e5efa', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 99015, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220401T143326.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30623335-3766-6664-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '5e1b18c2-8319-4962-bc59-5dfdacd9912d', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 126024, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20211116T130855.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30623335-3764-3233-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '1c0cefc5-5d3e-408c-96ce-0b60806143ec', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19018, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250420T015605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '6623829e-aea8-42c3-b228-99d0d26f537a', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '861dd57b-e87a-4006-84c5-52e3e16e1ffe', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 77003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210223T205145.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210224T115012.000Z', 'entitlementId': '30623335-3834-3439-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'cdb8f37a-ca19-487e-991c-5e8d1451f3ed', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250420T015605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'a2423995-4fb0-481b-a16a-f442b4d1cf35', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'fe896ef1-2a0c-49d4-a1f3-a8c8a9c14176', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4045, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T084949.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'f6a4d57d-7f63-475c-9216-ce14d9130a44', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '789b9546-e20b-4c29-a77e-d0d9d2c0b1a0', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4046, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T084949.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'be0ea4fa-685e-4c7c-8043-adf892597512', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '25121203-8270-48a5-b9fe-ebbe287d17cb', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 254029, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20211116T130804.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20211206T131943.000Z', 'entitlementId': '30623335-3831-3232-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '8278ac48-e411-4d3a-8439-9dbbe5b7f2b2', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4047, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T084949.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'ba2110e5-6c90-48fc-b7ec-1f596d6e4cc7', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '40012ef7-ae2c-4b13-b044-618f6f9ac327', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19023, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250420T015605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'bc9a8fb7-2fb0-4add-af56-33797d4d4618', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '6f363d48-b85d-4bd0-a9fe-d2d58a1d4674', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4048, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T084949.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '144b92d7-b205-4024-9213-2f4daa646458', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0d3c52e0-8113-4152-83d6-a1369eecf260', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4049, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T084949.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '7d9a1383-a534-4b4d-9165-750a7adac0b4', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0e09fbee-7c7f-48d2-b138-71205d9a771d', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19025, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250420T015605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'ada506f4-6ab0-42f1-8ef4-4760d52e222a', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'd8428f19-daeb-45c5-ad39-db2be9d111a1', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 18002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240525T205452.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '27dcb608-38ab-42a4-988c-12454d6ec55f', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '5c78a3cb-d20a-48b1-b170-1b7ffcc0158e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4050, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T084949.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'b52032e4-ea42-46a3-bd8a-6c08069a59d2', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '8c4d0f20-7a94-44a0-96ae-7bd8cb8ae6e7', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 50004, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20190528T121605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20190528T141655.000Z', 'entitlementId': '30623335-3830-3730-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '671d2f4a-4679-4491-ad0b-3cb4f93f4a48', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 777055, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240604T203106.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '160fc071-4dc4-4b33-8c02-fb80264cc7d9', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c0aa8405-7508-4d18-a83d-2f1a3c21bc46', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 51028, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20211206T142405.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30623335-3830-3337-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '5e64db14-3962-43e1-b199-1c4bee0d43c1', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 222037, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20211206T142357.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30623335-3763-3033-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '1115db96-f8ee-4f4f-a331-336fbb1a965f', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240510T184916.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'cf266c68-141f-42d3-bd72-3ca55ee77e88', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'bdeed643-9ac8-4cb6-b3d1-316ba6e053ff', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29021, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250420T015605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '29acd48a-c520-4dfc-8e2f-0c3ff0f31c41', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '2717bff4-051d-4cd7-8280-c15cafb51e26', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29023, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250420T015605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '68f43942-c994-4b0f-8256-dc97bf75f2e6', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '8f0562cb-7c6b-4f99-91f0-db41ccb9221b', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29024, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250420T015605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '0426b8a3-d1ed-4a63-8037-008334d6e9db', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '48e5636f-6d6a-4372-b594-6d73cc56380e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55009, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20190505T220256.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20190506T003028.000Z', 'entitlementId': '30623335-3835-6363-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'e8c8ea2c-5d90-4907-b851-6979cfe91010', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 23010, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200520T183656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200521T111408.000Z', 'entitlementId': '30623335-3836-3837-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'fd1c8b7a-cd52-4cb1-829a-43fee7acd51c', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 7012, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240505T140400.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '5eca1e7a-06fd-4f44-8c8d-dfd32772d390', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '7cf1d28f-9772-40a5-a8bb-d32b7dbb18e4', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 76005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20230131T175558.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '3a239d2a-7656-48ab-8262-23cb6918ae8e', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '3d39d31f-6eb7-4fc5-9cd7-a920c0db6b15', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 114022, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20190927T234542.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20191010T212453.000Z', 'entitlementId': '30623335-3832-6631-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b8ae47e7-2b65-4dfa-876a-4dade2fcf13e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 39016, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210427T172220.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210530T090842.000Z', 'entitlementId': '30623335-3765-3162-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '352d8ad1-e80d-42d9-bd65-d1f139f299a6', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 246002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200216T152939.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200216T214531.000Z', 'entitlementId': '30623335-3833-3964-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c93a5139-73b3-45b9-8bbd-b3abe7d8ac10', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 1009, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20191023T115246.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200704T003556.000Z', 'entitlementId': '30623335-3832-3765-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'a9951de2-e800-4ae6-9a57-7216bae4a664', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 1012, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20191030T214507.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20191116T211206.000Z', 'entitlementId': '30623335-3836-3061-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'e9fedd5d-15e6-4798-b75f-dfa03edfea35', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': True}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 92020, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200915T225132.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200916T175025.000Z', 'entitlementId': '30623335-3764-6464-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '2d60434e-f590-4d76-9bad-36269e0c5f63', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 102006, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200222T190210.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200223T055100.000Z', 'entitlementId': '30623335-3765-6438-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '3daa6a01-4082-4800-8c32-139ca0f02579', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 91005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200426T141516.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200426T143945.000Z', 'entitlementId': '30623335-3834-6266-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'df6e9ac8-e902-4996-a3ae-caca5844d610', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 86013, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200112T193644.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200113T201120.000Z', 'entitlementId': '30623335-3561-3564-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '029573f2-b5eb-4cc4-bf6b-eae7e0e310a9', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 43005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210201T005521.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210225T003340.000Z', 'entitlementId': '30623335-3833-3261-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b92c2ce3-b1ab-413a-9d79-bcdfe1c52a92', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 59007, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220209T164922.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30623335-3766-3861-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '443cafb2-3978-4f3a-96c1-d56ab3a7c69a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}]}
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Found CHAMPION_SKIN in items: [{'itemId': 60034, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250308T064821.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'bd0f5e4c-3c53-474b-8235-22d286629656', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c0740a3b-5bf2-43b4-ac50-f79542d707bc', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 98051, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250420T015605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '81557dd9-797c-4899-b310-b80c680adc31', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '47c09d19-79f1-4ee0-b796-473c1a8307d5', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 427011, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210223T205126.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30623335-3833-6436-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'cbdbada4-753e-4420-8f16-a15a8166e9fc', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 48006, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240621T184635.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '66441e41-2ed6-4db0-9764-796cd78601b6', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '9a3f0140-b61f-4f16-a70b-2705d2ebbc63', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 166020, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250420T015605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '4e60e76d-2e90-4b39-9dc0-1f1cef6014c2', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c309178c-f9e3-46c1-b073-4d5175dd62c5', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 133002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20201009T221859.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20201010T005800.000Z', 'entitlementId': '30623335-3835-3531-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'e225245a-aecf-46a2-ae57-fa2a9fc0f855', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 133003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210611T135336.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210630T115928.000Z', 'entitlementId': '30623335-3835-3866-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'e7a0cf29-4fa7-4e97-a146-a0470497c1a6', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 245002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20230131T175508.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '22366e20-1f6c-4fbd-96a4-588ff22994ce', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '05c094de-d840-469e-bc33-b6dc933809ea', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 106003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20191225T115229.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200107T185108.000Z', 'entitlementId': '30623335-3832-6237-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b5c684ce-d35f-4a82-89af-b203cd65287c', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 202001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240525T201834.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20250108T160303.000Z', 'entitlementId': '70aa186c-bbd3-41ac-ae95-ba66ef4cbba0', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '585dd20e-6cdd-4311-becb-2c0cd63003b0', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 106006, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200528T221445.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200528T231401.000Z', 'entitlementId': '30623335-3764-3632-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '1f5fc68d-f12b-4b40-a8ad-3d484fd66501', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 58009, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20191225T115248.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200502T144704.000Z', 'entitlementId': '30623335-3765-3535-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '3629369b-d668-4275-90b9-338233256180', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 10009, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220502T190806.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220502T194002.000Z', 'entitlementId': '30623335-3765-3939-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '37a3b9f9-7b44-47d2-9d03-a913b1006205', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 79007, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200403T064950.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200624T132108.000Z', 'entitlementId': '30623335-3834-3130-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'cc284108-5c6d-4456-a580-67793b80653d', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220710T021236.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30623335-3831-3936-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '85bf1b1a-1378-4385-8224-6b9480cbca89', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 164001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240525T205431.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'fa041e2a-7fb5-4760-8de3-86b1c2821008', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '43c279dd-49eb-4216-a822-fb7924fd8bf0', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 20004, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200314T154145.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200314T161310.000Z', 'entitlementId': '30623335-3764-6132-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '29a23f23-849b-4747-bdcc-4aeb3ae98968', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 91049, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240505T140338.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '9b21baa6-a154-43e9-9753-9278935a0071', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '9232d238-fbbe-462c-a1b4-d5391844f19b', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 36010, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20190711T122404.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20190711T145956.000Z', 'entitlementId': '30623335-3832-3061-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '8bde95da-317b-4d5f-9ef5-f2c5d65d3584', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 25003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20190822T000634.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20190822T221349.000Z', 'entitlementId': '30623335-3766-3531-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '4369ee2c-3c0e-4999-b3b3-7c7e9238e50f', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4013, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20230126T004803.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'b34d7492-3b79-442c-92b4-3e71d7b8ad4a', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '943ce6b7-4e61-404a-9976-2ff98cb89df9', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 9009, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200701T210434.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200701T212946.000Z', 'entitlementId': '30623335-3763-6530-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '17aff872-5d68-49b0-9c05-ef26d61be8ea', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 254002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200717T022030.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210423T174427.000Z', 'entitlementId': '30623335-3762-3562-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '079b3431-39b0-421d-9624-448f9bfdd477', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 30002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20190823T155205.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20190823T214756.000Z', 'entitlementId': '30623335-3835-3131-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'e0f54ec3-d9c9-40b5-b5f2-b4c0d0654a5a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 14005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20191023T104218.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20191115T201253.000Z', 'entitlementId': '30623335-3833-3633-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c4234437-95ae-4ad2-8f22-e2998623a86e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 90038, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250420T015605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '72a2225a-48d9-4f76-b5da-f6d7d985816d', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0a45c80e-6c37-4cbd-bf0f-c615cac05c76', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 78006, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20191023T131802.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20191023T170908.000Z', 'entitlementId': '30623335-3762-6266-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '09cc0990-308a-49a0-b7fd-ddd72338cd3f', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 99001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210629T140035.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210927T131249.000Z', 'entitlementId': '30623335-3436-3535-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '028a5545-4273-402c-88b0-432e9692bd9c', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200907T000335.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200907T003750.000Z', 'entitlementId': '30623335-3832-3432-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '996ec966-bfde-4151-8d1b-5b4101219876', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 83003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20191119T190325.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20191119T213355.000Z', 'entitlementId': '30623335-3766-3134-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '4245fd58-9460-45a6-a729-e12630e9280d', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 99004, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20191023T115258.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20191023T174602.000Z', 'entitlementId': '30623335-3830-6162-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '80f1ba40-4fd4-4704-b02d-2662fccc1c66', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 83004, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20191120T161107.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20191120T161519.000Z', 'entitlementId': '30623335-3836-3433-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'f505a0fc-99ee-4da9-a0d9-f87231e24444', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240505T145009.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'a9509099-a1b3-4e4b-962c-3f4b43678244', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '199af8b3-9c5a-4734-b49c-9c442637d940', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 67006, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220502T190930.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20240502T215739.000Z', 'entitlementId': '30623335-3834-3834-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'de1bb55e-3a0b-4631-a893-44d75279bc6c', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19008, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200927T150236.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20201001T035050.000Z', 'entitlementId': '30623335-3830-6538-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '82073838-dcad-4846-bf80-786be6c25342', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 8002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200731T215654.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200731T225515.000Z', 'entitlementId': '30623335-3766-6333-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '559b06c2-543b-421e-903d-f87b10b6e15a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 120003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20191026T131534.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20191026T131658.000Z', 'entitlementId': '30623335-3831-6430-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '88ff45e1-afeb-48d0-9c5e-3a237bedc4fe', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 8005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210331T190905.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210331T203923.000Z', 'entitlementId': '30623335-3831-3562-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '831b8497-03a6-4d94-aee0-f90af5ba6e50', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 8006, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200119T140442.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200430T211540.000Z', 'entitlementId': '30623335-3763-3432-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '141b01a2-6241-437d-9014-aa8b0b3e5efa', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 99015, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220401T143326.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30623335-3766-6664-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '5e1b18c2-8319-4962-bc59-5dfdacd9912d', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 126024, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20211116T130855.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30623335-3764-3233-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '1c0cefc5-5d3e-408c-96ce-0b60806143ec', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19018, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250420T015605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '6623829e-aea8-42c3-b228-99d0d26f537a', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '861dd57b-e87a-4006-84c5-52e3e16e1ffe', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 77003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210223T205145.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210224T115012.000Z', 'entitlementId': '30623335-3834-3439-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'cdb8f37a-ca19-487e-991c-5e8d1451f3ed', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250420T015605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'a2423995-4fb0-481b-a16a-f442b4d1cf35', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'fe896ef1-2a0c-49d4-a1f3-a8c8a9c14176', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4045, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T084949.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'f6a4d57d-7f63-475c-9216-ce14d9130a44', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '789b9546-e20b-4c29-a77e-d0d9d2c0b1a0', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4046, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T084949.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'be0ea4fa-685e-4c7c-8043-adf892597512', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '25121203-8270-48a5-b9fe-ebbe287d17cb', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 254029, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20211116T130804.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20211206T131943.000Z', 'entitlementId': '30623335-3831-3232-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '8278ac48-e411-4d3a-8439-9dbbe5b7f2b2', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4047, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T084949.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'ba2110e5-6c90-48fc-b7ec-1f596d6e4cc7', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '40012ef7-ae2c-4b13-b044-618f6f9ac327', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19023, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250420T015605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'bc9a8fb7-2fb0-4add-af56-33797d4d4618', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '6f363d48-b85d-4bd0-a9fe-d2d58a1d4674', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4048, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T084949.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '144b92d7-b205-4024-9213-2f4daa646458', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0d3c52e0-8113-4152-83d6-a1369eecf260', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4049, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T084949.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '7d9a1383-a534-4b4d-9165-750a7adac0b4', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0e09fbee-7c7f-48d2-b138-71205d9a771d', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19025, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250420T015605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'ada506f4-6ab0-42f1-8ef4-4760d52e222a', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'd8428f19-daeb-45c5-ad39-db2be9d111a1', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 18002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240525T205452.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '27dcb608-38ab-42a4-988c-12454d6ec55f', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '5c78a3cb-d20a-48b1-b170-1b7ffcc0158e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4050, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T084949.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'b52032e4-ea42-46a3-bd8a-6c08069a59d2', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '8c4d0f20-7a94-44a0-96ae-7bd8cb8ae6e7', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 50004, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20190528T121605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20190528T141655.000Z', 'entitlementId': '30623335-3830-3730-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '671d2f4a-4679-4491-ad0b-3cb4f93f4a48', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 777055, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240604T203106.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '160fc071-4dc4-4b33-8c02-fb80264cc7d9', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c0aa8405-7508-4d18-a83d-2f1a3c21bc46', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 51028, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20211206T142405.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30623335-3830-3337-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '5e64db14-3962-43e1-b199-1c4bee0d43c1', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 222037, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20211206T142357.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30623335-3763-3033-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '1115db96-f8ee-4f4f-a331-336fbb1a965f', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240510T184916.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'cf266c68-141f-42d3-bd72-3ca55ee77e88', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'bdeed643-9ac8-4cb6-b3d1-316ba6e053ff', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29021, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250420T015605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '29acd48a-c520-4dfc-8e2f-0c3ff0f31c41', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '2717bff4-051d-4cd7-8280-c15cafb51e26', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29023, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250420T015605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '68f43942-c994-4b0f-8256-dc97bf75f2e6', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '8f0562cb-7c6b-4f99-91f0-db41ccb9221b', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29024, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250420T015605.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '0426b8a3-d1ed-4a63-8037-008334d6e9db', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '48e5636f-6d6a-4372-b594-6d73cc56380e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55009, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20190505T220256.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20190506T003028.000Z', 'entitlementId': '30623335-3835-6363-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'e8c8ea2c-5d90-4907-b851-6979cfe91010', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 23010, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200520T183656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200521T111408.000Z', 'entitlementId': '30623335-3836-3837-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'fd1c8b7a-cd52-4cb1-829a-43fee7acd51c', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 7012, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240505T140400.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '5eca1e7a-06fd-4f44-8c8d-dfd32772d390', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '7cf1d28f-9772-40a5-a8bb-d32b7dbb18e4', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 76005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20230131T175558.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '3a239d2a-7656-48ab-8262-23cb6918ae8e', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '3d39d31f-6eb7-4fc5-9cd7-a920c0db6b15', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 114022, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20190927T234542.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20191010T212453.000Z', 'entitlementId': '30623335-3832-6631-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b8ae47e7-2b65-4dfa-876a-4dade2fcf13e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 39016, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210427T172220.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210530T090842.000Z', 'entitlementId': '30623335-3765-3162-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '352d8ad1-e80d-42d9-bd65-d1f139f299a6', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 246002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200216T152939.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200216T214531.000Z', 'entitlementId': '30623335-3833-3964-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c93a5139-73b3-45b9-8bbd-b3abe7d8ac10', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 1009, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20191023T115246.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200704T003556.000Z', 'entitlementId': '30623335-3832-3765-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'a9951de2-e800-4ae6-9a57-7216bae4a664', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 1012, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20191030T214507.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20191116T211206.000Z', 'entitlementId': '30623335-3836-3061-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'e9fedd5d-15e6-4798-b75f-dfa03edfea35', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': True}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 92020, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200915T225132.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200916T175025.000Z', 'entitlementId': '30623335-3764-6464-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '2d60434e-f590-4d76-9bad-36269e0c5f63', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 102006, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200222T190210.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200223T055100.000Z', 'entitlementId': '30623335-3765-6438-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '3daa6a01-4082-4800-8c32-139ca0f02579', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 91005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200426T141516.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200426T143945.000Z', 'entitlementId': '30623335-3834-6266-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'df6e9ac8-e902-4996-a3ae-caca5844d610', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 86013, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200112T193644.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200113T201120.000Z', 'entitlementId': '30623335-3561-3564-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '029573f2-b5eb-4cc4-bf6b-eae7e0e310a9', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 43005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210201T005521.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210225T003340.000Z', 'entitlementId': '30623335-3833-3261-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b92c2ce3-b1ab-413a-9d79-bcdfe1c52a92', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 59007, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220209T164922.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30623335-3766-3861-2d30-3939352d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '443cafb2-3978-4f3a-96c1-d56ab3a7c69a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}]
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] CHAMPION_SKIN is a list with 77 items
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Loaded 1941 skin entries from JSON database
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Loaded skin database with 1941 entries
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 60034
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 60034 as: skin - Masque of the Black Rose Elise
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 98051
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 98051 as: skin - Three Honors Shen
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 427011
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 427011 as: skin - Old God Ivern
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 48006
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 48006 as: skin - Dragonslayer Trundle
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 166020
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 166020 as: skin - Three Honors Akshan
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 133002
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 133002 as: skin - Woad Scout Quinn
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 133003
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 133003 as: skin - Corsair Quinn
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 245002
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 245002 as: skin - Academy Ekko
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 106003
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 106003 as: skin - Runeguard Volibear
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 202001
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 202001 as: skin - High Noon Jhin
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 106006
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 106006 as: skin - The Thousand-Pierced Bear
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 58009
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 58009 as: skin - Renektoy
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 10009
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 10009 as: skin - Pentakill Kayle
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 79007
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 79007 as: skin - Superfan Gragas
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4003
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 4003 as: skin - The Magnificent Twisted Fate
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 164001
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 164001 as: skin - Program Camille
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 20004
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 20004 as: skin - Nunu & Willump Bot
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 91049
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 91049 as: skin - Primal Ambush Talon
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 36010
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 36010 as: skin - Frozen Prince Mundo
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 25003
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 25003 as: skin - Blade Mistress Morgana
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4013
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 4013 as: skin - Odyssey Twisted Fate
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 9009
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 9009 as: skin - Praetorian Fiddlesticks
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 254002
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 254002 as: skin - Officer Vi
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 30002
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 30002 as: skin - Statue of Karthus
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 14005
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 14005 as: skin - Mecha Zero Sion
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 90038
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 90038 as: skin - Three Honors Malzahar
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 78006
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 78006 as: skin - Scarlet Hammer Poppy
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 99001
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 99001 as: skin - Sorceress Lux
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19001
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 19001 as: skin - Grey Warwick
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 83003
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 83003 as: skin - Arclight Yorick
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 99004
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 99004 as: skin - Imperial Lux
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 83004
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 83004 as: skin - Meowrick
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19005
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 19005 as: skin - Feral Warwick
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 67006
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 67006 as: skin - Arclight Vayne
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19008
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 19008 as: skin - Marauder Warwick
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 8002
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 8002 as: skin - Marquis Vladimir
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 120003
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 120003 as: skin - Headless Hecarim
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 8005
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 8005 as: skin - Blood Lord Vladimir
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 8006
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 8006 as: skin - Soulstealer Vladimir
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 99015
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 99015 as: skin - Battle Academia Lux
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 126024
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 126024 as: skin - Arcane Inventor Jayce
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19018
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 19018 as: chroma - Grey Warwick (Chroma)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 77003
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 77003 as: skin - Spirit Guard Udyr
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29003
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 29003 as: skin - Medieval Twitch
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4045
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 4045 as: unknown - Unknown Skin (4045)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4046
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 4046 as: unknown - Unknown Skin (4046)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 254029
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 254029 as: skin - Arcane Undercity Vi
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4047
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 4047 as: unknown - Unknown Skin (4047)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19023
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 19023 as: chroma - Grey Warwick (Chroma)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4048
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 4048 as: unknown - Unknown Skin (4048)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4049
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 4049 as: unknown - Unknown Skin (4049)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19025
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 19025 as: chroma - Grey Warwick (Chroma)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 18002
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 18002 as: skin - Earnest Elf Tristana
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4050
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 4050 as: unknown - Unknown Skin (4050)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 50004
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 50004 as: skin - Dragon Master Swain
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 777055
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 777055 as: skin - High Noon Yone
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 51028
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 51028 as: skin - Arcane Enforcer Caitlyn
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 222037
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 222037 as: skin - Arcane Enemy Jinx
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55002
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 55002 as: skin - Red Card Katarina
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29021
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 29021 as: chroma - Medieval Twitch (Chroma)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29023
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 29023 as: chroma - Medieval Twitch (Chroma)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29024
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 29024 as: chroma - Medieval Twitch (Chroma)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55009
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 55009 as: skin - PROJECT: Katarina
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 23010
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 23010 as: skin - Blood Moon Tryndamere
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 7012
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 7012 as: skin - Program LeBlanc
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 76005
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 76005 as: skin - Bewitching Nidalee
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 114022
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 114022 as: skin - Soaring Sword Fiora
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 39016
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 39016 as: skin - PROJECT: Irelia
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 246002
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 246002 as: skin - True Damage Qiyana
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 1009
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 1009 as: skin - Sweetheart Annie
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 1012
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 1012 as: skin - Annie-Versary
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 92020
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 92020 as: skin - Valiant Sword Riven
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 102006
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 102006 as: skin - Super Galaxy Shyvana
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 91005
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 91005 as: skin - Blood Moon Talon
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 86013
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 86013 as: skin - God-King Garen
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 43005
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 43005 as: skin - Warden Karma
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 59007
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 59007 as: skin - Dark Star Jarvan IV
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Extracted 77 skins from response
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 60034
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 60034 as: skin - Masque of the Black Rose Elise
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 98051
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 98051 as: skin - Three Honors Shen
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 427011
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 427011 as: skin - Old God Ivern
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 48006
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 48006 as: skin - Dragonslayer Trundle
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 166020
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 166020 as: skin - Three Honors Akshan
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 133002
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 133002 as: skin - Woad Scout Quinn
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 133003
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 133003 as: skin - Corsair Quinn
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 245002
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 245002 as: skin - Academy Ekko
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 106003
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 106003 as: skin - Runeguard Volibear
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 202001
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 202001 as: skin - High Noon Jhin
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 106006
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 106006 as: skin - The Thousand-Pierced Bear
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 58009
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 58009 as: skin - Renektoy
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 10009
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 10009 as: skin - Pentakill Kayle
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 79007
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 79007 as: skin - Superfan Gragas
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4003
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 4003 as: skin - The Magnificent Twisted Fate
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 164001
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 164001 as: skin - Program Camille
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 20004
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 20004 as: skin - Nunu & Willump Bot
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 91049
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 91049 as: skin - Primal Ambush Talon
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 36010
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 36010 as: skin - Frozen Prince Mundo
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 25003
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 25003 as: skin - Blade Mistress Morgana
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4013
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 4013 as: skin - Odyssey Twisted Fate
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 9009
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 9009 as: skin - Praetorian Fiddlesticks
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 254002
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 254002 as: skin - Officer Vi
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 30002
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 30002 as: skin - Statue of Karthus
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 14005
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 14005 as: skin - Mecha Zero Sion
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 90038
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 90038 as: skin - Three Honors Malzahar
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 78006
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 78006 as: skin - Scarlet Hammer Poppy
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 99001
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 99001 as: skin - Sorceress Lux
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19001
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 19001 as: skin - Grey Warwick
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 83003
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 83003 as: skin - Arclight Yorick
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 99004
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 99004 as: skin - Imperial Lux
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 83004
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 83004 as: skin - Meowrick
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19005
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 19005 as: skin - Feral Warwick
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 67006
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 67006 as: skin - Arclight Vayne
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19008
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 19008 as: skin - Marauder Warwick
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 8002
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 8002 as: skin - Marquis Vladimir
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 120003
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 120003 as: skin - Headless Hecarim
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 8005
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 8005 as: skin - Blood Lord Vladimir
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 8006
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 8006 as: skin - Soulstealer Vladimir
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 99015
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 99015 as: skin - Battle Academia Lux
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 126024
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 126024 as: skin - Arcane Inventor Jayce
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19018
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 19018 as: chroma - Grey Warwick (Chroma)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 77003
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 77003 as: skin - Spirit Guard Udyr
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29003
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 29003 as: skin - Medieval Twitch
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4045
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 4045 as: unknown - Unknown Skin (4045)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4046
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 4046 as: unknown - Unknown Skin (4046)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 254029
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 254029 as: skin - Arcane Undercity Vi
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4047
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 4047 as: unknown - Unknown Skin (4047)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19023
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 19023 as: chroma - Grey Warwick (Chroma)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4048
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 4048 as: unknown - Unknown Skin (4048)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4049
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 4049 as: unknown - Unknown Skin (4049)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19025
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 19025 as: chroma - Grey Warwick (Chroma)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 18002
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 18002 as: skin - Earnest Elf Tristana
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4050
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 4050 as: unknown - Unknown Skin (4050)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 50004
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 50004 as: skin - Dragon Master Swain
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 777055
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 777055 as: skin - High Noon Yone
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 51028
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 51028 as: skin - Arcane Enforcer Caitlyn
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 222037
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 222037 as: skin - Arcane Enemy Jinx
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55002
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 55002 as: skin - Red Card Katarina
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29021
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 29021 as: chroma - Medieval Twitch (Chroma)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29023
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 29023 as: chroma - Medieval Twitch (Chroma)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29024
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 29024 as: chroma - Medieval Twitch (Chroma)
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55009
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 55009 as: skin - PROJECT: Katarina
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 23010
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 23010 as: skin - Blood Moon Tryndamere
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 7012
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 7012 as: skin - Program LeBlanc
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 76005
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 76005 as: skin - Bewitching Nidalee
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 114022
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 114022 as: skin - Soaring Sword Fiora
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 39016
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 39016 as: skin - PROJECT: Irelia
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 246002
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 246002 as: skin - True Damage Qiyana
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 1009
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 1009 as: skin - Sweetheart Annie
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 1012
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 1012 as: skin - Annie-Versary
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 92020
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 92020 as: skin - Valiant Sword Riven
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 102006
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 102006 as: skin - Super Galaxy Shyvana
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 91005
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 91005 as: skin - Blood Moon Talon
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 86013
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 86013 as: skin - God-King Garen
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 43005
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 43005 as: skin - Warden Karma
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 59007
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Categorized skin 59007 as: skin - Dark Star Jarvan IV
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Saving 77 skins for pascuine123
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Got skin data for pascuine123
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Checking penalties for pascuine123
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Checking penalties for EUW using URL: https://euw-red.lol.sgp.pvp.net/leaverbuster-ledge/restrictionInfo
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Penalty check response: {'puuid': '894b494f-38d0-5c68-99e0-5b7c3f2e1fb2', 'rankedRestrictionEntryDto': {'puuid': '894b494f-38d0-5c68-99e0-5b7c3f2e1fb2', 'version': 0, 'restrictedGamesRemaining': 0, 'restrictedGamesOriginal': 0, 'rankedRestrictionPenaltyId': '11aca379-38da-4bb8-841c-66ec2fef2f18', 'punishmentIncurredGameId': 0, 'punishmentIncurredTimeMillis': 0, 'rankedRestrictionAckNeeded': False, 'penaltyOrigin': 'UNKNOWN'}, 'leaverBusterEntryDto': {'puuid': '894b494f-38d0-5c68-99e0-5b7c3f2e1fb2', 'version': 479, 'tainted': True, 'preLockoutAckNeeded': False, 'onLockoutAckNeeded': False, 'leaverScore': 0, 'leaverLevel': 0, 'punishedGamesRemaining': 0, 'currentPunishmentStep': 0, 'leaverPenalty': {'puuid': '894b494f-38d0-5c68-99e0-5b7c3f2e1fb2', 'hasActivePenalty': False, 'delayTime': 0, 'queueLockoutTimerExpiryUtcMillis': 0, 'punishmentTimerType': 'NO_PENALTY', 'rankRestrictedGamesRemaining': 0, 'rankRestrictedTimerExpiryUtcMillis': 0, 'rankRestricted': False}, 'warnSentMillis': 1554648552852, 'warnAckedMillis': 1615559473901, 'lastUpdatedMillis': 1751425313984, 'totalPunishedGamesPlayed': 43, 'lastBustedGameId': 5908640879, 'lastBustedTimeMillis': 1654519647390, 'lastPunishmentIncurredGameId': 5361730638, 'lastPunishmentIncurredTimeMillis': 1625954390347, 'processedGameIdHistoryString': '7381644483,7381684763,7381723545,7381757662,7381791616,7382122530,7382165355,7382202380,7382572397,7449289467,7449316597,7449343340,7449379023,7449387227,7449404497'}}
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] No active penalty
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Got penalty info for pascuine123: 0 minutes, 0 games
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Checking rank for Pascuine#EUW
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Using region code EUW for rank info retrieval
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[90m@←[0m←[97m] get_rank_info called with region_code: EUW, type: <class 'str'>
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[90m@←[0m←[97m] Getting rank info for Pascuine#EUW in region EUW
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[91m-←[0m←[97m] Error decoding JWT: module 'jwt' has no attribute 'decode'
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Found country from userinfo: unknown
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[92m$←[0m←[97m] Current rank data response: {'queues': [{'queueType': 'RANKED_SOLO_5x5', 'provisionalGameThreshold': 5, 'tier': 'EMERALD', 'rank': 'II', 'leaguePoints': 81, 'cumulativeLp': 2281, 'wins': 74, 'losses': 30, 'provisionalGamesRemaining': 0, 'highestTier': 'EMERALD', 'highestRank': 'II', 'previousSeasonEndTier': 'EMERALD', 'previousSeasonEndRank': 'III', 'previousSeasonHighestTier': 'EMERALD', 'previousSeasonHighestRank': 'III', 'previousSeasonAchievedTier': 'EMERALD', 'previousSeasonAchievedRank': 'III', 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_FLEX_SR', 'provisionalGameThreshold': 5, 'tier': 'BRONZE', 'rank': 'III', 'leaguePoints': 76, 'cumulativeLp': 576, 'wins': 1, 'losses': 0, 'provisionalGamesRemaining': 4, 'highestTier': 'BRONZE', 'highestRank': 'III', 'previousSeasonHighestTier': 'BRONZE', 'previousSeasonHighestRank': 'III', 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_TFT', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_TFT_TURBO', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_TFT_DOUBLE_UP', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}], 'highestPreviousSeasonEndTier': 'EMERALD', 'highestPreviousSeasonEndRank': 'III', 'highestPreviousSeasonAchievedTier': 'EMERALD', 'highestPreviousSeasonAchievedRank': 'III', 'earnedRegaliaRewardIds': [], 'splitsProgress': {}, 'seasons': {'RANKED_TFT': {'currentSeasonId': 29, 'currentSeasonEnd': 1753848001000, 'nextSeasonStart': 0}, 'RANKED_TFT_TURBO': {'currentSeasonId': 29, 'currentSeasonEnd': 1753848001000, 'nextSeasonStart': 0}, 'RANKED_FLEX_SR': {'currentSeasonId': 19, 'currentSeasonEnd': 1756249199000, 'nextSeasonStart': 0}, 'RANKED_TFT_DOUBLE_UP': {'currentSeasonId': 29, 'currentSeasonEnd': 1753848001000, 'nextSeasonStart': 0}, 'RANKED_SOLO_5x5': {'currentSeasonId': 19, 'currentSeasonEnd': 1756249199000, 'nextSeasonStart': 0}}, 'jwt': 'eyJzdWIiOiI4OTRiNDk0Zi0zOGQwLTVjNjgtOTllMC01YjdjM2YyZTFmYjIiLCJraWQiOiIxIiwic2hhcmRJZCI6IkVVVzEiLCJzdW1tb25lcklkIjoiMTI1MDQ3NTk4IiwiZXhwIjoxNzUxNDUwMDE2LCJpYXQiOjE3NTE0Mjg0MTYsImFsZyI6IlJTMjU2In0.eyJoaWdoZXN0UHJldmlvdXNTZWFzb25FbmRSYW5rIjoiSUlJIiwiaGlnaGVzdFByZXZpb3VzU2Vhc29uQWNoaWV2ZWRUaWVyIjoiRU1FUkFMRCIsInNlYXNvbnMiOnsiUkFOS0VEX1RGVCI6eyJjdXJyZW50U2Vhc29uSWQiOjI5LCJjdXJyZW50U2Vhc29uRW5kIjoxNzUzODQ4MDAxMDAwLCJuZXh0U2Vhc29uU3RhcnQiOjB9LCJSQU5LRURfVEZUX1RVUkJPIjp7ImN1cnJlbnRTZWFzb25JZCI6MjksImN1cnJlbnRTZWFzb25FbmQiOjE3NTM4NDgwMDEwMDAsIm5leHRTZWFzb25TdGFydCI6MH0sIlJBTktFRF9GTEVYX1NSIjp7ImN1cnJlbnRTZWFzb25JZCI6MTksImN1cnJlbnRTZWFzb25FbmQiOjE3NTYyNDkxOTkwMDAsIm5leHRTZWFzb25TdGFydCI6MH0sIlJBTktFRF9URlRfRE9VQkxFX1VQIjp7ImN1cnJlbnRTZWFzb25JZCI6MjksImN1cnJlbnRTZWFzb25FbmQiOjE3NTM4NDgwMDEwMDAsIm5leHRTZWFzb25TdGFydCI6MH0sIlJBTktFRF9TT0xPXzV4NSI6eyJjdXJyZW50U2Vhc29uSWQiOjE5LCJjdXJyZW50U2Vhc29uRW5kIjoxNzU2MjQ5MTk5MDAwLCJuZXh0U2Vhc29uU3RhcnQiOjB9fSwiaGlnaGVzdFByZXZpb3VzU2Vhc29uRW5kVGllciI6IkVNRVJBTEQiLCJoaWdoZXN0UHJldmlvdXNTZWFzb25BY2hpZXZlZFJhbmsiOiJJSUkiLCJxdWV1ZXMiOlt7InF1ZXVlVHlwZSI6IlJBTktFRF9TT0xPXzV4NSIsInByb3Zpc2lvbmFsR2FtZVRocmVzaG9sZCI6NSwidGllciI6IkVNRVJBTEQiLCJyYW5rIjoiSUkiLCJsZWFndWVQb2ludHMiOjgxLCJjdW11bGF0aXZlTHAiOjIyODEsIm1pbmlTZXJpZXNQcm9ncmVzcyI6bnVsbCwid2lucyI6NzQsImxvc3NlcyI6MzAsInByb3Zpc2lvbmFsR2FtZXNSZW1haW5pbmciOjAsImhpZ2hlc3RUaWVyIjoiRU1FUkFMRCIsImhpZ2hlc3RSYW5rIjoiSUkiLCJwcmV2aW91c1NlYXNvbkVuZFRpZXIiOiJFTUVSQUxEIiwicHJldmlvdXNTZWFzb25FbmRSYW5rIjoiSUlJIiwicHJldmlvdXNTZWFzb25IaWdoZXN0VGllciI6IkVNRVJBTEQiLCJwcmV2aW91c1NlYXNvbkhpZ2hlc3RSYW5rIjoiSUlJIiwicHJldmlvdXNTZWFzb25BY2hpZXZlZFRpZXIiOiJFTUVSQUxEIiwicHJldmlvdXNTZWFzb25BY2hpZXZlZFJhbmsiOiJJSUkiLCJyYXRlZFRpZXIiOm51bGwsInJhdGVkUmF0aW5nIjowLCJ3YXJuaW5ncyI6bnVsbCwicHJlbWFkZU1tclJlc3RyaWN0ZWQiOmZhbHNlfSx7InF1ZXVlVHlwZSI6IlJBTktFRF9GTEVYX1NSIiwicHJvdmlzaW9uYWxHYW1lVGhyZXNob2xkIjo1LCJ0aWVyIjoiQlJPTlpFIiwicmFuayI6IklJSSIsImxlYWd1ZVBvaW50cyI6NzYsImN1bXVsYXRpdmVMcCI6NTc2LCJtaW5pU2VyaWVzUHJvZ3Jlc3MiOm51bGwsIndpbnMiOjEsImxvc3NlcyI6MCwicHJvdmlzaW9uYWxHYW1lc1JlbWFpbmluZyI6NCwiaGlnaGVzdFRpZXIiOiJCUk9OWkUiLCJoaWdoZXN0UmFuayI6IklJSSIsInByZXZpb3VzU2Vhc29uRW5kVGllciI6bnVsbCwicHJldmlvdXNTZWFzb25FbmRSYW5rIjpudWxsLCJwcmV2aW91c1NlYXNvbkhpZ2hlc3RUaWVyIjoiQlJPTlpFIiwicHJldmlvdXNTZWFzb25IaWdoZXN0UmFuayI6IklJSSIsInByZXZpb3VzU2Vhc29uQWNoaWV2ZWRUaWVyIjpudWxsLCJwcmV2aW91c1NlYXNvbkFjaGlldmVkUmFuayI6bnVsbCwicmF0ZWRUaWVyIjpudWxsLCJyYXRlZFJhdGluZyI6MCwid2FybmluZ3MiOm51bGwsInByZW1hZGVNbXJSZXN0cmljdGVkIjpmYWxzZX0seyJxdWV1ZVR5cGUiOiJSQU5LRURfVEZUIiwicHJvdmlzaW9uYWxHYW1lVGhyZXNob2xkIjo1LCJ0aWVyIjpudWxsLCJyYW5rIjpudWxsLCJsZWFndWVQb2ludHMiOjAsImN1bXVsYXRpdmVMcCI6MCwibWluaVNlcmllc1Byb2dyZXNzIjpudWxsLCJ3aW5zIjowLCJsb3NzZXMiOjAsInByb3Zpc2lvbmFsR2FtZXNSZW1haW5pbmciOjAsImhpZ2hlc3RUaWVyIjpudWxsLCJoaWdoZXN0UmFuayI6bnVsbCwicHJldmlvdXNTZWFzb25FbmRUaWVyIjpudWxsLCJwcmV2aW91c1NlYXNvbkVuZFJhbmsiOm51bGwsInByZXZpb3VzU2Vhc29uSGlnaGVzdFRpZXIiOm51bGwsInByZXZpb3VzU2Vhc29uSGlnaGVzdFJhbmsiOm51bGwsInByZXZpb3VzU2Vhc29uQWNoaWV2ZWRUaWVyIjpudWxsLCJwcmV2aW91c1NlYXNvbkFjaGlldmVkUmFuayI6bnVsbCwicmF0ZWRUaWVyIjpudWxsLCJyYXRlZFJhdGluZyI6MCwid2FybmluZ3MiOm51bGwsInByZW1hZGVNbXJSZXN0cmljdGVkIjpmYWxzZX0seyJxdWV1ZVR5cGUiOiJSQU5LRURfVEZUX1RVUkJPIiwicHJvdmlzaW9uYWxHYW1lVGhyZXNob2xkIjo1LCJ0aWVyIjpudWxsLCJyYW5rIjpudWxsLCJsZWFndWVQb2ludHMiOjAsImN1bXVsYXRpdmVMcCI6MCwibWluaVNlcmllc1Byb2dyZXNzIjpudWxsLCJ3aW5zIjowLCJsb3NzZXMiOjAsInByb3Zpc2lvbmFsR2FtZXNSZW1haW5pbmciOjAsImhpZ2hlc3RUaWVyIjpudWxsLCJoaWdoZXN0UmFuayI6bnVsbCwicHJldmlvdXNTZWFzb25FbmRUaWVyIjpudWxsLCJwcmV2aW91c1NlYXNvbkVuZFJhbmsiOm51bGwsInByZXZpb3VzU2Vhc29uSGlnaGVzdFRpZXIiOm51bGwsInByZXZpb3VzU2Vhc29uSGlnaGVzdFJhbmsiOm51bGwsInByZXZpb3VzU2Vhc29uQWNoaWV2ZWRUaWVyIjpudWxsLCJwcmV2aW91c1NlYXNvbkFjaGlldmVkUmFuayI6bnVsbCwicmF0ZWRUaWVyIjpudWxsLCJyYXRlZFJhdGluZyI6MCwid2FybmluZ3MiOm51bGwsInByZW1hZGVNbXJSZXN0cmljdGVkIjpmYWxzZX0seyJxdWV1ZVR5cGUiOiJSQU5LRURfVEZUX0RPVUJMRV9VUCIsInByb3Zpc2lvbmFsR2FtZVRocmVzaG9sZCI6NSwidGllciI6bnVsbCwicmFuayI6bnVsbCwibGVhZ3VlUG9pbnRzIjowLCJjdW11bGF0aXZlTHAiOjAsIm1pbmlTZXJpZXNQcm9ncmVzcyI6bnVsbCwid2lucyI6MCwibG9zc2VzIjowLCJwcm92aXNpb25hbEdhbWVzUmVtYWluaW5nIjowLCJoaWdoZXN0VGllciI6bnVsbCwiaGlnaGVzdFJhbmsiOm51bGwsInByZXZpb3VzU2Vhc29uRW5kVGllciI6bnVsbCwicHJldmlvdXNTZWFzb25FbmRSYW5rIjpudWxsLCJwcmV2aW91c1NlYXNvbkhpZ2hlc3RUaWVyIjpudWxsLCJwcmV2aW91c1NlYXNvbkhpZ2hlc3RSYW5rIjpudWxsLCJwcmV2aW91c1NlYXNvbkFjaGlldmVkVGllciI6bnVsbCwicHJldmlvdXNTZWFzb25BY2hpZXZlZFJhbmsiOm51bGwsInJhdGVkVGllciI6bnVsbCwicmF0ZWRSYXRpbmciOjAsIndhcm5pbmdzIjpudWxsLCJwcmVtYWRlTW1yUmVzdHJpY3RlZCI6ZmFsc2V9XX0.NagZkj5HsU-fzFcTdrPUzJccG3jqFz4OXPorFmEUq1wa5zKwJ43ypBMoG55zhzrac58nL0NSMtiOjzMk9vSEM7xbz-PoioL7TbVzjdOEO_PflQVCmZv8F6KutFtNvu_LqHo83w4gAOaUbJcMyGHd3Tb4YObDZ7imqiLdK5cxW1s'}
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[90m@←[0m←[97m] scrape_account_info called with region_code: EUW, type: <class 'str'>
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[90m@←[0m←[97m] Scraping account info from: https://www.leagueofgraphs.com/summoner/euw/Pascuine-EUW
←[0m←[97m[2025-07-02 05:53:35] [←[0m←[90m@←[0m←[97m] Got HTML response length: 1413773
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Found 9 season tags
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Processing tag: S9 Bronze with tooltip: <itemname class='tagTitle brown'>S9 Bronze</itemname><br/><span class='tagDescription'><highlight>Ranked Solo/Duo</highlight><br/>This player reached Bronze IV during Season 9. At the end of the season, this player was Bronze IV.<br/></span>
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Processing tag: S10 Bronze with tooltip: <itemname class='tagTitle brown'>S10 Bronze</itemname><br/><span class='tagDescription'><highlight>Ranked Solo/Duo</highlight><br/>This player reached Bronze IV during Season 10. At the end of the season, this player was Bronze IV.<br/></span>
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Processing tag: S11 Bronze with tooltip: <itemname class='tagTitle brown'>S11 Bronze</itemname><br/><span class='tagDescription'><highlight>Ranked Solo/Duo</highlight><br/>This player reached Bronze III during Season 11. At the end of the season, this player was Bronze IV.<br/></span>
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Processing tag: S14 (Split 1) Bronze with tooltip: <itemname class='tagTitle brown'>S14 (Split 1) Bronze</itemname><br/><span class='tagDescription'><highlight>Ranked Solo/Duo</highlight><br/>This player reached Bronze II during Season 14 (Split 1). At the end of the season, this player was Bronze III.<br/></span>
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Processing tag: S14 (Split 2) Bronze with tooltip: <itemname class='tagTitle brown'>S14 (Split 2) Bronze</itemname><br/><span class='tagDescription'><highlight>Ranked Solo/Duo</highlight><br/>This player reached Bronze I during Season 14 (Split 2). At the end of the season, this player was Bronze II.<br/></span>
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Processing tag: S14 (Split 3) Bronze with tooltip: <itemname class='tagTitle brown'>S14 (Split 3) Bronze</itemname><br/><span class='tagDescription'><highlight>Ranked Solo/Duo</highlight><br/>This player reached Bronze I during Season 14 (Split 3). At the end of the season, this player was Bronze I.<br/></span>
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Processing tag: Hot Streak with tooltip: <itemname class='tagTitle green'>Hot Streak</itemname><br/><span class='tagDescription'>This player has won their last <span class='highlight'>6</span> games in a row</span>
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Processing tag: Snowballs with tooltip: <itemname class='tagTitle green'>Snowballs</itemname><br/><span class='tagDescription'>This player has a better winrate when 2/0, 3/1... at 10 minutes than the other <span class='highlight'>Emerald</span> players: +<span class='highlight'>30</span>%</span>
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Processing tag: Never gives up with tooltip: <itemname class='tagTitle yellow'>Never gives up</itemname><br/><span class='tagDescription'>This player very rarely surrenders</span>
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Found previous season rank from LeagueOfGraphs: {'tier': 'BRONZE', 'division': 'I'}
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Stored complete season history with 6 entries
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Processed rank info: {'solo': {'tier': 'EMERALD', 'division': 'II', 'lp': 81, 'wins': 74, 'losses': 30, 'previous_tier': 'BRONZE', 'previous_division': 'I'}, 'flex': {'tier': 'BRONZE', 'division': 'III', 'lp': 76, 'wins': 1, 'losses': 0, 'previous_tier': 'UNRANKED', 'previous_division': ''}, 'country': 'unknown', 'creation_region': 'EUW', 'season_history': [{'season': 14, 'split': '3', 'queue_type': 'SOLO_DUO', 'peak_tier': 'BRONZE', 'peak_division': 'I', 'end_tier': 'BRONZE', 'end_division': 'I'}, {'season': 14, 'split': '2', 'queue_type': 'SOLO_DUO', 'peak_tier': 'BRONZE', 'peak_division': 'I', 'end_tier': 'BRONZE', 'end_division': 'II'}, {'season': 14, 'split': '1', 'queue_type': 'SOLO_DUO', 'peak_tier': 'BRONZE', 'peak_division': 'II', 'end_tier': 'BRONZE', 'end_division': 'III'}, {'season': 11, 'split': None, 'queue_type': 'SOLO_DUO', 'peak_tier': 'BRONZE', 'peak_division': 'III', 'end_tier': 'BRONZE', 'end_division': 'IV'}, {'season': 10, 'split': None, 'queue_type': 'SOLO_DUO', 'peak_tier': 'BRONZE', 'peak_division': 'IV', 'end_tier': 'BRONZE', 'end_division': 'IV'}, {'season': 9, 'split': None, 'queue_type': 'SOLO_DUO', 'peak_tier': 'BRONZE', 'peak_division': 'IV', 'end_tier': 'BRONZE', 'end_division': 'IV'}]}
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Got rank info for pascuine123
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Including 6 season history entries for pascuine123
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Saving rank info for pascuine123: Solo=EMERALD II, Flex=BRONZE III
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Successfully rechecked account: pascuine123
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Loading account pascuine123 from database
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Account details: BE=8804, RP=0, Level=215
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Account info: created_at=2019-04-06 18:22:35, creation_region=EUW, country=egy
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Game info: game_name=Pascuine, tag_line=EUW
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Checking JWT token for region information for pascuine123
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] JWT region debugging for pascuine123:
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m]   lol field: [{'cuid': ****************, 'cpid': 'EUW1', 'uid': ****************, 'uname': 'pascuine123', 'ptrid': None, 'pid': 'EUW1', 'state': 'ENABLED'}]
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m]   region field: {}
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m]   original_platform_id:
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m]   lol_region: [{'active': True, 'cpid': 'EUW1', 'cuid': ****************, 'lp': False, 'pid': 'EUW1', 'uid': ****************}]
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m]   dat field: {}
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Using lol_region[].cpid to determine region for pascuine123: EUW
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Found region in JWT for pascuine123: EUW
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Loaded 6 season history entries for pascuine123
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Using region=EUW, region_code=EUW for account pascuine123
←[0m←[97m[2025-07-02 05:53:36] [←[0m←[92m$←[0m←[97m] Loaded 1 accounts from database
←[0mDEBUG: PyQtBridge.handle_account_selection called with index: 0
DEBUG: Updated PyQtBridge.selected_account_index to: 0
DEBUG: Updated main_window.selected_account_index via set_selected_account_index
DEBUG: Generating HTML for 6 chromas
