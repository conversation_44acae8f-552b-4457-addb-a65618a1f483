
C:\Users\<USER>\Desktop\sources\League Acc Checker>py main.py
←[97m[2025-07-01 21:38:47] [←[0m←[92m$←[0m←[97m] Created DatabaseManagerWrapper with path accounts.db
←[0m←[97m[2025-07-01 21:38:47] [←[0m←[92m$←[0m←[97m] Wrapped db_manager with DatabaseManagerWrapper in AccountPool
←[0m←[97m[2025-07-01 21:38:48] [←[0m←[92m$←[0m←[97m] Loaded 1941 skin entries from JSON database
←[0mDEBUG: Created CustomWebEngineView
DEBUG: Created web view with custom context menu handling
Loaded 1941 skin entries from JSON database
DEBUG: Sharing champion database with 171 entries to PyQtBridge and AccountPool
←[97m[2025-07-01 21:38:49] [←[0m←[92m$←[0m←[97m] Loaded 0 accounts from database
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Importing accounts from clipboard
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Loading account konjisenpaii from database
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Account details: BE=0, RP=0, Level=0
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Account info: created_at=None, creation_region=None, country=None
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Game info: game_name=None, tag_line=None
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Loaded 6 season history entries for konjisenpaii
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] No region information found for konjisenpaii, leaving region as None
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Using region=None, region_code=None for account konjisenpaii
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Loaded 1 accounts from database
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Successfully imported 1 accounts from clipboard
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Loading account konjisenpaii from database
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Account details: BE=0, RP=0, Level=0
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Account info: created_at=None, creation_region=None, country=None
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Game info: game_name=None, tag_line=None
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Loaded 6 season history entries for konjisenpaii
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] No region information found for konjisenpaii, leaving region as None
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Using region=None, region_code=None for account konjisenpaii
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Loaded 1 accounts from database
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Starting to recheck account: konjisenpaii
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Initializing authenticator for account: konjisenpaii
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Initializing authenticator for konjisenpaii with region None
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[93m!←[0m←[97m] No region code provided for konjisenpaii, authentication may fail
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Authenticator initialized for konjisenpaii in region
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Authenticator initialized for konjisenpaii in region None
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] No refresh token available for konjisenpaii
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Rechecking account: konjisenpaii
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Starting check for account konjisenpaii with region None
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Getting userinfo for konjisenpaii
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Got account info for konjisenpaii: {'username': 'konjisenpaii', 'password': 'majamys1', 'region': '', 'refresh_token': '', 'access_token': None, 'id_token': None, 'entitlements_token': None, 'userinfo': None, 'timestamp': 4939, 'puuid': None, 'account_id': None, 'game_name': None, 'tag_line': None, 'champion_count': 0, 'blue_essence': 0, 'riot_points': 0, 'summoner_level': 0, 'created_at': None, 'last_checked': '2025-07-01 19:38:54', 'penalty_minutes': 0, 'ranked_games_remaining': 0, 'is_banned': 0, 'ban_info': None, 'country': None, 'creation_region': None, 'solo_tier': 'UNRANKED', 'solo_division': '', 'solo_lp': 0, 'solo_wins': 0, 'solo_losses': 0, 'solo_previous_tier': 'UNRANKED', 'solo_previous_division': '', 'flex_tier': 'UNRANKED', 'flex_division': '', 'flex_lp': 0, 'flex_wins': 0, 'flex_losses': 0, 'flex_previous_tier': 'UNRANKED', 'flex_previous_division': '', 'chat_restrictions': None, 'is_glitch_account': 0, 'region_code': None, 'rank_info': {'solo': {'tier': 'UNRANKED', 'division': '', 'lp': 0, 'wins': 0, 'losses': 0, 'previous_tier': 'UNRANKED', 'previous_division': ''}, 'flex': {'tier': 'UNRANKED', 'division': '', 'lp': 0, 'wins': 0, 'losses': 0, 'previous_tier': 'UNRANKED', 'previous_division': ''}, 'season_history': [{'season': 14, 'split': '2', 'queue_type': 'SOLO_DUO', 'peak_tier': 'GOLD', 'peak_division': 'IV', 'end_tier': 'GOLD', 'end_division': 'IV'}, {'season': 14, 'split': '1', 'queue_type': 'SOLO_DUO', 'peak_tier': 'SILVER', 'peak_division': 'II', 'end_tier': 'SILVER', 'end_division': 'II'}, {'season': 13, 'split': '2', 'queue_type': 'SOLO_DUO', 'peak_tier': 'BRONZE', 'peak_division': 'I', 'end_tier': 'BRONZE', 'end_division': 'I'}, {'season': 12, 'split': None, 'queue_type': 'SOLO_DUO', 'peak_tier': 'SILVER', 'peak_division': 'I', 'end_tier': 'SILVER', 'end_division': 'II'}, {'season': 11, 'split': None, 'queue_type': 'SOLO_DUO', 'peak_tier': 'SILVER', 'peak_division': 'II', 'end_tier': 'SILVER', 'end_division': 'II'}, {'season': 10, 'split': None, 'queue_type': 'SOLO_DUO', 'peak_tier': 'BRONZE', 'peak_division': 'I', 'end_tier': 'BRONZE', 'end_division': 'I'}]}}
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Using refresh token for konjisenpaii: no
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Falling back to password auth for konjisenpaii
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Solve captcha starting for konjisenpaii
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Initial post payload: {'clientId': 'lol', 'language': 'en_US', 'platform': 'windows', 'remember': False, 'riot_identity': {'state': 'auth'}, 'sdkVersion': '24.8.0.4145', 'type': 'auth'}
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Post response status: 200
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Got captcha challenge with sitekey: 019f1553-3845-481c-a6f5-5a60ccf6d830
←[0m←[97m[2025-07-01 21:38:54] [←[0m←[92m$←[0m←[97m] Starting captcha solver subprocess
←[0m←[97m[2025-07-01 21:38:58] [←[0m←[92m$←[0m←[97m] Captcha solver process completed
←[0m←[97m[2025-07-01 21:38:58] [←[0m←[92m$←[0m←[97m] Got captcha token: P1_eyJ0eXAiOiJKV1QiL
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] Extracted login token
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] Extracted authorization code
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] Successfully retrieved tokens for konjisenpaii
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] Solve captcha result: True
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] Extracted region from JWT lol_region[].cpid: EUW
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] Updating region from  to EUW based on JWT token
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] Using access token for userinfo: eyJraWQiOi...3ZFjD-bC3g
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] Access token length: 1233
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] Userinfo request URL: https://auth.riotgames.com/userinfo
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] Userinfo request headers: Authorization: Bearer eyJraWQiOi...3ZFjD-bC3g, Accept: application/json, Content-Type: application/json
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] Raw userinfo response (first 100 chars): eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwidHlwIjoidXNlcmluZm8rand0IiwiYWxnIjoiUlMyNTYifQ.eyJjb3VudHJ5Ijoic
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] Userinfo response length: 1954
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] Received JWT token for userinfo
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] JWT token has 3 parts
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] Successfully decoded JWT payload with 25 keys
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[93m!←[0m←[97m] Ban info in JWT payload: {
  "restrictions": []
}
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[93m!←[0m←[97m] Found 0 ban restrictions
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] Successfully decoded JWT payload with 25 keys
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] Successfully authenticated konjisenpaii with password
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] Got userinfo for konjisenpaii, decoding JWT
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] Getting tokens for konjisenpaii
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[91m-←[0m←[97m] Error parsing userinfo string in get_queue_token: Expecting value: line 1 column 1 (char 0)
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - URL: https://euc1-red.pp.sgp.pvp.net/login-queue/v2/login/products/lol/regions/EUW1
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Region: EUW, Region Upper: EUW1
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Player Platform Edge URL: https://euc1-red.pp.sgp.pvp.net
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Headers: {"Authorization": "Bearer eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwiYWxnIjoiUlMyNTYifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PWlm4zAVWqecDOi5IY1IORCHv_YA_9vPA4WAzjbTDEivgKTdvII39Zmf2zcmW1aO772ps6M0tThiN2ojKCOzXo6cTaWt-cb8_lL0iHhp64h938JwAsCeSLaTUdq0wwQ2f3uNQL-VmR-Rtvw-GerkyXkXGfILVrHA523kt68_nBPu4fY1MYp1Il4rElxePRgWFwKbiHJwoJJ5eS-pNTM1ttuduwxKrQVcrXCCibt4wtUCX-waEZqlyQkoq_g4U35bhZ8zf7or9MxuUF8Wlho5Z1IbIHLYs3qAcV0s01At67mLx5upRSrFp1iHybVLS8vS_CxWJ7NQzBuG3ZFjD-bC3g", "Accept": "application/json", "Content-Type": "application/json"}
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Body: {"clientName": "lcu", "entitlements": "*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "userinfo": "eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwidHlwIjoidXNlcmluZm8rand0IiwiYWxnIjoiUlMyNTYifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fZNs3ylz44Z0yon-gVEte4V9Uan2dcvwPw3kwSpRbp6oUavhpTzDpVKzvibnc4YCVJ4qHm-yF3GumexlGeKAO2Lc_r63ruriOZXNPaLb0RYMTJZMD-EkvCbtenxF14LFjwCK5du4KabKgPZ1XNWFCgL3ps8pyQhixJr_CPKUkh4B-fTd1LlyTJTyUktGt0b7wYulCgnfqUDAcy4yjnsqSoLKCcTHpNENvc8kq-qRNiVEqyW9lepL6xQVvo0rSJqAgh6oOwaLoPBS0HfJcSbhUEfrZydqc8p5f9pxVESaa_Cf2pFxvmdBxaqDb6tDk_ut9wy1lm702xKIAnrwcSV7zA"}
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Status: 200
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Headers: {"date": "Tue, 01 Jul 2025 19:39:00 GMT", "content-type": "application/json;charset=utf-8", "transfer-encoding": "chunked", "connection": "keep-alive", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "expires": "0", "pragma": "no-cache", "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers, accept-encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-riot-edge-trace-id": "eec0f648-32d1-4f26-a23c-ea36b2785a74", "x-xss-protection": "0", "content-encoding": "gzip", "access-control-allow-origin": "*", "access-control-allow-methods": "GET, PUT, DELETE, POST, OPTIONS", "access-control-allow-headers": "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range", "access-control-expose-headers": "Content-Length,Content-Range", "cf-cache-status": "DYNAMIC", "set-cookie": "__cf_bm=ut7T4hdKmy0Y19gL_0_hHpVsqMseYRUT8nI5E943f3I-1751398740-*******-Up2mJlbSpsFInACmIH9ecIe2DnvEhC5zgx2Hves0y8jYmfe_hA.QrTMPC1UOhZ1UyEHZgrMHMz6_cJWZWqegxJJ1Tadkavvqgz3IeBfgz_o; path=/; expires=Tue, 01-Jul-25 20:09:00 GMT; domain=.sgp.pvp.net; HttpOnly; Secure; SameSite=None", "server": "cloudflare", "cf-ray": "95885df1da52716f-DUS"}
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Body: {"token": "eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.OXss7qdNj5xHuBjjm3aTWkrmcIrSk4-zSctsCRcbl8qm7GOXm0tWfq8OKar3rpbPTQSIMzdZSUaJbWe19RlGN-iUsXHbbyRT8xWSXfd_cyqDlcPJtEol3GGel7VhBtC3LD_A2W5mOYJ5eNcMi6YEQj_6_8OwozJVPWBcrMT0siaaeGBivUIZoeujycN7drYAypoA-zaf__TFQ_c0shgPKhhfFGC5ofgrabM2PeUn5jB4H11VGxAphJPCu9QwbG1S0R6vq1ma5XlxHxzCH7l21uvaBbPER_B0ExQRONLBmUXuMo5mOxV7w1CwDvUCB-YUl4X9z-3jYdbLVud_Afc1aw", "type": "LOGIN"}
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[91m-←[0m←[97m] Error parsing userinfo string in get_session_token: Expecting value: line 1 column 1 (char 0)
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Got JWT token from queue_token
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Parsed access token, PUUID: 6c47ee27-a616-527f-a007-582ff470c05f
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - URL: https://euc1-red.pp.sgp.pvp.net/session-external/v1/session/create
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Region: EUW, Region Upper: EUW1
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Player Platform Edge URL: https://euc1-red.pp.sgp.pvp.net
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Headers: {"Authorization": "Bearer eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.OXss7qdNj5xHuBjjm3aTWkrmcIrSk4-zSctsCRcbl8qm7GOXm0tWfq8OKar3rpbPTQSIMzdZSUaJbWe19RlGN-iUsXHbbyRT8xWSXfd_cyqDlcPJtEol3GGel7VhBtC3LD_A2W5mOYJ5eNcMi6YEQj_6_8OwozJVPWBcrMT0siaaeGBivUIZoeujycN7drYAypoA-zaf__TFQ_c0shgPKhhfFGC5ofgrabM2PeUn5jB4H11VGxAphJPCu9QwbG1S0R6vq1ma5XlxHxzCH7l21uvaBbPER_B0ExQRONLBmUXuMo5mOxV7w1CwDvUCB-YUl4X9z-3jYdbLVud_Afc1aw", "Accept": "application/json", "Content-Type": "application/json"}
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Body: {"claims": {"cname": "lcu"}, "product": "lol", "puuid": "6c47ee27-a616-527f-a007-582ff470c05f", "region": "EUW1"}
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Status: 200
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Headers: {"date": "Tue, 01 Jul 2025 19:39:00 GMT", "content-type": "application/json;charset=utf-8", "transfer-encoding": "chunked", "connection": "keep-alive", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "expires": "0", "pragma": "no-cache", "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers, accept-encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-riot-edge-trace-id": "c7c5123a-d522-4570-ba71-5be93b282cc0", "x-xss-protection": "0", "content-encoding": "gzip", "access-control-allow-origin": "*", "access-control-allow-methods": "GET, PUT, DELETE, POST, OPTIONS", "access-control-allow-headers": "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range", "access-control-expose-headers": "Content-Length,Content-Range", "cf-cache-status": "DYNAMIC", "server": "cloudflare", "cf-ray": "95885df23a83716f-DUS"}
←[0m←[97m[2025-07-01 21:38:59] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Text: "eyJraWQiOiJzMSIsImFsZyI6IlJTMjU2In0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CmSvm5qsvXVmZ1mHcwI1lsRYGPzXqBgxtrqdE39HBLAKjKd3-jq1foPwQuAsoaA4QlBhuCGwvX6upr9S2h9QY3MFfuMEF4jN-qH6nW2-leYXMPWRwffZas-97DOqneEj2u1OP34Tp9lNUovgnUaH_6Dc7_6VO6iWXaXP-KzDrpBrHrbz_xChROtugUrqJGXRKXOmkt_63ZdvZk5y3uJ35WKfkkGw9zVrHl23yOZse_xhZcu9BoBrr0J_yyir5vBXKUiBdWu3nZEvGERt6iM_12cDDnG8FxCcYg5Yb0WUebvAxwhAdoUmQtPKJt0S96lGb5XR2wPRrr4sKXJwKX6oUg"
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Got all tokens for konjisenpaii
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Importing get_owned_champions for konjisenpaii
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Extracted region from userinfo original_platform_id: EUW
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Using account region for champion data retrieval: EUW for konjisenpaii
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Calling get_owned_champions for konjisenpaii with region EUW
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Getting owned champions for region: EUW
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Headers set up: Authorization and Entitlements tokens configured
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] TCP Connector created with IPv4 family and SSL disabled
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Trying URL pattern 1/4
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Using base URL: https://euw-red.lol.sgp.pvp.net
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Inventory URL: https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v2/inventoriesWithLoyalty
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Wallet URL: https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v1/walletsbalances
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Location format: lolriot.aws-euc1-prod.euw
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Inventory params: {'puuid': '6c47ee27-a616-527f-a007-582ff470c05f', 'accountId': '****************', 'inventoryTypes': 'CHAMPION', 'location': 'lolriot.aws-euc1-prod.euw'}
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Attempting to resolve hostname: euw-red.lol.sgp.pvp.net
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Successfully resolved euw-red.lol.sgp.pvp.net to *************
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Successfully retrieved inventory data with pattern 1
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Found 94 owned champions
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Requesting wallet data from: https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v1/walletsbalances
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Wallet params: {'puuid': '6c47ee27-a616-527f-a007-582ff470c05f', 'location': 'lolriot.aws-euc1-prod.euw', 'accountId': '****************', 'currencyTypes': ['RP', 'lol_blue_essence', 'tft_star_fragments']}
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Successfully retrieved wallet data with pattern 1
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Successfully retrieved all data with pattern 1
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[90m@←[0m←[97m] Session closed properly
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Returned from get_owned_champions for konjisenpaii
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Extracted creation date for konjisenpaii: 2020-03-24 10:08:02 from timestamp *************
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Extracted creation region for konjisenpaii: EUW from tag euw
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Setting account region to EUW based on API data
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Saving comprehensive account info for konjisenpaii: BE=6385, RP=0, Level=167
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Saving 94 champions for konjisenpaii
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Getting skin data for konjisenpaii
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Extracted skin region from userinfo original_platform_id: EUW
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Using account region for skin data retrieval: EUW for konjisenpaii
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Getting owned skins for account 6c47ee27-a616-527f-a007-582ff470c05f
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Found account for puuid 6c47ee27-a616-527f-a007-582ff470c05f
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Extracted account ID **************** from userinfo token
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[91m-←[0m←[97m] Error parsing userinfo string in get_queue_token: Expecting value: line 1 column 1 (char 0)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - URL: https://euc1-red.pp.sgp.pvp.net/login-queue/v2/login/products/lol/regions/EUW1
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Region: EUW, Region Upper: EUW1
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Player Platform Edge URL: https://euc1-red.pp.sgp.pvp.net
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Headers: {"Authorization": "Bearer eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwiYWxnIjoiUlMyNTYifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PWlm4zAVWqecDOi5IY1IORCHv_YA_9vPA4WAzjbTDEivgKTdvII39Zmf2zcmW1aO772ps6M0tThiN2ojKCOzXo6cTaWt-cb8_lL0iHhp64h938JwAsCeSLaTUdq0wwQ2f3uNQL-VmR-Rtvw-GerkyXkXGfILVrHA523kt68_nBPu4fY1MYp1Il4rElxePRgWFwKbiHJwoJJ5eS-pNTM1ttuduwxKrQVcrXCCibt4wtUCX-waEZqlyQkoq_g4U35bhZ8zf7or9MxuUF8Wlho5Z1IbIHLYs3qAcV0s01At67mLx5upRSrFp1iHybVLS8vS_CxWJ7NQzBuG3ZFjD-bC3g", "Accept": "application/json", "Content-Type": "application/json"}
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Body: {"clientName": "lcu", "entitlements": "*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "userinfo": "eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwidHlwIjoidXNlcmluZm8rand0IiwiYWxnIjoiUlMyNTYifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fZNs3ylz44Z0yon-gVEte4V9Uan2dcvwPw3kwSpRbp6oUavhpTzDpVKzvibnc4YCVJ4qHm-yF3GumexlGeKAO2Lc_r63ruriOZXNPaLb0RYMTJZMD-EkvCbtenxF14LFjwCK5du4KabKgPZ1XNWFCgL3ps8pyQhixJr_CPKUkh4B-fTd1LlyTJTyUktGt0b7wYulCgnfqUDAcy4yjnsqSoLKCcTHpNENvc8kq-qRNiVEqyW9lepL6xQVvo0rSJqAgh6oOwaLoPBS0HfJcSbhUEfrZydqc8p5f9pxVESaa_Cf2pFxvmdBxaqDb6tDk_ut9wy1lm702xKIAnrwcSV7zA"}
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Status: 200
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Headers: {"date": "Tue, 01 Jul 2025 19:39:01 GMT", "content-type": "application/json;charset=utf-8", "transfer-encoding": "chunked", "connection": "keep-alive", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "expires": "0", "pragma": "no-cache", "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers, accept-encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-riot-edge-trace-id": "ee74134f-9ec4-4622-907f-45d07bc0e4d2", "x-xss-protection": "0", "content-encoding": "gzip", "access-control-allow-origin": "*", "access-control-allow-methods": "GET, PUT, DELETE, POST, OPTIONS", "access-control-allow-headers": "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range", "access-control-expose-headers": "Content-Length,Content-Range", "cf-cache-status": "DYNAMIC", "server": "cloudflare", "cf-ray": "95885df43b66716f-DUS"}
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Body: {"token": "eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dDrK8zrgSVRcqNJhxAr661ZqKV1Gzw9f7vp2pLtsIuY42YcMPonvFwlZGbc5RcC1CekciOq3b4_ZYQer1wcgHazd0q9pWnDPqk2Y75yea45NeK70a5mO7m8lAOpLtF_VCHtNvKubokjcKmHztL0drpvHgBpf0yHalFcd94pGxAZEj1gGFAYXOc7LMXhaahFxZt-K39_pDF_TeOAgvwkIiPLY_4_02fQMJdT5OPvZQUm9_1XJwpf_BHTKzTz63Sot2zUHJPvXIBGZrfpuilq7fueWcof4WJwnUgSafNFrpqkThiP91VWpPLZ38v0RmuFFpEwvCNhC_cd-izs-wH3m4Q", "type": "LOGIN"}
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[91m-←[0m←[97m] Error parsing userinfo string in get_session_token: Expecting value: line 1 column 1 (char 0)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Got JWT token from queue_token
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Parsed access token, PUUID: 6c47ee27-a616-527f-a007-582ff470c05f
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - URL: https://euc1-red.pp.sgp.pvp.net/session-external/v1/session/create
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Region: EUW, Region Upper: EUW1
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Player Platform Edge URL: https://euc1-red.pp.sgp.pvp.net
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Headers: {"Authorization": "Bearer eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dDrK8zrgSVRcqNJhxAr661ZqKV1Gzw9f7vp2pLtsIuY42YcMPonvFwlZGbc5RcC1CekciOq3b4_ZYQer1wcgHazd0q9pWnDPqk2Y75yea45NeK70a5mO7m8lAOpLtF_VCHtNvKubokjcKmHztL0drpvHgBpf0yHalFcd94pGxAZEj1gGFAYXOc7LMXhaahFxZt-K39_pDF_TeOAgvwkIiPLY_4_02fQMJdT5OPvZQUm9_1XJwpf_BHTKzTz63Sot2zUHJPvXIBGZrfpuilq7fueWcof4WJwnUgSafNFrpqkThiP91VWpPLZ38v0RmuFFpEwvCNhC_cd-izs-wH3m4Q", "Accept": "application/json", "Content-Type": "application/json"}
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Body: {"claims": {"cname": "lcu"}, "product": "lol", "puuid": "6c47ee27-a616-527f-a007-582ff470c05f", "region": "EUW1"}
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Status: 200
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Headers: {"date": "Tue, 01 Jul 2025 19:39:01 GMT", "content-type": "application/json;charset=utf-8", "transfer-encoding": "chunked", "connection": "keep-alive", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "expires": "0", "pragma": "no-cache", "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers, accept-encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-riot-edge-trace-id": "82e18087-a5a6-43cb-96fe-5f7f366b965f", "x-xss-protection": "0", "content-encoding": "gzip", "access-control-allow-origin": "*", "access-control-allow-methods": "GET, PUT, DELETE, POST, OPTIONS", "access-control-allow-headers": "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range", "access-control-expose-headers": "Content-Length,Content-Range", "cf-cache-status": "DYNAMIC", "server": "cloudflare", "cf-ray": "95885df49b93716f-DUS"}
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Text: "eyJraWQiOiJzMSIsImFsZyI6IlJTMjU2In0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IJh_Nm0akO1fAjPR2DxIdyFNN6lPDiJFpC8O_ecG6Jzbb4CkDHId6PsOagNySxoeGhqBf7kuTV9sBBVwoVzlFtZnVS_ajOdP4LNa13qjEF3CfwH-IjGx7SdfR-rFncAdNrYfXJS3Upgx18FcQN2He10XmrlZgnDVDxh3BULO90LUrA7SJ6L1mOq0_6YDnLAMlbUKbkISbr9BNSkOlPqYgkNgq24ehF5o2N7Jgr-Kn1xhP67IjrrYn0u2ofJBA3h7wytNRh7lcsKhllFHn78voT-9MH8kYrcBzq15csb0daZkHeQ20BuBBdPMp8ZI9ESouLTbBBzwdan20tE4Bf2VkQ"
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Successfully retrieved all tokens for skin retrieval
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Making request to https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v2/inventoriesWithLoyalty for skin data
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Successfully retrieved skin data response
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Found items in skin data response: {'CHAMPION_SKIN': [{'itemId': 98051, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '805efa7f-f186-4f68-a83f-f0e4df12afd1', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '47c09d19-79f1-4ee0-b796-473c1a8307d5', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 80004, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200626T023618.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220705T170719.000Z', 'entitlementId': '30336130-3933-3730-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '2d24cf1a-ee4f-4dab-bdba-fa544fe05101', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 107015, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200708T213553.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200708T221700.000Z', 'entitlementId': '30336130-3935-3739-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '925296d6-3bc4-4b62-88c2-33ff8ee0e0de', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 166020, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '44fbe6e6-3871-4c5b-b7d6-80593083f1aa', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c309178c-f9e3-46c1-b073-4d5175dd62c5', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 21001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20211121T021328.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30336130-3533-3237-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0bcc9fd8-6b2f-4aea-bc67-c1264b2e2a3c', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 107019, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200708T214011.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200708T225741.000Z', 'entitlementId': '30336130-3933-3133-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '2a97d417-9ad3-48e8-a9c6-fcb520579e89', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 21002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200410T195746.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30336133-3331-3231-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'f67dbb57-0368-4234-8952-85101e6ef42d', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 107023, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200905T140912.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200905T221849.000Z', 'entitlementId': '30336130-3732-3630-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '13964487-2705-49fc-b937-1d792b5e8aa8', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 58008, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20201120T145639.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30336130-3937-3163-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b5fe5d2d-6838-45da-8a4a-10222b85895b', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 234010, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250530T175019.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '203b5c69-3590-4b00-9c84-213caa0d71d1', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '23253a42-ca5a-4ac5-a808-fc30739e96f0', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 64027, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200625T232643.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200626T011822.000Z', 'entitlementId': '30336130-3934-6264-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '8bf804a5-8393-4825-9383-1f4d83616ecc', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 84001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210712T221656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210712T230058.000Z', 'entitlementId': '30336130-3933-6234-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '328fc0ae-9f6d-44c8-972d-833a061bb74a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 84009, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210826T154754.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220711T233411.000Z', 'entitlementId': '30336130-3939-3439-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'd9fb0ced-9dda-46be-b53f-c1fbf26aabd7', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 105002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220623T180747.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20221026T202318.000Z', 'entitlementId': '30336130-3936-3037-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'a147e016-76b2-418e-a42a-3dd233d98975', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 105005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200617T135758.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200617T144225.000Z', 'entitlementId': '30336130-3936-3465-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'aeacc9f2-4e75-4d74-b420-7144d9d5d85f', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 84015, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200716T223220.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200716T231215.000Z', 'entitlementId': '30336130-3934-3338-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '60242bab-0053-4f52-a7de-525c6e96089b', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 58033, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250530T175040.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'f3a1d3c1-c2e0-43b3-9980-d35cf7326c27', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '07cbefcd-9797-4ea8-9995-ff0596e77ed7', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 62002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210701T131847.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20240901T202416.000Z', 'entitlementId': '30336130-3937-3563-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b77d48e3-db26-4c2c-8b31-b78638cbd0ac', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 14003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220430T122301.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220501T125316.000Z', 'entitlementId': '30336130-3733-6333-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '277e69dc-f3b0-4dbc-af88-ec1a83df1f25', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 78005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20230909T114841.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '24e98aa7-2cf3-4a16-8e6c-36006d477d61', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '1ef8a11d-6489-4022-ba83-452b131da19f', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 90038, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '0ddda8bc-e2ef-4bee-b913-7e3e6d57eb50', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0a45c80e-6c37-4cbd-bf0f-c615cac05c76', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 238011, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220818T145121.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220911T172442.000Z', 'entitlementId': 'a95cbdd2-0db3-490e-a228-03bfbc89120a', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '7cad0716-5a59-41f6-85fa-c10cae6b76c2', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '31c2790a-16bd-4c68-b10f-fa563fdbd4e7', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '996ec966-bfde-4151-8d1b-5b4101219876', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 14014, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220430T122315.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220430T141335.000Z', 'entitlementId': '30336130-3731-6636-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '135248bd-bfd0-4864-91ea-7026697e66e3', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 37056, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20241012T041048.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '3e3f3358-9292-4c1a-aebc-94d9b8c843ee', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'd3d6f524-933b-48b1-90da-d5f42db9bf18', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 8001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20230522T211055.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20230815T173549.000Z', 'entitlementId': '7890df04-43dc-4d7c-80d2-02e828c826e9', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '3da824a4-1f14-4a3d-ac3a-bd25b4370afe', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 37057, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20241012T041048.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '9368386c-7d32-47cc-8c00-ad33e1d81c14', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'cc7dedc5-cc93-460c-84af-26ad26f1e95a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 37058, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20241012T041048.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '1e56df0d-b1ca-4373-abbb-23fa8edf8fb2', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '3d4bac4a-5451-4396-9543-cee2623a3ea1', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 24003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200726T125553.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20240411T231624.000Z', 'entitlementId': '30336130-3937-6437-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'ba88c164-738c-40b1-b118-7a2ce93209c6', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 37059, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20241012T041048.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'f8286a6f-63ee-4bc9-9c7c-d6ec7b9e98cc', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c1c96856-eb6c-41d8-98eb-f57457a2a6a4', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 126024, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20211117T151205.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30336130-3732-6132-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '1c0cefc5-5d3e-408c-96ce-0b60806143ec', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 157003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20221007T124004.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20221007T124434.000Z', 'entitlementId': '6da861f7-8b83-447f-af71-78aa31915abf', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'ba7e7e39-3bd8-490e-947b-c973d86eb297', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19018, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '5703367f-cb96-4300-9557-3db59233cb0d', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '861dd57b-e87a-4006-84c5-52e3e16e1ffe', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '97b82bef-3b85-4a3c-8df1-74000c02991f', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'fe896ef1-2a0c-49d4-a1f3-a8c8a9c14176', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4045, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T092837.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'b9796018-d3cb-4a16-9e18-b22a73a28941', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '789b9546-e20b-4c29-a77e-d0d9d2c0b1a0', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4046, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T092837.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '638e730f-cee7-4d39-980e-d88076c4dda9', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '25121203-8270-48a5-b9fe-ebbe287d17cb', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4047, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T092837.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '5c99aeef-9438-46d9-bd43-2f540475bb57', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '40012ef7-ae2c-4b13-b044-618f6f9ac327', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19023, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'deb6b1ab-b7cc-4daa-9637-41785adead8a', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '6f363d48-b85d-4bd0-a9fe-d2d58a1d4674', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4048, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T092837.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '5476baaa-1469-420a-942b-1cc2c9b70e11', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0d3c52e0-8113-4152-83d6-a1369eecf260', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4049, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T092837.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'dd05b66c-c5ba-40a9-a18e-fe4504ea8c1b', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0e09fbee-7c7f-48d2-b138-71205d9a771d', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19025, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'ad51b750-2d66-42ae-8af0-4466a8eed4a1', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'd8428f19-daeb-45c5-ad39-db2be9d111a1', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200613T142057.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200613T152939.000Z', 'entitlementId': '30336130-3938-3133-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'bdf82d63-e04b-4468-bec6-27c01dab8630', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 35033, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20230822T195706.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '6ff84cc7-50e9-4986-9930-336f0a266393', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '64006c9a-1881-4d3f-8a31-d2ddcb06ceb9', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 23004, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20211128T012651.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20211128T152741.000Z', 'entitlementId': '30336130-3938-6364-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c1581b9d-96bf-468a-a45d-2887f670567e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 103005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240417T205555.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '61a48b45-b487-4dde-bcfa-9ae42a11bae5', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '2076567d-d90b-46d3-8799-3ebab3e365ce', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220619T172214.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220619T175326.000Z', 'entitlementId': '30336130-3933-6638-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '5a260596-10b2-4417-a3dd-e420c636c25a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29021, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '8f9474dd-0fe5-49f5-8492-c4de092485db', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '2717bff4-051d-4cd7-8280-c15cafb51e26', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 7005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200802T180447.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20230829T201434.000Z', 'entitlementId': '30336130-3937-3961-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b7faf0b7-26d0-4daa-a594-1a8a97478408', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29023, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '636bb0b7-31e4-429d-aa7e-ebba39c42535', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '8f0562cb-7c6b-4f99-91f0-db41ccb9221b', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29024, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '1972488a-a799-4acc-80d2-9bacef80a48b', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '48e5636f-6d6a-4372-b594-6d73cc56380e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 92003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200530T173233.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200530T174055.000Z', 'entitlementId': '30336133-3333-6333-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'fc0faa88-cb03-4db3-8231-2a5e88cf676a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 236001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200429T192422.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200430T104605.000Z', 'entitlementId': '30336130-3939-6336-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'e2da99c8-d4bc-400b-a385-f270aa51dac5', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55012, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200611T131830.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200611T154603.000Z', 'entitlementId': '30336130-3935-6335-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '92df77a2-5f01-470a-a261-35e793c71c12', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55015, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200611T132036.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200611T134123.000Z', 'entitlementId': '30336130-3934-3761-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '8658343b-b56d-4ce8-bee6-6983685127c0', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 103014, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20201116T211614.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20201116T212257.000Z', 'entitlementId': '30336130-3939-3062-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c4e28e05-5d3b-410b-95e0-fbd52d5f30ba', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 236008, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220504T123129.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220504T145604.000Z', 'entitlementId': '30336130-3938-3864-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'be840427-eabf-495e-a8ca-903a796b2eee', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55019, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200611T132039.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200611T145834.000Z', 'entitlementId': '30336130-3733-3266-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '20a675c4-265b-4b0f-be2d-ff1d6e470a0e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 81005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220922T150453.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220923T132932.000Z', 'entitlementId': '0db08953-f549-4093-810c-4dd6207f314e', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'a0638a12-1a65-45e8-9c33-53e85e60c268', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55021, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200725T003804.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200725T012415.000Z', 'entitlementId': '30336130-3936-6439-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b4b349e1-ca8c-4d24-a029-85187cbed1b4', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 39026, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220716T003418.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20230522T212104.000Z', 'entitlementId': '30336130-3936-3938-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'af4078ba-27b4-4bde-89df-52b6add727de', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 166001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220511T163201.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220714T190013.000Z', 'entitlementId': '30336130-3732-6538-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '1f258038-4725-42ac-9cce-39a1602aa09e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 22005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200726T125619.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20250402T073510.000Z', 'entitlementId': '30336130-3939-3837-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'e1ac28d8-57b3-47eb-b8fb-c2b82e99e973', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 92023, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210629T001935.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210630T234902.000Z', 'entitlementId': '30336130-3733-3733-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '241d116b-aeb3-4370-9394-d9806b387dab', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 86013, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250530T175101.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'c678d252-f079-46e6-b3af-bf5f2f756008', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '029573f2-b5eb-4cc4-bf6b-eae7e0e310a9', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}]}
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Found CHAMPION_SKIN in items: [{'itemId': 98051, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '805efa7f-f186-4f68-a83f-f0e4df12afd1', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '47c09d19-79f1-4ee0-b796-473c1a8307d5', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 80004, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200626T023618.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220705T170719.000Z', 'entitlementId': '30336130-3933-3730-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '2d24cf1a-ee4f-4dab-bdba-fa544fe05101', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 107015, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200708T213553.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200708T221700.000Z', 'entitlementId': '30336130-3935-3739-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '925296d6-3bc4-4b62-88c2-33ff8ee0e0de', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 166020, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '44fbe6e6-3871-4c5b-b7d6-80593083f1aa', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c309178c-f9e3-46c1-b073-4d5175dd62c5', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 21001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20211121T021328.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30336130-3533-3237-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0bcc9fd8-6b2f-4aea-bc67-c1264b2e2a3c', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 107019, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200708T214011.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200708T225741.000Z', 'entitlementId': '30336130-3933-3133-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '2a97d417-9ad3-48e8-a9c6-fcb520579e89', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 21002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200410T195746.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30336133-3331-3231-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'f67dbb57-0368-4234-8952-85101e6ef42d', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 107023, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200905T140912.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200905T221849.000Z', 'entitlementId': '30336130-3732-3630-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '13964487-2705-49fc-b937-1d792b5e8aa8', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 58008, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20201120T145639.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30336130-3937-3163-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b5fe5d2d-6838-45da-8a4a-10222b85895b', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 234010, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250530T175019.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '203b5c69-3590-4b00-9c84-213caa0d71d1', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '23253a42-ca5a-4ac5-a808-fc30739e96f0', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 64027, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200625T232643.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200626T011822.000Z', 'entitlementId': '30336130-3934-6264-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '8bf804a5-8393-4825-9383-1f4d83616ecc', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 84001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210712T221656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210712T230058.000Z', 'entitlementId': '30336130-3933-6234-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '328fc0ae-9f6d-44c8-972d-833a061bb74a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 84009, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210826T154754.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220711T233411.000Z', 'entitlementId': '30336130-3939-3439-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'd9fb0ced-9dda-46be-b53f-c1fbf26aabd7', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 105002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220623T180747.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20221026T202318.000Z', 'entitlementId': '30336130-3936-3037-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'a147e016-76b2-418e-a42a-3dd233d98975', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 105005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200617T135758.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200617T144225.000Z', 'entitlementId': '30336130-3936-3465-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'aeacc9f2-4e75-4d74-b420-7144d9d5d85f', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 84015, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200716T223220.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200716T231215.000Z', 'entitlementId': '30336130-3934-3338-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '60242bab-0053-4f52-a7de-525c6e96089b', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 58033, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250530T175040.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'f3a1d3c1-c2e0-43b3-9980-d35cf7326c27', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '07cbefcd-9797-4ea8-9995-ff0596e77ed7', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 62002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210701T131847.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20240901T202416.000Z', 'entitlementId': '30336130-3937-3563-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b77d48e3-db26-4c2c-8b31-b78638cbd0ac', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 14003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220430T122301.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220501T125316.000Z', 'entitlementId': '30336130-3733-6333-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '277e69dc-f3b0-4dbc-af88-ec1a83df1f25', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 78005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20230909T114841.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '24e98aa7-2cf3-4a16-8e6c-36006d477d61', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '1ef8a11d-6489-4022-ba83-452b131da19f', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 90038, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '0ddda8bc-e2ef-4bee-b913-7e3e6d57eb50', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0a45c80e-6c37-4cbd-bf0f-c615cac05c76', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 238011, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220818T145121.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220911T172442.000Z', 'entitlementId': 'a95cbdd2-0db3-490e-a228-03bfbc89120a', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '7cad0716-5a59-41f6-85fa-c10cae6b76c2', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '31c2790a-16bd-4c68-b10f-fa563fdbd4e7', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '996ec966-bfde-4151-8d1b-5b4101219876', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 14014, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220430T122315.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220430T141335.000Z', 'entitlementId': '30336130-3731-6636-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '135248bd-bfd0-4864-91ea-7026697e66e3', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 37056, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20241012T041048.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '3e3f3358-9292-4c1a-aebc-94d9b8c843ee', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'd3d6f524-933b-48b1-90da-d5f42db9bf18', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 8001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20230522T211055.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20230815T173549.000Z', 'entitlementId': '7890df04-43dc-4d7c-80d2-02e828c826e9', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '3da824a4-1f14-4a3d-ac3a-bd25b4370afe', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 37057, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20241012T041048.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '9368386c-7d32-47cc-8c00-ad33e1d81c14', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'cc7dedc5-cc93-460c-84af-26ad26f1e95a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 37058, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20241012T041048.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '1e56df0d-b1ca-4373-abbb-23fa8edf8fb2', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '3d4bac4a-5451-4396-9543-cee2623a3ea1', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 24003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200726T125553.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20240411T231624.000Z', 'entitlementId': '30336130-3937-6437-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'ba88c164-738c-40b1-b118-7a2ce93209c6', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 37059, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20241012T041048.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'f8286a6f-63ee-4bc9-9c7c-d6ec7b9e98cc', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c1c96856-eb6c-41d8-98eb-f57457a2a6a4', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 126024, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20211117T151205.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30336130-3732-6132-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '1c0cefc5-5d3e-408c-96ce-0b60806143ec', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 157003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20221007T124004.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20221007T124434.000Z', 'entitlementId': '6da861f7-8b83-447f-af71-78aa31915abf', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'ba7e7e39-3bd8-490e-947b-c973d86eb297', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19018, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '5703367f-cb96-4300-9557-3db59233cb0d', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '861dd57b-e87a-4006-84c5-52e3e16e1ffe', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '97b82bef-3b85-4a3c-8df1-74000c02991f', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'fe896ef1-2a0c-49d4-a1f3-a8c8a9c14176', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4045, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T092837.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'b9796018-d3cb-4a16-9e18-b22a73a28941', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '789b9546-e20b-4c29-a77e-d0d9d2c0b1a0', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4046, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T092837.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '638e730f-cee7-4d39-980e-d88076c4dda9', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '25121203-8270-48a5-b9fe-ebbe287d17cb', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4047, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T092837.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '5c99aeef-9438-46d9-bd43-2f540475bb57', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '40012ef7-ae2c-4b13-b044-618f6f9ac327', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19023, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'deb6b1ab-b7cc-4daa-9637-41785adead8a', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '6f363d48-b85d-4bd0-a9fe-d2d58a1d4674', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4048, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T092837.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '5476baaa-1469-420a-942b-1cc2c9b70e11', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0d3c52e0-8113-4152-83d6-a1369eecf260', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4049, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T092837.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'dd05b66c-c5ba-40a9-a18e-fe4504ea8c1b', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0e09fbee-7c7f-48d2-b138-71205d9a771d', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19025, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'ad51b750-2d66-42ae-8af0-4466a8eed4a1', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'd8428f19-daeb-45c5-ad39-db2be9d111a1', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200613T142057.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200613T152939.000Z', 'entitlementId': '30336130-3938-3133-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'bdf82d63-e04b-4468-bec6-27c01dab8630', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 35033, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20230822T195706.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '6ff84cc7-50e9-4986-9930-336f0a266393', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '64006c9a-1881-4d3f-8a31-d2ddcb06ceb9', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 23004, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20211128T012651.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20211128T152741.000Z', 'entitlementId': '30336130-3938-6364-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c1581b9d-96bf-468a-a45d-2887f670567e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 103005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240417T205555.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '61a48b45-b487-4dde-bcfa-9ae42a11bae5', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '2076567d-d90b-46d3-8799-3ebab3e365ce', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220619T172214.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220619T175326.000Z', 'entitlementId': '30336130-3933-6638-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '5a260596-10b2-4417-a3dd-e420c636c25a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29021, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '8f9474dd-0fe5-49f5-8492-c4de092485db', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '2717bff4-051d-4cd7-8280-c15cafb51e26', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 7005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200802T180447.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20230829T201434.000Z', 'entitlementId': '30336130-3937-3961-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b7faf0b7-26d0-4daa-a594-1a8a97478408', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29023, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '636bb0b7-31e4-429d-aa7e-ebba39c42535', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '8f0562cb-7c6b-4f99-91f0-db41ccb9221b', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29024, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '1972488a-a799-4acc-80d2-9bacef80a48b', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '48e5636f-6d6a-4372-b594-6d73cc56380e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 92003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200530T173233.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200530T174055.000Z', 'entitlementId': '30336133-3333-6333-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'fc0faa88-cb03-4db3-8231-2a5e88cf676a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 236001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200429T192422.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200430T104605.000Z', 'entitlementId': '30336130-3939-6336-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'e2da99c8-d4bc-400b-a385-f270aa51dac5', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55012, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200611T131830.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200611T154603.000Z', 'entitlementId': '30336130-3935-6335-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '92df77a2-5f01-470a-a261-35e793c71c12', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55015, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200611T132036.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200611T134123.000Z', 'entitlementId': '30336130-3934-3761-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '8658343b-b56d-4ce8-bee6-6983685127c0', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 103014, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20201116T211614.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20201116T212257.000Z', 'entitlementId': '30336130-3939-3062-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c4e28e05-5d3b-410b-95e0-fbd52d5f30ba', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 236008, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220504T123129.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220504T145604.000Z', 'entitlementId': '30336130-3938-3864-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'be840427-eabf-495e-a8ca-903a796b2eee', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55019, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200611T132039.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200611T145834.000Z', 'entitlementId': '30336130-3733-3266-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '20a675c4-265b-4b0f-be2d-ff1d6e470a0e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 81005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220922T150453.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220923T132932.000Z', 'entitlementId': '0db08953-f549-4093-810c-4dd6207f314e', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'a0638a12-1a65-45e8-9c33-53e85e60c268', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55021, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200725T003804.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200725T012415.000Z', 'entitlementId': '30336130-3936-6439-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b4b349e1-ca8c-4d24-a029-85187cbed1b4', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 39026, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220716T003418.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20230522T212104.000Z', 'entitlementId': '30336130-3936-3938-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'af4078ba-27b4-4bde-89df-52b6add727de', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 166001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220511T163201.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220714T190013.000Z', 'entitlementId': '30336130-3732-6538-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '1f258038-4725-42ac-9cce-39a1602aa09e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 22005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200726T125619.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20250402T073510.000Z', 'entitlementId': '30336130-3939-3837-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'e1ac28d8-57b3-47eb-b8fb-c2b82e99e973', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 92023, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210629T001935.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210630T234902.000Z', 'entitlementId': '30336130-3733-3733-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '241d116b-aeb3-4370-9394-d9806b387dab', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 86013, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250530T175101.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'c678d252-f079-46e6-b3af-bf5f2f756008', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '029573f2-b5eb-4cc4-bf6b-eae7e0e310a9', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}]
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] CHAMPION_SKIN is a list with 64 items
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Loaded 1941 skin entries from JSON database
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Loaded skin database with 1941 entries
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 98051
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 98051 as: skin - Three Honors Shen
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 80004
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 80004 as: skin - Full Metal Pantheon
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 107015
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 107015 as: skin - Pretty Kitty Rengar
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 166020
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 166020 as: skin - Three Honors Akshan
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 21001
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 21001 as: skin - Cowgirl Miss Fortune
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 107019
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 107019 as: chroma - Pretty Kitty Rengar (Chroma)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 21002
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 21002 as: skin - Waterloo Miss Fortune
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 107023
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 107023 as: skin - Guardian of the Sands Rengar
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 58008
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 58008 as: skin - SKT T1 Renekton
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 234010
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 234010 as: skin - Dissonance of Pentakill Viego
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 64027
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 64027 as: skin - Nightbringer Lee Sin
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 84001
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 84001 as: skin - Stinger Akali
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 84009
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 84009 as: skin - K/DA Akali
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 105002
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 105002 as: skin - Tundra Fizz
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 105005
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 105005 as: chroma - Fizz (Chroma)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 84015
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 84015 as: skin - True Damage Akali
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 58033
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 58033 as: skin - Dawnbringer Renekton
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 62002
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 62002 as: skin - General Wukong
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 14003
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 14003 as: skin - Lumberjack Sion
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 78005
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 78005 as: skin - Battle Regalia Poppy
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 90038
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 90038 as: skin - Three Honors Malzahar
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 238011
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 238011 as: skin - Death Sworn Zed
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19001
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 19001 as: skin - Grey Warwick
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 14014
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 14014 as: skin - Worldbreaker Sion
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 37056
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 37056 as: skin - Victorious Sona
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 8001
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 8001 as: skin - Count Vladimir
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 37057
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 37057 as: chroma - Victorious Sona (Bronze)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 37058
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 37058 as: chroma - Victorious Sona (Silver)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 24003
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 24003 as: skin - Angler Jax
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 37059
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 37059 as: chroma - Victorious Sona (Gold)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 126024
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 126024 as: skin - Arcane Inventor Jayce
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 157003
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 157003 as: skin - Blood Moon Yasuo
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19018
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 19018 as: chroma - Grey Warwick (Chroma)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29003
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 29003 as: skin - Medieval Twitch
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4045
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 4045 as: unknown - Unknown Skin (4045)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4046
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 4046 as: unknown - Unknown Skin (4046)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4047
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 4047 as: unknown - Unknown Skin (4047)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19023
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 19023 as: chroma - Grey Warwick (Chroma)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4048
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 4048 as: unknown - Unknown Skin (4048)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4049
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 4049 as: unknown - Unknown Skin (4049)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19025
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 19025 as: chroma - Grey Warwick (Chroma)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55001
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 55001 as: skin - Mercenary Katarina
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 35033
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 35033 as: skin - Winterblessed Shaco
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 23004
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 23004 as: skin - Demonblade Tryndamere
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 103005
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 103005 as: skin - Challenger Ahri
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55005
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 55005 as: skin - High Command Katarina
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29021
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 29021 as: chroma - Medieval Twitch (Chroma)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 7005
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 7005 as: skin - Elderwood LeBlanc
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29023
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 29023 as: chroma - Medieval Twitch (Chroma)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29024
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 29024 as: chroma - Medieval Twitch (Chroma)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 92003
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 92003 as: skin - Battle Bunny Riven
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 236001
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 236001 as: skin - Hired Gun Lucian
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55012
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 55012 as: skin - Battle Academia Katarina
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55015
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 55015 as: chroma - Battle Academia Katarina (Chroma)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 103014
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 103014 as: skin - Star Guardian Ahri
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 236008
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 236008 as: skin - High Noon Lucian
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55019
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 55019 as: chroma - Battle Academia Katarina (Chroma)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 81005
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 81005 as: skin - Pulsefire Ezreal
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55021
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 55021 as: skin - Blood Moon Katarina
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 39026
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 39026 as: skin - Sentinel Irelia
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 166001
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 166001 as: skin - Cyber Pop Akshan
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 22005
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 22005 as: skin - Amethyst Ashe
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 92023
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 92023 as: skin - Spirit Blossom Riven
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 86013
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 86013 as: skin - God-King Garen
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Extracted 64 skins from response
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 98051
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 98051 as: skin - Three Honors Shen
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 80004
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 80004 as: skin - Full Metal Pantheon
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 107015
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 107015 as: skin - Pretty Kitty Rengar
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 166020
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 166020 as: skin - Three Honors Akshan
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 21001
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 21001 as: skin - Cowgirl Miss Fortune
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 107019
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 107019 as: chroma - Pretty Kitty Rengar (Chroma)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 21002
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 21002 as: skin - Waterloo Miss Fortune
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 107023
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 107023 as: skin - Guardian of the Sands Rengar
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 58008
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 58008 as: skin - SKT T1 Renekton
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 234010
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 234010 as: skin - Dissonance of Pentakill Viego
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 64027
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 64027 as: skin - Nightbringer Lee Sin
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 84001
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 84001 as: skin - Stinger Akali
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 84009
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 84009 as: skin - K/DA Akali
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 105002
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 105002 as: skin - Tundra Fizz
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 105005
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 105005 as: chroma - Fizz (Chroma)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 84015
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 84015 as: skin - True Damage Akali
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 58033
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 58033 as: skin - Dawnbringer Renekton
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 62002
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 62002 as: skin - General Wukong
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 14003
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 14003 as: skin - Lumberjack Sion
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 78005
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 78005 as: skin - Battle Regalia Poppy
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 90038
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 90038 as: skin - Three Honors Malzahar
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 238011
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 238011 as: skin - Death Sworn Zed
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19001
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 19001 as: skin - Grey Warwick
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 14014
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 14014 as: skin - Worldbreaker Sion
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 37056
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 37056 as: skin - Victorious Sona
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 8001
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 8001 as: skin - Count Vladimir
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 37057
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 37057 as: chroma - Victorious Sona (Bronze)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 37058
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 37058 as: chroma - Victorious Sona (Silver)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 24003
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 24003 as: skin - Angler Jax
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 37059
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 37059 as: chroma - Victorious Sona (Gold)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 126024
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 126024 as: skin - Arcane Inventor Jayce
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 157003
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 157003 as: skin - Blood Moon Yasuo
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19018
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 19018 as: chroma - Grey Warwick (Chroma)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29003
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 29003 as: skin - Medieval Twitch
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4045
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 4045 as: unknown - Unknown Skin (4045)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4046
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 4046 as: unknown - Unknown Skin (4046)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4047
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 4047 as: unknown - Unknown Skin (4047)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19023
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 19023 as: chroma - Grey Warwick (Chroma)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4048
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 4048 as: unknown - Unknown Skin (4048)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4049
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 4049 as: unknown - Unknown Skin (4049)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19025
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 19025 as: chroma - Grey Warwick (Chroma)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55001
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 55001 as: skin - Mercenary Katarina
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 35033
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 35033 as: skin - Winterblessed Shaco
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 23004
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 23004 as: skin - Demonblade Tryndamere
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 103005
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 103005 as: skin - Challenger Ahri
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55005
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 55005 as: skin - High Command Katarina
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29021
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 29021 as: chroma - Medieval Twitch (Chroma)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 7005
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 7005 as: skin - Elderwood LeBlanc
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29023
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 29023 as: chroma - Medieval Twitch (Chroma)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29024
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 29024 as: chroma - Medieval Twitch (Chroma)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 92003
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 92003 as: skin - Battle Bunny Riven
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 236001
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 236001 as: skin - Hired Gun Lucian
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55012
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 55012 as: skin - Battle Academia Katarina
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55015
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 55015 as: chroma - Battle Academia Katarina (Chroma)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 103014
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 103014 as: skin - Star Guardian Ahri
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 236008
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 236008 as: skin - High Noon Lucian
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55019
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 55019 as: chroma - Battle Academia Katarina (Chroma)
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 81005
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 81005 as: skin - Pulsefire Ezreal
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55021
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 55021 as: skin - Blood Moon Katarina
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 39026
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 39026 as: skin - Sentinel Irelia
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 166001
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 166001 as: skin - Cyber Pop Akshan
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 22005
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 22005 as: skin - Amethyst Ashe
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 92023
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 92023 as: skin - Spirit Blossom Riven
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 86013
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Categorized skin 86013 as: skin - God-King Garen
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Saving 64 skins for konjisenpaii
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Got skin data for konjisenpaii
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Checking penalties for konjisenpaii
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Checking penalties for None using URL: https://euw-red.lol.sgp.pvp.net/leaverbuster-ledge/restrictionInfo
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Penalty check response: {'puuid': '6c47ee27-a616-527f-a007-582ff470c05f', 'rankedRestrictionEntryDto': {'puuid': '6c47ee27-a616-527f-a007-582ff470c05f', 'version': 0, 'restrictedGamesRemaining': 0, 'restrictedGamesOriginal': 0, 'rankedRestrictionPenaltyId': '8918c0d6-0ddc-4f04-9004-8b56b56dc343', 'punishmentIncurredGameId': 0, 'punishmentIncurredTimeMillis': 0, 'rankedRestrictionAckNeeded': False, 'penaltyOrigin': 'UNKNOWN'}, 'leaverBusterEntryDto': {'puuid': '6c47ee27-a616-527f-a007-582ff470c05f', 'version': 949, 'tainted': True, 'preLockoutAckNeeded': False, 'onLockoutAckNeeded': False, 'leaverScore': 0, 'leaverLevel': 0, 'punishedGamesRemaining': 0, 'currentPunishmentStep': 0, 'leaverPenalty': {'puuid': '6c47ee27-a616-527f-a007-582ff470c05f', 'hasActivePenalty': False, 'delayTime': 0, 'queueLockoutTimerExpiryUtcMillis': 0, 'punishmentTimerType': 'NO_PENALTY', 'rankRestrictedGamesRemaining': 0, 'rankRestrictedTimerExpiryUtcMillis': 0, 'rankRestricted': False}, 'warnSentMillis': 1585675593168, 'warnAckedMillis': 1585746326260, 'lastUpdatedMillis': 1749896594496, 'totalPunishedGamesPlayed': 12, 'lastBustedGameId': 7094866894, 'lastBustedTimeMillis': 1724870816608, 'lastPunishmentIncurredGameId': 7094866894, 'lastPunishmentIncurredTimeMillis': 1724870816608, 'processedGameIdHistoryString': '7417264658,7417350390,7417420378,7417479305,7417520673,7417571839,7417592277,7417612125,7417625186,7417639122,7417988841,7418034703,7418091241,7418121397,7431748234'}}
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] No active penalty
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Got penalty info for konjisenpaii: 0 minutes, 0 games
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Checking rank for CornyTalonAbuser#EUW
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Using region code EUW for rank info retrieval
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[90m@←[0m←[97m] get_rank_info called with region_code: EUW, type: <class 'str'>
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[90m@←[0m←[97m] Getting rank info for CornyTalonAbuser#EUW in region EUW
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[91m-←[0m←[97m] Error decoding JWT: module 'jwt' has no attribute 'decode'
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Found country from userinfo: unknown
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[92m$←[0m←[97m] Current rank data response: {'queues': [{'queueType': 'RANKED_SOLO_5x5', 'provisionalGameThreshold': 5, 'tier': 'DIAMOND', 'rank': 'III', 'leaguePoints': 35, 'cumulativeLp': 2535, 'wins': 72, 'losses': 29, 'provisionalGamesRemaining': 0, 'highestTier': 'DIAMOND', 'highestRank': 'III', 'previousSeasonEndTier': 'PLATINUM', 'previousSeasonEndRank': 'II', 'previousSeasonHighestTier': 'PLATINUM', 'previousSeasonHighestRank': 'II', 'previousSeasonAchievedTier': 'PLATINUM', 'previousSeasonAchievedRank': 'II', 'ratedRating': 0, 'warnings': {'apexDaysUntilDecay': 4, 'daysUntilDecay': 4}, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_FLEX_SR', 'provisionalGameThreshold': 5, 'tier': 'IRON', 'rank': 'IV', 'leaguePoints': 76, 'cumulativeLp': 76, 'wins': 1, 'losses': 0, 'provisionalGamesRemaining': 4, 'highestTier': 'IRON', 'highestRank': 'IV', 'previousSeasonHighestTier': 'IRON', 'previousSeasonHighestRank': 'IV', 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_TFT', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_TFT_TURBO', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_TFT_DOUBLE_UP', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}], 'highestPreviousSeasonEndTier': 'PLATINUM', 'highestPreviousSeasonEndRank': 'II', 'highestPreviousSeasonAchievedTier': 'PLATINUM', 'highestPreviousSeasonAchievedRank': 'II', 'earnedRegaliaRewardIds': [], 'splitsProgress': {}, 'seasons': {'RANKED_TFT': {'currentSeasonId': 29, 'currentSeasonEnd': 1753848001000, 'nextSeasonStart': 0}, 'RANKED_TFT_TURBO': {'currentSeasonId': 29, 'currentSeasonEnd': 1753848001000, 'nextSeasonStart': 0}, 'RANKED_FLEX_SR': {'currentSeasonId': 19, 'currentSeasonEnd': 1756249199000, 'nextSeasonStart': 0}, 'RANKED_TFT_DOUBLE_UP': {'currentSeasonId': 29, 'currentSeasonEnd': 1753848001000, 'nextSeasonStart': 0}, 'RANKED_SOLO_5x5': {'currentSeasonId': 19, 'currentSeasonEnd': 1756249199000, 'nextSeasonStart': 0}}, 'jwt': '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'}
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[90m@←[0m←[97m] scrape_account_info called with region_code: EUW, type: <class 'str'>
←[0m←[97m[2025-07-01 21:39:00] [←[0m←[90m@←[0m←[97m] Scraping account info from: https://www.leagueofgraphs.com/summoner/euw/CornyTalonAbuser-EUW
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[90m@←[0m←[97m] Got HTML response length: 1469500
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Found 10 season tags
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Processing tag: S10 Bronze with tooltip: <itemname class='tagTitle brown'>S10 Bronze</itemname><br/><span class='tagDescription'><highlight>Ranked Solo/Duo</highlight><br/>This player reached Bronze I during Season 10. At the end of the season, this player was Bronze I.<br/><br/><highlight>Ranked Flex</highlight><br/>This player reached Iron I during Season 10. At the end of the season, this player was Iron I.</span>
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Processing tag: S11 Silver with tooltip: <itemname class='tagTitle brown'>S11 Silver</itemname><br/><span class='tagDescription'><highlight>Ranked Solo/Duo</highlight><br/>This player reached Silver II during Season 11. At the end of the season, this player was Silver II.<br/></span>
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Processing tag: S12 Silver with tooltip: <itemname class='tagTitle brown'>S12 Silver</itemname><br/><span class='tagDescription'><highlight>Ranked Solo/Duo</highlight><br/>This player reached Silver I during Season 12. At the end of the season, this player was Silver II.<br/></span>
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Processing tag: S13 (Split 2) Bronze with tooltip: <itemname class='tagTitle brown'>S13 (Split 2) Bronze</itemname><br/><span class='tagDescription'><highlight>Ranked Solo/Duo</highlight><br/>This player reached Bronze I during Season 13 (Split 2). At the end of the season, this player was Bronze I.<br/></span>
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Processing tag: S14 (Split 1) Silver with tooltip: <itemname class='tagTitle brown'>S14 (Split 1) Silver</itemname><br/><span class='tagDescription'><highlight>Ranked Solo/Duo</highlight><br/>This player reached Silver II during Season 14 (Split 1). At the end of the season, this player was Silver II.<br/></span>
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Processing tag: S14 (Split 2) Gold with tooltip: <itemname class='tagTitle brown'>S14 (Split 2) Gold</itemname><br/><span class='tagDescription'><highlight>Ranked Solo/Duo</highlight><br/>This player reached Gold IV during Season 14 (Split 2). At the end of the season, this player was Gold IV.<br/></span>
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Processing tag: Bounces back with tooltip: <itemname class='tagTitle green'>Bounces back</itemname><br/><span class='tagDescription'>This player has a higher winrate when playing just after a defeat (+<span class='highlight'>7</span>%)</span>
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Processing tag: Snowballs with tooltip: <itemname class='tagTitle green'>Snowballs</itemname><br/><span class='tagDescription'>This player has a better winrate when 2/0, 3/1... at 10 minutes than the other <span class='highlight'>Diamond</span> players: +<span class='highlight'>16</span>%</span>
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Processing tag: Blue side lover with tooltip: <itemname class='tagTitle yellow'>Blue side lover</itemname><br/><span class='tagDescription'>This player has a far higher winrate when playing on the blue side (+<span class='highlight'>6</span>%)</span>
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Processing tag: Too confident with tooltip: <itemname class='tagTitle red'>Too confident</itemname><br/><span class='tagDescription'>This player's winrate is lower when playing just after a victory (-<span class='highlight'>6</span>%)</span>
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Found previous season rank from LeagueOfGraphs: {'tier': 'GOLD', 'division': 'IV'}
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Stored complete season history with 6 entries
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Processed rank info: {'solo': {'tier': 'DIAMOND', 'division': 'III', 'lp': 35, 'wins': 72, 'losses': 29, 'previous_tier': 'GOLD', 'previous_division': 'IV'}, 'flex': {'tier': 'IRON', 'division': 'IV', 'lp': 76, 'wins': 1, 'losses': 0, 'previous_tier': 'UNRANKED', 'previous_division': ''}, 'country': 'unknown', 'creation_region': 'EUW', 'season_history': [{'season': 14, 'split': '2', 'queue_type': 'SOLO_DUO', 'peak_tier': 'GOLD', 'peak_division': 'IV', 'end_tier': 'GOLD', 'end_division': 'IV'}, {'season': 14, 'split': '1', 'queue_type': 'SOLO_DUO', 'peak_tier': 'SILVER', 'peak_division': 'II', 'end_tier': 'SILVER', 'end_division': 'II'}, {'season': 13, 'split': '2', 'queue_type': 'SOLO_DUO', 'peak_tier': 'BRONZE', 'peak_division': 'I', 'end_tier': 'BRONZE', 'end_division': 'I'}, {'season': 12, 'split': None, 'queue_type': 'SOLO_DUO', 'peak_tier': 'SILVER', 'peak_division': 'I', 'end_tier': 'SILVER', 'end_division': 'II'}, {'season': 11, 'split': None, 'queue_type': 'SOLO_DUO', 'peak_tier': 'SILVER', 'peak_division': 'II', 'end_tier': 'SILVER', 'end_division': 'II'}, {'season': 10, 'split': None, 'queue_type': 'SOLO_DUO', 'peak_tier': 'BRONZE', 'peak_division': 'I', 'end_tier': 'BRONZE', 'end_division': 'I'}]}
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Got rank info for konjisenpaii
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Including 6 season history entries for konjisenpaii
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Saving rank info for konjisenpaii: Solo=DIAMOND III, Flex=IRON IV
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Successfully rechecked account: konjisenpaii
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Loading account konjisenpaii from database
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Account details: BE=6385, RP=0, Level=167
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Account info: created_at=2020-03-24 10:08:02, creation_region=EUW, country=swe
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Game info: game_name=CornyTalonAbuser, tag_line=EUW
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Checking JWT token for region information for konjisenpaii
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] JWT region debugging for konjisenpaii:
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m]   lol field: [{'cuid': ****************, 'cpid': 'EUW1', 'uid': ****************, 'uname': 'konjisenpaii', 'ptrid': None, 'pid': 'EUW1', 'state': 'ENABLED'}]
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m]   region field: {}
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m]   original_platform_id:
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m]   lol_region: [{'active': True, 'cpid': 'EUW1', 'cuid': ****************, 'lp': False, 'pid': 'EUW1', 'uid': ****************}]
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m]   dat field: {}
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Using lol_region[].cpid to determine region for konjisenpaii: EUW
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Found region in JWT for konjisenpaii: EUW
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Loaded 6 season history entries for konjisenpaii
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Using region=EUW, region_code=EUW for account konjisenpaii
←[0m←[97m[2025-07-01 21:39:01] [←[0m←[92m$←[0m←[97m] Loaded 1 accounts from database
←[0mDEBUG: PyQtBridge.handle_account_selection called with index: 0
DEBUG: Updated PyQtBridge.selected_account_index to: 0
DEBUG: Updated main_window.selected_account_index via set_selected_account_index
DEBUG: Found Victorious parent skin: 37056 for chroma: 37057
DEBUG: Generating HTML for 2 chromas
