Microsoft Windows [Version 10.0.19045.5965]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\Desktop\sources\League Acc Checker>py main.py
←[97m[2025-07-01 21:26:45] [←[0m←[92m$←[0m←[97m] Created DatabaseManagerWrapper with path accounts.db
←[0m←[97m[2025-07-01 21:26:45] [←[0m←[92m$←[0m←[97m] Wrapped db_manager with DatabaseManagerWrapper in AccountPool
←[0m←[97m[2025-07-01 21:26:45] [←[0m←[92m$←[0m←[97m] Loaded 1941 skin entries from JSON database
←[0mDEBUG: Created CustomWebEngineView
DEBUG: Created web view with custom context menu handling
Loaded 1941 skin entries from JSON database
DEBUG: Sharing champion database with 171 entries to PyQtBridge and AccountPool
←[97m[2025-07-01 21:26:47] [←[0m←[92m$←[0m←[97m] Loaded 0 accounts from database
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[92m$←[0m←[97m] Importing accounts from clipboard
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[92m$←[0m←[97m] Loading account konjisenpaii from database
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[92m$←[0m←[97m] Account details: BE=0, RP=0, Level=0
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[92m$←[0m←[97m] Account info: created_at=None, creation_region=None, country=None
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[92m$←[0m←[97m] Game info: game_name=None, tag_line=None
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[92m$←[0m←[97m] Loaded 0 season history entries for konjisenpaii
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[92m$←[0m←[97m] No region information found for konjisenpaii, leaving region as None
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[92m$←[0m←[97m] Using region=None, region_code=None for account konjisenpaii
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[92m$←[0m←[97m] Loaded 1 accounts from database
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[92m$←[0m←[97m] Successfully imported 1 accounts from clipboard
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[92m$←[0m←[97m] Loading account konjisenpaii from database
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[92m$←[0m←[97m] Account details: BE=0, RP=0, Level=0
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[92m$←[0m←[97m] Account info: created_at=None, creation_region=None, country=None
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[92m$←[0m←[97m] Game info: game_name=None, tag_line=None
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[92m$←[0m←[97m] Loaded 0 season history entries for konjisenpaii
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[92m$←[0m←[97m] No region information found for konjisenpaii, leaving region as None
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[92m$←[0m←[97m] Using region=None, region_code=None for account konjisenpaii
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[92m$←[0m←[97m] Loaded 1 accounts from database
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[92m$←[0m←[97m] Starting to recheck account: konjisenpaii
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[92m$←[0m←[97m] Initializing authenticator for account: konjisenpaii
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[92m$←[0m←[97m] Initializing authenticator for konjisenpaii with region None
←[0m←[97m[2025-07-01 21:27:26] [←[0m←[93m!←[0m←[97m] No region code provided for konjisenpaii, authentication may fail
←[0m←[97m[2025-07-01 21:27:27] [←[0m←[92m$←[0m←[97m] Authenticator initialized for konjisenpaii in region
←[0m←[97m[2025-07-01 21:27:27] [←[0m←[92m$←[0m←[97m] Authenticator initialized for konjisenpaii in region None
←[0m←[97m[2025-07-01 21:27:27] [←[0m←[92m$←[0m←[97m] No refresh token available for konjisenpaii
←[0m←[97m[2025-07-01 21:27:27] [←[0m←[92m$←[0m←[97m] Rechecking account: konjisenpaii
←[0m←[97m[2025-07-01 21:27:27] [←[0m←[92m$←[0m←[97m] Starting check for account konjisenpaii with region None
←[0m←[97m[2025-07-01 21:27:27] [←[0m←[92m$←[0m←[97m] Getting userinfo for konjisenpaii
←[0m←[97m[2025-07-01 21:27:27] [←[0m←[92m$←[0m←[97m] Got account info for konjisenpaii: {'username': 'konjisenpaii', 'password': 'majamys1', 'region': '', 'refresh_token': '', 'access_token': None, 'id_token': None, 'entitlements_token': None, 'userinfo': None, 'timestamp': 4251, 'puuid': None, 'account_id': None, 'game_name': None, 'tag_line': None, 'champion_count': 0, 'blue_essence': 0, 'riot_points': 0, 'summoner_level': 0, 'created_at': None, 'last_checked': '2025-07-01 19:27:26', 'penalty_minutes': 0, 'ranked_games_remaining': 0, 'is_banned': 0, 'ban_info': None, 'country': None, 'creation_region': None, 'solo_tier': 'UNRANKED', 'solo_division': '', 'solo_lp': 0, 'solo_wins': 0, 'solo_losses': 0, 'solo_previous_tier': 'UNRANKED', 'solo_previous_division': '', 'flex_tier': 'UNRANKED', 'flex_division': '', 'flex_lp': 0, 'flex_wins': 0, 'flex_losses': 0, 'flex_previous_tier': 'UNRANKED', 'flex_previous_division': '', 'chat_restrictions': None, 'is_glitch_account': 0, 'region_code': None}
←[0m←[97m[2025-07-01 21:27:27] [←[0m←[92m$←[0m←[97m] Using refresh token for konjisenpaii: no
←[0m←[97m[2025-07-01 21:27:27] [←[0m←[92m$←[0m←[97m] Falling back to password auth for konjisenpaii
←[0m←[97m[2025-07-01 21:27:27] [←[0m←[92m$←[0m←[97m] Solve captcha starting for konjisenpaii
←[0m←[97m[2025-07-01 21:27:27] [←[0m←[92m$←[0m←[97m] Initial post payload: {'clientId': 'lol', 'language': 'en_US', 'platform': 'windows', 'remember': False, 'riot_identity': {'state': 'auth'}, 'sdkVersion': '24.8.0.4145', 'type': 'auth'}
←[0m←[97m[2025-07-01 21:27:27] [←[0m←[92m$←[0m←[97m] Post response status: 200
←[0m←[97m[2025-07-01 21:27:27] [←[0m←[92m$←[0m←[97m] Got captcha challenge with sitekey: 019f1553-3845-481c-a6f5-5a60ccf6d830
←[0m←[97m[2025-07-01 21:27:27] [←[0m←[92m$←[0m←[97m] Starting captcha solver subprocess
←[0m←[97m[2025-07-01 21:27:32] [←[0m←[92m$←[0m←[97m] Captcha solver process completed
←[0m←[97m[2025-07-01 21:27:32] [←[0m←[92m$←[0m←[97m] Got captcha token: P1_eyJ0eXAiOiJKV1QiL
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] Extracted login token
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] Extracted authorization code
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] Successfully retrieved tokens for konjisenpaii
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] Solve captcha result: True
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] Extracted region from JWT lol_region[].cpid: EUW
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] Updating region from  to EUW based on JWT token
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] Using access token for userinfo: eyJraWQiOi...Dgp4kzUp_A
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] Access token length: 1233
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] Userinfo request URL: https://auth.riotgames.com/userinfo
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] Userinfo request headers: Authorization: Bearer eyJraWQiOi...Dgp4kzUp_A, Accept: application/json, Content-Type: application/json
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] Raw userinfo response (first 100 chars): eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwidHlwIjoidXNlcmluZm8rand0IiwiYWxnIjoiUlMyNTYifQ.eyJjb3VudHJ5Ijoic
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] Userinfo response length: 1954
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] Received JWT token for userinfo
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] JWT token has 3 parts
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] Successfully decoded JWT payload with 25 keys
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[93m!←[0m←[97m] Ban info in JWT payload: {
  "restrictions": []
}
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[93m!←[0m←[97m] Found 0 ban restrictions
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] Successfully decoded JWT payload with 25 keys
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] Successfully authenticated konjisenpaii with password
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] Got userinfo for konjisenpaii, decoding JWT
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] Getting tokens for konjisenpaii
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[91m-←[0m←[97m] Error parsing userinfo string in get_queue_token: Expecting value: line 1 column 1 (char 0)
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - URL: https://euc1-red.pp.sgp.pvp.net/login-queue/v2/login/products/lol/regions/EUW1
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Region: EUW, Region Upper: EUW1
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Player Platform Edge URL: https://euc1-red.pp.sgp.pvp.net
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Headers: {"Authorization": "Bearer eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwiYWxnIjoiUlMyNTYifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.UBy_j2WOlo2NGvSonQ3BSnSj2jIuHVZCmGEZnhusmu1ZQZmwuqhlW8hLaBRaep9bxyzTdxsxYDV0cAJB-ydGVPPRFK837mBVMyb9nKuZfT70V-rDwW1j-HLXkD___Dg4NxHBFDcy3PYLvCXj-a_fg5MMd7TNRMlS3hqrqdzo-FVT3uA6-KIA3rzTncBYuSw71jOy1bRFuVwkBYUozvkbjy-NMbs1Yq6ubz4Q2UhsUIB2Sp6A6n4i0TPEfy1VdsLlULzypMGZRR0JYVCjfOJwuYOStKFSr10qKNgU77KiAx-Z6qcC5Kl5vm8jkT9hfkTMLcOLEOfbRYhPDgp4kzUp_A", "Accept": "application/json", "Content-Type": "application/json"}
←[0m←[97m[2025-07-01 21:27:33] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Body: {"clientName": "lcu", "entitlements": "*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "userinfo": "eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwidHlwIjoidXNlcmluZm8rand0IiwiYWxnIjoiUlMyNTYifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PwJXbdFGFGlWyR76LA-hy9yKWnUaf7c42-R1nUYGknV46D86pnPagAxJ4U1KyO-U4f9yHd64tmHVVifkADaCgZzdQYwUHsvfHdvhvtJx5ldl-vYIs2uv_WdgFlE0RwtzCnnQ9VThQg_GmP3RuHIulJOLXL4T2Yq0Do0sg4TGVRx0xYXfq2VjIQoU092h6wgZ7Zb9KaqQDAErhEfDfuvid0aBBT8Ie5AluAQvz2dZ4IXaeTt9Usdb0yDikL2v0208Aw4u0vNaJPs0DGD3_Uq-Kbo-Nsu11LYM815SimKvFHEuRnlmEaepM8lbOBxJ3RRuo6DIrCovMETpX6K5IBMseQ"}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Status: 200
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Headers: {"date": "Tue, 01 Jul 2025 19:27:34 GMT", "content-type": "application/json;charset=utf-8", "transfer-encoding": "chunked", "connection": "keep-alive", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "expires": "0", "pragma": "no-cache", "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers, accept-encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-riot-edge-trace-id": "0473076f-1417-4285-8fa0-db6b59acf153", "x-xss-protection": "0", "content-encoding": "gzip", "access-control-allow-origin": "*", "access-control-allow-methods": "GET, PUT, DELETE, POST, OPTIONS", "access-control-allow-headers": "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range", "access-control-expose-headers": "Content-Length,Content-Range", "cf-cache-status": "DYNAMIC", "set-cookie": "__cf_bm=0DUt_CoTH_nMCsASvqCe_JvOqWmEuHL3r8SOlmH30pU-1751398054-*******-PwHnXF3Tbpgh1GWI3BH5XFAEFr2hAb372is.Xp3_4WmzYksdqYQOtqdJwHurhf1jWkeXyLMf_RKTnfDrfNbP9ddl_c16nUKxlaQJ1pW.FLQ; path=/; expires=Tue, 01-Jul-25 19:57:34 GMT; domain=.sgp.pvp.net; HttpOnly; Secure; SameSite=None", "server": "cloudflare", "cf-ray": "95884d32f9361623-DUS"}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Body: {"token": "eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fSQyjMCbeE_VNRGaxRR3ssP9tfkeMuCbXkgztffcCruewg92_nEEJwJVzB1BO1AVoZ-ngim99bGF1FRO6gqJMFG44QcYu9Ho9QZZdC24WAfxHsU43PWIdhAONrayYYoSY5tl-NzUQ0kMroW5DRoQk-HzkQfgG-1jH2DubQ-beb9TZv2m3nEEew9uvgMSiHHKTd2Fy23ypFcxwJe--sOMIt-BUwDhNMXBQAvmaRFdmzEkxzuBOTjCgtFqFHahQPGmQKVqSaAY6Sh9fZ3BmzEXCXBdt_cC9y6z6m0lu01Iu2u1K3qi_eGNLE3Q8RGEsmho_m7TNyOHdOX79gIBWY3gKw", "type": "LOGIN"}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[91m-←[0m←[97m] Error parsing userinfo string in get_session_token: Expecting value: line 1 column 1 (char 0)
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Got JWT token from queue_token
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Parsed access token, PUUID: 6c47ee27-a616-527f-a007-582ff470c05f
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - URL: https://euc1-red.pp.sgp.pvp.net/session-external/v1/session/create
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Region: EUW, Region Upper: EUW1
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Player Platform Edge URL: https://euc1-red.pp.sgp.pvp.net
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Headers: {"Authorization": "Bearer eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fSQyjMCbeE_VNRGaxRR3ssP9tfkeMuCbXkgztffcCruewg92_nEEJwJVzB1BO1AVoZ-ngim99bGF1FRO6gqJMFG44QcYu9Ho9QZZdC24WAfxHsU43PWIdhAONrayYYoSY5tl-NzUQ0kMroW5DRoQk-HzkQfgG-1jH2DubQ-beb9TZv2m3nEEew9uvgMSiHHKTd2Fy23ypFcxwJe--sOMIt-BUwDhNMXBQAvmaRFdmzEkxzuBOTjCgtFqFHahQPGmQKVqSaAY6Sh9fZ3BmzEXCXBdt_cC9y6z6m0lu01Iu2u1K3qi_eGNLE3Q8RGEsmho_m7TNyOHdOX79gIBWY3gKw", "Accept": "application/json", "Content-Type": "application/json"}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Body: {"claims": {"cname": "lcu"}, "product": "lol", "puuid": "6c47ee27-a616-527f-a007-582ff470c05f", "region": "EUW1"}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Status: 200
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Headers: {"date": "Tue, 01 Jul 2025 19:27:34 GMT", "content-type": "application/json;charset=utf-8", "transfer-encoding": "chunked", "connection": "keep-alive", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "expires": "0", "pragma": "no-cache", "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers, accept-encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-riot-edge-trace-id": "6396bd70-71bb-46c8-82fb-7da6fe2d3298", "x-xss-protection": "0", "content-encoding": "gzip", "access-control-allow-origin": "*", "access-control-allow-methods": "GET, PUT, DELETE, POST, OPTIONS", "access-control-allow-headers": "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range", "access-control-expose-headers": "Content-Length,Content-Range", "cf-cache-status": "DYNAMIC", "server": "cloudflare", "cf-ray": "95884d33597d1623-DUS"}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Text: "eyJraWQiOiJzMSIsImFsZyI6IlJTMjU2In0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jRY0NaSf77l-KbB8b_4e1L3VKS_q5sF4d8u4kq9GsIUijgDuYBv6a-W3mE6NJ6tKE4Afde4UvUFNS3p3N2wJnP58Cct6EHBVbQixof1mzMiIfwKGuj0AxTNnlAjJgzX7a-ERysKboA3WtCopRTU8mj8j3vISlJG5ti1AJM0GcMt2o4kuMHj_6TzmZgGIv1y_1lMO0GYpwm12ie3F0ith0NfwHQJbjym2h9yLULACUAgD5-tsJGDysrnww5eMH-QRcwCtMAOgnWDyz3obuZ8j_p01mmBugTRNvALT75X2u46pEnT3bebH1Zd1FiWQoQaVcDoapFpbTdHj7nFxu9CCjw"
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Got all tokens for konjisenpaii
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Importing get_owned_champions for konjisenpaii
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Extracted region from userinfo original_platform_id: EUW
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Using account region for champion data retrieval: EUW for konjisenpaii
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Calling get_owned_champions for konjisenpaii with region EUW
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Getting owned champions for region: EUW
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Headers set up: Authorization and Entitlements tokens configured
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] TCP Connector created with IPv4 family and SSL disabled
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Trying URL pattern 1/4
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Using base URL: https://euw-red.lol.sgp.pvp.net
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Inventory URL: https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v2/inventoriesWithLoyalty
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Wallet URL: https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v1/walletsbalances
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Location format: lolriot.aws-euc1-prod.euw
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Inventory params: {'puuid': '6c47ee27-a616-527f-a007-582ff470c05f', 'accountId': '****************', 'inventoryTypes': 'CHAMPION', 'location': 'lolriot.aws-euc1-prod.euw'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Attempting to resolve hostname: euw-red.lol.sgp.pvp.net
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Successfully resolved euw-red.lol.sgp.pvp.net to *************
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Successfully retrieved inventory data with pattern 1
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found 94 owned champions
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Requesting wallet data from: https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v1/walletsbalances
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Wallet params: {'puuid': '6c47ee27-a616-527f-a007-582ff470c05f', 'location': 'lolriot.aws-euc1-prod.euw', 'accountId': '****************', 'currencyTypes': ['RP', 'lol_blue_essence', 'tft_star_fragments']}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Successfully retrieved wallet data with pattern 1
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Successfully retrieved all data with pattern 1
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[90m@←[0m←[97m] Session closed properly
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Returned from get_owned_champions for konjisenpaii
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Extracted creation date for konjisenpaii: 2020-03-24 10:08:02 from timestamp *************
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Extracted creation region for konjisenpaii: EUW from tag euw
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Setting account region to EUW based on API data
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Saving comprehensive account info for konjisenpaii: BE=6385, RP=0, Level=167
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Saving 94 champions for konjisenpaii
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Getting skin data for konjisenpaii
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Extracted skin region from userinfo original_platform_id: EUW
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Using account region for skin data retrieval: EUW for konjisenpaii
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Getting owned skins for account 6c47ee27-a616-527f-a007-582ff470c05f
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found account for puuid 6c47ee27-a616-527f-a007-582ff470c05f
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Extracted account ID **************** from userinfo token
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[91m-←[0m←[97m] Error parsing userinfo string in get_queue_token: Expecting value: line 1 column 1 (char 0)
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - URL: https://euc1-red.pp.sgp.pvp.net/login-queue/v2/login/products/lol/regions/EUW1
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Region: EUW, Region Upper: EUW1
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Player Platform Edge URL: https://euc1-red.pp.sgp.pvp.net
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Headers: {"Authorization": "Bearer eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwiYWxnIjoiUlMyNTYifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.UBy_j2WOlo2NGvSonQ3BSnSj2jIuHVZCmGEZnhusmu1ZQZmwuqhlW8hLaBRaep9bxyzTdxsxYDV0cAJB-ydGVPPRFK837mBVMyb9nKuZfT70V-rDwW1j-HLXkD___Dg4NxHBFDcy3PYLvCXj-a_fg5MMd7TNRMlS3hqrqdzo-FVT3uA6-KIA3rzTncBYuSw71jOy1bRFuVwkBYUozvkbjy-NMbs1Yq6ubz4Q2UhsUIB2Sp6A6n4i0TPEfy1VdsLlULzypMGZRR0JYVCjfOJwuYOStKFSr10qKNgU77KiAx-Z6qcC5Kl5vm8jkT9hfkTMLcOLEOfbRYhPDgp4kzUp_A", "Accept": "application/json", "Content-Type": "application/json"}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Body: {"clientName": "lcu", "entitlements": "eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.******************************************************************************************************************************************************************************************************************************************************.tDuS89qt_P_U9PN1QGfDxHCYPBIY2NJEdT2tXPtgWOIwJmaRnBpq_sKwcc5JThXE99QLPOkTBIrqAsqydNAVYiqYG2IE3T7z9H8txj-kSfwMwd1LL75xuu1NNGtVOZf6jNmeB2wqkTcRkXrkimcrZ_urmStq0Qy3g3ZE22ZZKCB6r4Mk6CanKdxZ2UVRMH2NmFzG6dKMByyDE9r2DtBYq7MChHZsUIZXOvNQPoQhe3ltfL5BIMUsVffTKDkpiy9cgS3XDBKyW8MENT9WdELQWugNHQYJVvSUWEDlTBLNQlofO6ldmISJn8C7xQbgAJOgYHD68u1773s-LYV68olVgA", "userinfo": "eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwidHlwIjoidXNlcmluZm8rand0IiwiYWxnIjoiUlMyNTYifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PwJXbdFGFGlWyR76LA-hy9yKWnUaf7c42-R1nUYGknV46D86pnPagAxJ4U1KyO-U4f9yHd64tmHVVifkADaCgZzdQYwUHsvfHdvhvtJx5ldl-vYIs2uv_WdgFlE0RwtzCnnQ9VThQg_GmP3RuHIulJOLXL4T2Yq0Do0sg4TGVRx0xYXfq2VjIQoU092h6wgZ7Zb9KaqQDAErhEfDfuvid0aBBT8Ie5AluAQvz2dZ4IXaeTt9Usdb0yDikL2v0208Aw4u0vNaJPs0DGD3_Uq-Kbo-Nsu11LYM815SimKvFHEuRnlmEaepM8lbOBxJ3RRuo6DIrCovMETpX6K5IBMseQ"}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Status: 200
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Headers: {"date": "Tue, 01 Jul 2025 19:27:35 GMT", "content-type": "application/json;charset=utf-8", "transfer-encoding": "chunked", "connection": "keep-alive", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "expires": "0", "pragma": "no-cache", "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers, accept-encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-riot-edge-trace-id": "e668daac-7d31-4628-95d4-016d452ba47c", "x-xss-protection": "0", "content-encoding": "gzip", "access-control-allow-origin": "*", "access-control-allow-methods": "GET, PUT, DELETE, POST, OPTIONS", "access-control-allow-headers": "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range", "access-control-expose-headers": "Content-Length,Content-Range", "cf-cache-status": "DYNAMIC", "server": "cloudflare", "cf-ray": "95884d355ae11623-DUS"}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Body: {"token": "eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MwGk-0ZLO4R0zjeZycPH5PJifa891YULXRTDbVr3_wGe3N-6Fywef_csdWA7TgwxtienW-1OhiuOehER5fcou-iXmbt7WMFhhTQ6b9WJbnkJuMkC-lykpicKOu2Tvs-XZMmx0gLDJHuACpXqbZjwVyMlxI9m88FgCTo_ZEmqomNhCaRL1Ck7HQaZFL_Be_TLvXoaIEKuARwU5s6CbozFfGC0brgG3IjIQRpEK12w4WIJXI5MAoLdSIXq7hAv0DU5dvlkb9EJFeWtiM-V4KrNGRLtSr7WZL9nqLdsJ_AyKwtCRaNYDqDAlly_IADFBbE3fMdjkvi_b_4uPHa0ohuOfg", "type": "LOGIN"}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[91m-←[0m←[97m] Error parsing userinfo string in get_session_token: Expecting value: line 1 column 1 (char 0)
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Got JWT token from queue_token
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Parsed access token, PUUID: 6c47ee27-a616-527f-a007-582ff470c05f
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - URL: https://euc1-red.pp.sgp.pvp.net/session-external/v1/session/create
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Region: EUW, Region Upper: EUW1
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Player Platform Edge URL: https://euc1-red.pp.sgp.pvp.net
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Headers: {"Authorization": "Bearer eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MwGk-0ZLO4R0zjeZycPH5PJifa891YULXRTDbVr3_wGe3N-6Fywef_csdWA7TgwxtienW-1OhiuOehER5fcou-iXmbt7WMFhhTQ6b9WJbnkJuMkC-lykpicKOu2Tvs-XZMmx0gLDJHuACpXqbZjwVyMlxI9m88FgCTo_ZEmqomNhCaRL1Ck7HQaZFL_Be_TLvXoaIEKuARwU5s6CbozFfGC0brgG3IjIQRpEK12w4WIJXI5MAoLdSIXq7hAv0DU5dvlkb9EJFeWtiM-V4KrNGRLtSr7WZL9nqLdsJ_AyKwtCRaNYDqDAlly_IADFBbE3fMdjkvi_b_4uPHa0ohuOfg", "Accept": "application/json", "Content-Type": "application/json"}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Body: {"claims": {"cname": "lcu"}, "product": "lol", "puuid": "6c47ee27-a616-527f-a007-582ff470c05f", "region": "EUW1"}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Status: 200
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Headers: {"date": "Tue, 01 Jul 2025 19:27:35 GMT", "content-type": "application/json;charset=utf-8", "transfer-encoding": "chunked", "connection": "keep-alive", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "expires": "0", "pragma": "no-cache", "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers, accept-encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-riot-edge-trace-id": "360a9244-5eb5-467f-8b22-2390fa21a927", "x-xss-protection": "0", "content-encoding": "gzip", "access-control-allow-origin": "*", "access-control-allow-methods": "GET, PUT, DELETE, POST, OPTIONS", "access-control-allow-headers": "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range", "access-control-expose-headers": "Content-Length,Content-Range", "cf-cache-status": "DYNAMIC", "server": "cloudflare", "cf-ray": "95884d35bb471623-DUS"}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Text: "eyJraWQiOiJzMSIsImFsZyI6IlJTMjU2In0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TvGjJDXl3v791DshoH4sMJv-UkyYvYnG6MaIBaW_mI7_9tMgAElpUcmS0Rz-aHb-QYBkJjFfmpeQlAM-ygRbGTUGz54sMulOtx3cP5ZJ8Nuln-u1S_bU9rXYjXrccJv7cr_O5jk6CYEX_KHPDF3FTn6ZvBb-MY0yiIjFN1PZ-B1NATuKezrPhGoK2ff-k0yn-T-pg1tLVE5M_cYD6m9jFBVLv0wvTro-3x4r3SQfYGFtJEgfDCs-KONz_52DQYeIu3iDGWHB0Gewxj8ZHRfOmsrFDFbH8nqsCKBg2pLOk-DzSYjnlsjR6MZCcNrI-2KiZRl6kWC9WCwk86DHA2BdXQ"
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Successfully retrieved all tokens for skin retrieval
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Making request to https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v2/inventoriesWithLoyalty for skin data
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Successfully retrieved skin data response
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found items in skin data response: {'CHAMPION_SKIN': [{'itemId': 98051, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '805efa7f-f186-4f68-a83f-f0e4df12afd1', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '47c09d19-79f1-4ee0-b796-473c1a8307d5', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 80004, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200626T023618.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220705T170719.000Z', 'entitlementId': '30336130-3933-3730-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '2d24cf1a-ee4f-4dab-bdba-fa544fe05101', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 107015, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200708T213553.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200708T221700.000Z', 'entitlementId': '30336130-3935-3739-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '925296d6-3bc4-4b62-88c2-33ff8ee0e0de', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 166020, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '44fbe6e6-3871-4c5b-b7d6-80593083f1aa', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c309178c-f9e3-46c1-b073-4d5175dd62c5', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 21001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20211121T021328.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30336130-3533-3237-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0bcc9fd8-6b2f-4aea-bc67-c1264b2e2a3c', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 107019, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200708T214011.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200708T225741.000Z', 'entitlementId': '30336130-3933-3133-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '2a97d417-9ad3-48e8-a9c6-fcb520579e89', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 21002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200410T195746.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30336133-3331-3231-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'f67dbb57-0368-4234-8952-85101e6ef42d', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 107023, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200905T140912.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200905T221849.000Z', 'entitlementId': '30336130-3732-3630-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '13964487-2705-49fc-b937-1d792b5e8aa8', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 58008, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20201120T145639.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30336130-3937-3163-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b5fe5d2d-6838-45da-8a4a-10222b85895b', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 234010, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250530T175019.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '203b5c69-3590-4b00-9c84-213caa0d71d1', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '23253a42-ca5a-4ac5-a808-fc30739e96f0', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 64027, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200625T232643.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200626T011822.000Z', 'entitlementId': '30336130-3934-6264-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '8bf804a5-8393-4825-9383-1f4d83616ecc', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 84001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210712T221656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210712T230058.000Z', 'entitlementId': '30336130-3933-6234-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '328fc0ae-9f6d-44c8-972d-833a061bb74a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 84009, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210826T154754.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220711T233411.000Z', 'entitlementId': '30336130-3939-3439-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'd9fb0ced-9dda-46be-b53f-c1fbf26aabd7', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 105002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220623T180747.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20221026T202318.000Z', 'entitlementId': '30336130-3936-3037-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'a147e016-76b2-418e-a42a-3dd233d98975', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 105005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200617T135758.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200617T144225.000Z', 'entitlementId': '30336130-3936-3465-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'aeacc9f2-4e75-4d74-b420-7144d9d5d85f', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 84015, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200716T223220.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200716T231215.000Z', 'entitlementId': '30336130-3934-3338-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '60242bab-0053-4f52-a7de-525c6e96089b', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 58033, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250530T175040.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'f3a1d3c1-c2e0-43b3-9980-d35cf7326c27', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '07cbefcd-9797-4ea8-9995-ff0596e77ed7', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 62002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210701T131847.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20240901T202416.000Z', 'entitlementId': '30336130-3937-3563-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b77d48e3-db26-4c2c-8b31-b78638cbd0ac', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 14003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220430T122301.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220501T125316.000Z', 'entitlementId': '30336130-3733-6333-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '277e69dc-f3b0-4dbc-af88-ec1a83df1f25', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 78005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20230909T114841.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '24e98aa7-2cf3-4a16-8e6c-36006d477d61', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '1ef8a11d-6489-4022-ba83-452b131da19f', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 90038, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '0ddda8bc-e2ef-4bee-b913-7e3e6d57eb50', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0a45c80e-6c37-4cbd-bf0f-c615cac05c76', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 238011, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220818T145121.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220911T172442.000Z', 'entitlementId': 'a95cbdd2-0db3-490e-a228-03bfbc89120a', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '7cad0716-5a59-41f6-85fa-c10cae6b76c2', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '31c2790a-16bd-4c68-b10f-fa563fdbd4e7', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '996ec966-bfde-4151-8d1b-5b4101219876', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 14014, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220430T122315.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220430T141335.000Z', 'entitlementId': '30336130-3731-6636-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '135248bd-bfd0-4864-91ea-7026697e66e3', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 37056, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20241012T041048.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '3e3f3358-9292-4c1a-aebc-94d9b8c843ee', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'd3d6f524-933b-48b1-90da-d5f42db9bf18', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 8001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20230522T211055.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20230815T173549.000Z', 'entitlementId': '7890df04-43dc-4d7c-80d2-02e828c826e9', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '3da824a4-1f14-4a3d-ac3a-bd25b4370afe', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 37057, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20241012T041048.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '9368386c-7d32-47cc-8c00-ad33e1d81c14', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'cc7dedc5-cc93-460c-84af-26ad26f1e95a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 37058, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20241012T041048.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '1e56df0d-b1ca-4373-abbb-23fa8edf8fb2', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '3d4bac4a-5451-4396-9543-cee2623a3ea1', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 24003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200726T125553.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20240411T231624.000Z', 'entitlementId': '30336130-3937-6437-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'ba88c164-738c-40b1-b118-7a2ce93209c6', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 37059, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20241012T041048.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'f8286a6f-63ee-4bc9-9c7c-d6ec7b9e98cc', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c1c96856-eb6c-41d8-98eb-f57457a2a6a4', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 126024, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20211117T151205.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30336130-3732-6132-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '1c0cefc5-5d3e-408c-96ce-0b60806143ec', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 157003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20221007T124004.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20221007T124434.000Z', 'entitlementId': '6da861f7-8b83-447f-af71-78aa31915abf', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'ba7e7e39-3bd8-490e-947b-c973d86eb297', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19018, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '5703367f-cb96-4300-9557-3db59233cb0d', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '861dd57b-e87a-4006-84c5-52e3e16e1ffe', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '97b82bef-3b85-4a3c-8df1-74000c02991f', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'fe896ef1-2a0c-49d4-a1f3-a8c8a9c14176', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4045, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T092837.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'b9796018-d3cb-4a16-9e18-b22a73a28941', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '789b9546-e20b-4c29-a77e-d0d9d2c0b1a0', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4046, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T092837.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '638e730f-cee7-4d39-980e-d88076c4dda9', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '25121203-8270-48a5-b9fe-ebbe287d17cb', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4047, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T092837.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '5c99aeef-9438-46d9-bd43-2f540475bb57', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '40012ef7-ae2c-4b13-b044-618f6f9ac327', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19023, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'deb6b1ab-b7cc-4daa-9637-41785adead8a', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '6f363d48-b85d-4bd0-a9fe-d2d58a1d4674', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4048, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T092837.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '5476baaa-1469-420a-942b-1cc2c9b70e11', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0d3c52e0-8113-4152-83d6-a1369eecf260', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4049, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T092837.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'dd05b66c-c5ba-40a9-a18e-fe4504ea8c1b', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0e09fbee-7c7f-48d2-b138-71205d9a771d', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19025, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'ad51b750-2d66-42ae-8af0-4466a8eed4a1', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'd8428f19-daeb-45c5-ad39-db2be9d111a1', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200613T142057.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200613T152939.000Z', 'entitlementId': '30336130-3938-3133-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'bdf82d63-e04b-4468-bec6-27c01dab8630', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 35033, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20230822T195706.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '6ff84cc7-50e9-4986-9930-336f0a266393', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '64006c9a-1881-4d3f-8a31-d2ddcb06ceb9', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 23004, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20211128T012651.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20211128T152741.000Z', 'entitlementId': '30336130-3938-6364-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c1581b9d-96bf-468a-a45d-2887f670567e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 103005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240417T205555.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '61a48b45-b487-4dde-bcfa-9ae42a11bae5', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '2076567d-d90b-46d3-8799-3ebab3e365ce', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220619T172214.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220619T175326.000Z', 'entitlementId': '30336130-3933-6638-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '5a260596-10b2-4417-a3dd-e420c636c25a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29021, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '8f9474dd-0fe5-49f5-8492-c4de092485db', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '2717bff4-051d-4cd7-8280-c15cafb51e26', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 7005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200802T180447.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20230829T201434.000Z', 'entitlementId': '30336130-3937-3961-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b7faf0b7-26d0-4daa-a594-1a8a97478408', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29023, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '636bb0b7-31e4-429d-aa7e-ebba39c42535', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '8f0562cb-7c6b-4f99-91f0-db41ccb9221b', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29024, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '1972488a-a799-4acc-80d2-9bacef80a48b', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '48e5636f-6d6a-4372-b594-6d73cc56380e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 92003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200530T173233.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200530T174055.000Z', 'entitlementId': '30336133-3333-6333-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'fc0faa88-cb03-4db3-8231-2a5e88cf676a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 236001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200429T192422.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200430T104605.000Z', 'entitlementId': '30336130-3939-6336-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'e2da99c8-d4bc-400b-a385-f270aa51dac5', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55012, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200611T131830.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200611T154603.000Z', 'entitlementId': '30336130-3935-6335-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '92df77a2-5f01-470a-a261-35e793c71c12', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55015, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200611T132036.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200611T134123.000Z', 'entitlementId': '30336130-3934-3761-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '8658343b-b56d-4ce8-bee6-6983685127c0', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 103014, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20201116T211614.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20201116T212257.000Z', 'entitlementId': '30336130-3939-3062-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c4e28e05-5d3b-410b-95e0-fbd52d5f30ba', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 236008, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220504T123129.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220504T145604.000Z', 'entitlementId': '30336130-3938-3864-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'be840427-eabf-495e-a8ca-903a796b2eee', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55019, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200611T132039.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200611T145834.000Z', 'entitlementId': '30336130-3733-3266-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '20a675c4-265b-4b0f-be2d-ff1d6e470a0e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 81005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220922T150453.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220923T132932.000Z', 'entitlementId': '0db08953-f549-4093-810c-4dd6207f314e', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'a0638a12-1a65-45e8-9c33-53e85e60c268', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55021, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200725T003804.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200725T012415.000Z', 'entitlementId': '30336130-3936-6439-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b4b349e1-ca8c-4d24-a029-85187cbed1b4', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 39026, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220716T003418.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20230522T212104.000Z', 'entitlementId': '30336130-3936-3938-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'af4078ba-27b4-4bde-89df-52b6add727de', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 166001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220511T163201.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220714T190013.000Z', 'entitlementId': '30336130-3732-6538-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '1f258038-4725-42ac-9cce-39a1602aa09e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 22005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200726T125619.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20250402T073510.000Z', 'entitlementId': '30336130-3939-3837-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'e1ac28d8-57b3-47eb-b8fb-c2b82e99e973', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 92023, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210629T001935.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210630T234902.000Z', 'entitlementId': '30336130-3733-3733-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '241d116b-aeb3-4370-9394-d9806b387dab', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 86013, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250530T175101.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'c678d252-f079-46e6-b3af-bf5f2f756008', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '029573f2-b5eb-4cc4-bf6b-eae7e0e310a9', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}]}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found CHAMPION_SKIN in items: [{'itemId': 98051, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '805efa7f-f186-4f68-a83f-f0e4df12afd1', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '47c09d19-79f1-4ee0-b796-473c1a8307d5', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 80004, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200626T023618.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220705T170719.000Z', 'entitlementId': '30336130-3933-3730-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '2d24cf1a-ee4f-4dab-bdba-fa544fe05101', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 107015, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200708T213553.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200708T221700.000Z', 'entitlementId': '30336130-3935-3739-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '925296d6-3bc4-4b62-88c2-33ff8ee0e0de', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 166020, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '44fbe6e6-3871-4c5b-b7d6-80593083f1aa', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c309178c-f9e3-46c1-b073-4d5175dd62c5', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 21001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20211121T021328.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30336130-3533-3237-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0bcc9fd8-6b2f-4aea-bc67-c1264b2e2a3c', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 107019, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200708T214011.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200708T225741.000Z', 'entitlementId': '30336130-3933-3133-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '2a97d417-9ad3-48e8-a9c6-fcb520579e89', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 21002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200410T195746.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30336133-3331-3231-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'f67dbb57-0368-4234-8952-85101e6ef42d', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 107023, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200905T140912.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200905T221849.000Z', 'entitlementId': '30336130-3732-3630-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '13964487-2705-49fc-b937-1d792b5e8aa8', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 58008, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20201120T145639.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30336130-3937-3163-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b5fe5d2d-6838-45da-8a4a-10222b85895b', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 234010, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250530T175019.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '203b5c69-3590-4b00-9c84-213caa0d71d1', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '23253a42-ca5a-4ac5-a808-fc30739e96f0', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 64027, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200625T232643.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200626T011822.000Z', 'entitlementId': '30336130-3934-6264-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '8bf804a5-8393-4825-9383-1f4d83616ecc', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 84001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210712T221656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210712T230058.000Z', 'entitlementId': '30336130-3933-6234-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '328fc0ae-9f6d-44c8-972d-833a061bb74a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 84009, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210826T154754.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220711T233411.000Z', 'entitlementId': '30336130-3939-3439-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'd9fb0ced-9dda-46be-b53f-c1fbf26aabd7', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 105002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220623T180747.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20221026T202318.000Z', 'entitlementId': '30336130-3936-3037-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'a147e016-76b2-418e-a42a-3dd233d98975', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 105005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200617T135758.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200617T144225.000Z', 'entitlementId': '30336130-3936-3465-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'aeacc9f2-4e75-4d74-b420-7144d9d5d85f', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 84015, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200716T223220.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200716T231215.000Z', 'entitlementId': '30336130-3934-3338-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '60242bab-0053-4f52-a7de-525c6e96089b', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 58033, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250530T175040.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'f3a1d3c1-c2e0-43b3-9980-d35cf7326c27', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '07cbefcd-9797-4ea8-9995-ff0596e77ed7', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 62002, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210701T131847.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20240901T202416.000Z', 'entitlementId': '30336130-3937-3563-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b77d48e3-db26-4c2c-8b31-b78638cbd0ac', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 14003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220430T122301.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220501T125316.000Z', 'entitlementId': '30336130-3733-6333-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '277e69dc-f3b0-4dbc-af88-ec1a83df1f25', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 78005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20230909T114841.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '24e98aa7-2cf3-4a16-8e6c-36006d477d61', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '1ef8a11d-6489-4022-ba83-452b131da19f', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 90038, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '0ddda8bc-e2ef-4bee-b913-7e3e6d57eb50', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0a45c80e-6c37-4cbd-bf0f-c615cac05c76', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 238011, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220818T145121.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220911T172442.000Z', 'entitlementId': 'a95cbdd2-0db3-490e-a228-03bfbc89120a', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '7cad0716-5a59-41f6-85fa-c10cae6b76c2', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '31c2790a-16bd-4c68-b10f-fa563fdbd4e7', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '996ec966-bfde-4151-8d1b-5b4101219876', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 14014, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220430T122315.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220430T141335.000Z', 'entitlementId': '30336130-3731-6636-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '135248bd-bfd0-4864-91ea-7026697e66e3', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 37056, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20241012T041048.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '3e3f3358-9292-4c1a-aebc-94d9b8c843ee', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'd3d6f524-933b-48b1-90da-d5f42db9bf18', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 8001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20230522T211055.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20230815T173549.000Z', 'entitlementId': '7890df04-43dc-4d7c-80d2-02e828c826e9', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '3da824a4-1f14-4a3d-ac3a-bd25b4370afe', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 37057, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20241012T041048.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '9368386c-7d32-47cc-8c00-ad33e1d81c14', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'cc7dedc5-cc93-460c-84af-26ad26f1e95a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 37058, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20241012T041048.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '1e56df0d-b1ca-4373-abbb-23fa8edf8fb2', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '3d4bac4a-5451-4396-9543-cee2623a3ea1', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 24003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200726T125553.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20240411T231624.000Z', 'entitlementId': '30336130-3937-6437-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'ba88c164-738c-40b1-b118-7a2ce93209c6', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 37059, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20241012T041048.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'f8286a6f-63ee-4bc9-9c7c-d6ec7b9e98cc', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c1c96856-eb6c-41d8-98eb-f57457a2a6a4', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 126024, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20211117T151205.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '30336130-3732-6132-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '1c0cefc5-5d3e-408c-96ce-0b60806143ec', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 157003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20221007T124004.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20221007T124434.000Z', 'entitlementId': '6da861f7-8b83-447f-af71-78aa31915abf', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'ba7e7e39-3bd8-490e-947b-c973d86eb297', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19018, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '5703367f-cb96-4300-9557-3db59233cb0d', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '861dd57b-e87a-4006-84c5-52e3e16e1ffe', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '97b82bef-3b85-4a3c-8df1-74000c02991f', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'fe896ef1-2a0c-49d4-a1f3-a8c8a9c14176', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4045, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T092837.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'b9796018-d3cb-4a16-9e18-b22a73a28941', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '789b9546-e20b-4c29-a77e-d0d9d2c0b1a0', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4046, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T092837.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '638e730f-cee7-4d39-980e-d88076c4dda9', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '25121203-8270-48a5-b9fe-ebbe287d17cb', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4047, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T092837.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '5c99aeef-9438-46d9-bd43-2f540475bb57', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '40012ef7-ae2c-4b13-b044-618f6f9ac327', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19023, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'deb6b1ab-b7cc-4daa-9637-41785adead8a', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '6f363d48-b85d-4bd0-a9fe-d2d58a1d4674', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4048, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T092837.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '5476baaa-1469-420a-942b-1cc2c9b70e11', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0d3c52e0-8113-4152-83d6-a1369eecf260', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 4049, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250501T092837.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'dd05b66c-c5ba-40a9-a18e-fe4504ea8c1b', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '0e09fbee-7c7f-48d2-b138-71205d9a771d', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 19025, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'ad51b750-2d66-42ae-8af0-4466a8eed4a1', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'd8428f19-daeb-45c5-ad39-db2be9d111a1', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200613T142057.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200613T152939.000Z', 'entitlementId': '30336130-3938-3133-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'bdf82d63-e04b-4468-bec6-27c01dab8630', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 35033, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20230822T195706.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '6ff84cc7-50e9-4986-9930-336f0a266393', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '64006c9a-1881-4d3f-8a31-d2ddcb06ceb9', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 23004, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20211128T012651.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20211128T152741.000Z', 'entitlementId': '30336130-3938-6364-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c1581b9d-96bf-468a-a45d-2887f670567e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 103005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20240417T205555.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '61a48b45-b487-4dde-bcfa-9ae42a11bae5', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '2076567d-d90b-46d3-8799-3ebab3e365ce', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220619T172214.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220619T175326.000Z', 'entitlementId': '30336130-3933-6638-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '5a260596-10b2-4417-a3dd-e420c636c25a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29021, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '8f9474dd-0fe5-49f5-8492-c4de092485db', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '2717bff4-051d-4cd7-8280-c15cafb51e26', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 7005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200802T180447.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20230829T201434.000Z', 'entitlementId': '30336130-3937-3961-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b7faf0b7-26d0-4daa-a594-1a8a97478408', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29023, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '636bb0b7-31e4-429d-aa7e-ebba39c42535', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '8f0562cb-7c6b-4f99-91f0-db41ccb9221b', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 29024, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250526T181656.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': '1972488a-a799-4acc-80d2-9bacef80a48b', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '48e5636f-6d6a-4372-b594-6d73cc56380e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 92003, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200530T173233.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200530T174055.000Z', 'entitlementId': '30336133-3333-6333-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'fc0faa88-cb03-4db3-8231-2a5e88cf676a', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 236001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200429T192422.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200430T104605.000Z', 'entitlementId': '30336130-3939-6336-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'e2da99c8-d4bc-400b-a385-f270aa51dac5', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55012, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200611T131830.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200611T154603.000Z', 'entitlementId': '30336130-3935-6335-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '92df77a2-5f01-470a-a261-35e793c71c12', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55015, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200611T132036.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200611T134123.000Z', 'entitlementId': '30336130-3934-3761-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '8658343b-b56d-4ce8-bee6-6983685127c0', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 103014, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20201116T211614.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20201116T212257.000Z', 'entitlementId': '30336130-3939-3062-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'c4e28e05-5d3b-410b-95e0-fbd52d5f30ba', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 236008, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220504T123129.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220504T145604.000Z', 'entitlementId': '30336130-3938-3864-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'be840427-eabf-495e-a8ca-903a796b2eee', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55019, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200611T132039.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200611T145834.000Z', 'entitlementId': '30336130-3733-3266-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '20a675c4-265b-4b0f-be2d-ff1d6e470a0e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 81005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220922T150453.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220923T132932.000Z', 'entitlementId': '0db08953-f549-4093-810c-4dd6207f314e', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'a0638a12-1a65-45e8-9c33-53e85e60c268', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 55021, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200725T003804.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20200725T012415.000Z', 'entitlementId': '30336130-3936-6439-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'b4b349e1-ca8c-4d24-a029-85187cbed1b4', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 39026, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220716T003418.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20230522T212104.000Z', 'entitlementId': '30336130-3936-3938-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'af4078ba-27b4-4bde-89df-52b6add727de', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 166001, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20220511T163201.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20220714T190013.000Z', 'entitlementId': '30336130-3732-6538-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '1f258038-4725-42ac-9cce-39a1602aa09e', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 22005, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20200726T125619.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20250402T073510.000Z', 'entitlementId': '30336130-3939-3837-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': 'e1ac28d8-57b3-47eb-b8fb-c2b82e99e973', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 92023, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20210629T001935.000Z', 'quantity': 1, 'ownedQuantity': 1, 'usedInGameDate': '20210630T234902.000Z', 'entitlementId': '30336130-3733-3733-2d30-3939392d3131', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '241d116b-aeb3-4370-9394-d9806b387dab', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}, {'itemId': 86013, 'inventoryType': 'CHAMPION_SKIN', 'purchaseDate': '20250530T175101.000Z', 'quantity': 1, 'ownedQuantity': 1, 'entitlementId': 'c678d252-f079-46e6-b3af-bf5f2f756008', 'entitlementTypeId': '887960ea-4755-4443-9235-9aa50ceba935', 'instanceId': '029573f2-b5eb-4cc4-bf6b-eae7e0e310a9', 'instanceTypeId': 'd620ae7b-cf87-496c-96c5-893c99dc2791', 'payload': {'isVintage': False}, 'f2p': False, 'rental': False, 'loyalty': False, 'loyaltySources': []}]
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] CHAMPION_SKIN is a list with 64 items
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Loaded 1941 skin entries from JSON database
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Loaded skin database with 1941 entries
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 98051
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 98051, 'contentId': '47c09d19-79f1-4ee0-b796-473c1a8307d5', 'isBase': False, 'name': 'Three Honors Shen', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Shen/Skins/Skin51/Images/shen_splash_centered_51.SKINS_Shen_Skin51.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Shen/Skins/Skin51/Images/shen_splash_uncentered_51.SKINS_Shen_Skin51.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Shen/Skins/Skin51/Images/shen_splash_tile_51.SKINS_Shen_Skin51.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Shen/Skins/Skin51/ShenLoadScreen_51.SKINS_Shen_Skin51.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 176}], 'description': 'Shen is the last surviving founder of the Order of Solace. He shuttered every temple when the world fell into apathy and carelessness, disappearing into obscurity until Malzahar revived the Order. Now returned from the shadows, Shen serves as its most senior teacher, a stoic leader of integrity and grace that all can rely on.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 80004
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 80004, 'contentId': '2d24cf1a-ee4f-4dab-bdba-fa544fe05101', 'isBase': False, 'name': 'Full Metal Pantheon', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Pantheon/Skins/Skin04/Images/pantheon_splash_centered_4.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Pantheon/Skins/Skin04/Images/pantheon_splash_uncentered_4.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Pantheon/Skins/Skin04/Images/pantheon_splash_tile_4.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Pantheon/Skins/Skin04/PantheonLoadscreen_4.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 118}], 'description': 'A former Full Metal Robot Fighting League champion (2x reign, 14 title defenses), Pantheon was forced to retire in disgrace after a scandal involving Targon Fabrications. Reemerging from retirement to face the chosen champion, Jayce, Pantheon may be damaged, but has found that revenge adequately charges his weapon systems.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 107015
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 107015, 'contentId': '925296d6-3bc4-4b62-88c2-33ff8ee0e0de', 'isBase': False, 'name': 'Pretty Kitty Rengar', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Rengar/Skins/Skin15/Images/rengar_splash_centered_15.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Rengar/Skins/Skin15/Images/rengar_splash_uncentered_15.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Rengar/Skins/Skin15/Images/rengar_splash_tile_15.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Rengar/Skins/Skin15/RengarLoadscreen_15.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Rengar/Skins/Skin15/RengarLoadscreen_15_LE.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107015.png', 'chromas': [{'id': 107016, 'name': 'Pretty Kitty Rengar', 'contentId': 'cbc4ed44-801c-4d67-908e-74f29901c6b4', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107016.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive during 2019.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 107017, 'name': 'Pretty Kitty Rengar', 'contentId': 'c8ab9a62-1fe1-4944-879d-646f0b272c74', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107017.png', 'colors': ['#6ABBEE', '#6ABBEE'], 'descriptions': [], 'rarities': []}, {'id': 107018, 'name': 'Pretty Kitty Rengar', 'contentId': '72386172-586b-4edc-92c3-9d40a62e471f', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107018.png', 'colors': ['#DF9117', '#DF9117'], 'descriptions': [], 'rarities': []}, {'id': 107019, 'name': 'Pretty Kitty Rengar', 'contentId': '2a97d417-9ad3-48e8-a9c6-fcb520579e89', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107019.png', 'colors': ['#ECF9F8', '#ECF9F8'], 'descriptions': [], 'rarities': []}, {'id': 107020, 'name': 'Pretty Kitty Rengar', 'contentId': '8bfee237-829f-4916-b084-d664e03768f8', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107020.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [{'region': 'riot', 'description': 'Obtained through Influencer audience giveaways.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 107021, 'name': 'Pretty Kitty Rengar', 'contentId': 'cb270194-506c-4d1e-8b1e-e5e90ceb805e', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107021.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'TENCENT', 'rarity': 1}]}, {'id': 107022, 'name': 'Pretty Kitty Rengar', 'contentId': '02bb31a8-9c0d-45aa-9551-fe978799c200', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107022.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': []}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 98}], 'description': 'Once again, the prettiest kitty… is the deadliest.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 166020
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 166020, 'contentId': 'c309178c-f9e3-46c1-b073-4d5175dd62c5', 'isBase': False, 'name': 'Three Honors Akshan', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Akshan/Skins/Skin20/Images/akshan_splash_centered_20.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Akshan/Skins/Skin20/Images/akshan_splash_uncentered_20.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Akshan/Skins/Skin20/Images/akshan_splash_tile_20.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Akshan/Skins/Skin20/AkshanLoadScreen_20.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 176}], 'description': "When the Order of Solace found Akshan, he was a soulless weapon of death caught in a cycle of revenge following his mentor's murder. Even with his despair, the Order's teachings were able to fill the endless hole within Akshan and rejuvenate him. Now he stands with Malzahar as a Three Honors stalwart, ready to fight the Essents with harmony and quips."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 21001
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 21001, 'contentId': '0bcc9fd8-6b2f-4aea-bc67-c1264b2e2a3c', 'isBase': False, 'name': 'Cowgirl Miss Fortune', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/MissFortune/Skins/Skin01/Images/missfortune_splash_centered_1.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/MissFortune/Skins/Skin01/Images/missfortune_splash_uncentered_1.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/MissFortune/Skins/Skin01/Images/missfortune_splash_tile_1.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/MissFortune/Skins/Skin01/MissFortuneLoadScreen_1.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 39}], 'description': "No bounty hunter on the prairie is as skilled as Sarah Fortune, but she doesn't work for anything less than top dollar. Cause enough trouble on the high frontier and, sure enough, she'll come calling—with two gleaming pistols, and a bullet with your name on it."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 107019
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Pretty Kitty Rengar (Color: #ECF9F8) for champion Rengar
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 21002
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 21002, 'contentId': 'f67dbb57-0368-4234-8952-85101e6ef42d', 'isBase': False, 'name': 'Waterloo Miss Fortune', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/MissFortune/Skins/Skin02/Images/missfortune_splash_centered_2.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/MissFortune/Skins/Skin02/Images/missfortune_splash_uncentered_2.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/MissFortune/Skins/Skin02/Images/missfortune_splash_tile_2.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/MissFortune/Skins/Skin02/MissFortuneLoadScreen_2.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 167}], 'description': None}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 107023
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 107023, 'contentId': '13964487-2705-49fc-b937-1d792b5e8aa8', 'isBase': False, 'name': 'Guardian of the Sands Rengar', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Rengar/Skins/Skin23/Images/rengar_splash_centered_23.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Rengar/Skins/Skin23/Images/rengar_splash_uncentered_23.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Rengar/Skins/Skin23/Images/rengar_splash_tile_23.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Rengar/Skins/Skin23/RengarLoadscreen_23.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107023.png', 'chromas': [{'id': 107024, 'name': 'Guardian of the Sands Rengar', 'contentId': '1572c31e-97e4-4711-9c23-e76a43ed6243', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107024.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': []}, {'id': 107025, 'name': 'Guardian of the Sands Rengar', 'contentId': '07f46e55-dbef-468d-9bad-7bc748699099', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107025.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': []}, {'id': 107026, 'name': 'Guardian of the Sands Rengar', 'contentId': 'd518ccb0-4f2f-4ea7-aa76-92ecdcf81795', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107026.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2020.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 107027, 'name': 'Guardian of the Sands Rengar', 'contentId': 'f3e40e43-5b92-468d-bc98-3ca609773c5f', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107027.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': []}, {'id': 107028, 'name': 'Guardian of the Sands Rengar', 'contentId': '61e43e0a-cb47-418f-9c21-1f03cdb5fcfa', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107028.png', 'colors': ['#9C68D7', '#9C68D7'], 'descriptions': [], 'rarities': []}, {'id': 107029, 'name': 'Guardian of the Sands Rengar', 'contentId': '6bdfcdc4-2ed5-46f2-832a-2e0b5252a23a', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107029.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'TENCENT', 'rarity': 1}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 66}], 'description': 'An assassin of a forgotten order long thought dead, Rengar prowls the tombs, retrieving ancient relics of power for purposes unknown. Far more alarming, he appears to be hunting guardians, his blades enchanted to pierce their ancient magic and defenses…'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 58008
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 58008, 'contentId': 'b5fe5d2d-6838-45da-8a4a-10222b85895b', 'isBase': False, 'name': 'SKT T1 Renekton', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Renekton/Skins/skin08/Images/renekton_splash_centered_8.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Renekton/Skins/skin08/Images/renekton_splash_uncentered_8.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Renekton/Skins/skin08/Images/renekton_splash_tile_8.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Renekton/Skins/skin08/RenektonLoadscreen_8.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': True, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 5}], 'description': "Honoring MaRin's winning performance as Renekton during the 2015 World Championship."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 234010
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 234010, 'contentId': '23253a42-ca5a-4ac5-a808-fc30739e96f0', 'isBase': False, 'name': 'Dissonance of Pentakill Viego', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Viego/Skins/Skin10/Images/viego_splash_centered_10.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Viego/Skins/Skin10/Images/viego_splash_uncentered_10.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Viego/Skins/Skin10/Images/viego_splash_tile_10.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Viego/Skins/Skin10/ViegoLoadscreen_10.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/234/234010.png', 'chromas': [{'id': 234011, 'name': 'Dissonance of Pentakill Viego', 'contentId': '13eea8ae-9c40-4ef6-8e58-4ec4ccc1b1ec', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/234/234011.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2021.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 234012, 'name': 'Dissonance of Pentakill Viego', 'contentId': '102b4641-b891-4cd7-a94e-e1eff3d492e0', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/234/234012.png', 'colors': ['#DF9117', '#DF9117'], 'descriptions': [], 'rarities': []}, {'id': 234013, 'name': 'Dissonance of Pentakill Viego', 'contentId': '4c35835c-b781-4020-bce4-ad30807f43b5', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/234/234013.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': []}, {'id': 234014, 'name': 'Dissonance of Pentakill Viego', 'contentId': 'c267570e-f239-49ae-b80c-b02c8f4ef8a1', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/234/234014.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'TENCENT', 'rarity': 1}]}, {'id': 234015, 'name': 'Dissonance of Pentakill Viego', 'contentId': '27170c3b-a583-4d3e-9b29-4abd568d3163', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/234/234015.png', 'colors': ['#DED6B0', '#DED6B0'], 'descriptions': [], 'rarities': []}, {'id': 234016, 'name': 'Dissonance of Pentakill Viego', 'contentId': '0ef5bf7d-3eae-4ed8-ad78-941985cb353f', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/234/234016.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': []}, {'id': 234017, 'name': 'Dissonance of Pentakill Viego', 'contentId': 'b767f12d-adac-49f8-92e0-0774f7d3ef81', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/234/234017.png', 'colors': ['#54209B', '#54209B'], 'descriptions': [], 'rarities': []}, {'id': 234018, 'name': 'Dissonance of Pentakill Viego', 'contentId': '575389b3-3427-4797-bcad-8491b8e6fe9a', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/234/234018.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': []}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 151}], 'description': 'Only the broken-hearted can know the true sound of grief, that yearning for perfection that goes unheard by all others. Viego exists in a world of ashes and shadow, when it ought to be filled with light, and now looks beyond these grey horizons for new sounds and visions to make it so...'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 64027
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 64027, 'contentId': '8bf804a5-8393-4825-9383-1f4d83616ecc', 'isBase': False, 'name': 'Nightbringer Lee Sin', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/LeeSin/Skins/Skin27/Images/leesin_splash_centered_27.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/LeeSin/Skins/Skin27/Images/leesin_splash_uncentered_27.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/LeeSin/Skins/Skin27/Images/leesin_splash_tile_27.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/LeeSin/Skins/Skin27/LeeSinLoadscreen_27.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/LeeSin/Skins/Skin27/LeeSinLoadscreen_27_LE.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 193}], 'description': 'Lee Sin thrives in the heat of combat. An embodiment of passion and fury, he rules over the raw, chaotic emotions of the heart. While his siblings fight for various ideals, Lee Sin fights for the thrill of battle itself, striking with power and unpredictability.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 84001
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 84001, 'contentId': '328fc0ae-9f6d-44c8-972d-833a061bb74a', 'isBase': False, 'name': 'Stinger Akali', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin01/Images/akali_splash_centered_1.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin01/Images/akali_splash_uncentered_1.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin01/Images/akali_splash_tile_1.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin01/AkaliLoadscreen_1.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': True, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': None, 'description': 'The Order of the Great Sparrow Bee follows a brutal, time-honored training regimen, teaching students to sting with the ferocity and ceaselessness of a hornet. Akali is their greatest champion, and she intends to take the art to even greater heights.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 84009
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 84009, 'contentId': 'd9fb0ced-9dda-46be-b53f-c1fbf26aabd7', 'isBase': False, 'name': 'K/DA Akali', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin09/Images/akali_splash_centered_9.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin09/Images/akali_splash_uncentered_9.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin09/Images/akali_splash_tile_9.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin09/AkaliLoadscreen_9.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 91}], 'description': "Akali's street aesthetic and hard-hitting lyrics inspired a dedicated fandom. She wields a microphone or kama as a hip-hop ninja, revolutionizing the stale scene with her raw talents."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 105002
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 105002, 'contentId': 'a147e016-76b2-418e-a42a-3dd233d98975', 'isBase': False, 'name': 'Tundra Fizz', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Fizz/Skins/Skin02/Images/fizz_splash_centered_2.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Fizz/Skins/Skin02/Images/fizz_splash_uncentered_2.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Fizz/Skins/Skin02/Images/fizz_splash_tile_2.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Fizz/Skins/Skin02/FizzLoadScreen_2.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 167}], 'description': None}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 105005
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Fizz (Color: #F9E330) for champion Fizz
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 84015
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 84015, 'contentId': '60242bab-0053-4f52-a7de-525c6e96089b', 'isBase': False, 'name': 'True Damage Akali', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin15/Images/akali_splash_centered_15.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin15/Images/akali_splash_uncentered_15.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin15/Images/akali_splash_tile_15.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin15/AkaliLoadscreen_15.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin15/AkaliLoadscreen_15_LE.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/84/84015.png', 'chromas': [{'id': 84025, 'name': 'True Damage Akali', 'contentId': '940f6b17-be76-423a-84e9-60eaae20ad82', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/84/84025.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2019.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 84026, 'name': 'True Damage Akali', 'contentId': 'c86cf67e-5732-4a85-9f89-ad8e4bde2790', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/84/84026.png', 'colors': ['#FFEE59', '#FFEE59'], 'descriptions': [], 'rarities': []}, {'id': 84027, 'name': 'True Damage Akali', 'contentId': '9dfb6406-fd85-4dac-a7b1-9e65fa3b1360', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/84/84027.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [{'region': 'riot', 'description': 'Obtained through Influencer audience giveaways.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 84028, 'name': 'True Damage Akali', 'contentId': 'accb7f93-8389-43cb-a3ec-796e491cf64c', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/84/84028.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': []}, {'id': 84029, 'name': 'True Damage Akali', 'contentId': '21cd0f9b-089c-4341-a11d-0cabc5b42a58', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/84/84029.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'TENCENT', 'rarity': 1}]}, {'id': 84030, 'name': 'True Damage Akali', 'contentId': 'f91bf6c0-9565-4cab-bacc-b461579a5cc0', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/84/84030.png', 'colors': ['#ECF9F8', '#ECF9F8'], 'descriptions': [], 'rarities': []}, {'id': 84031, 'name': 'True Damage Akali', 'contentId': '93226781-b880-4872-a4d2-961c77ef6bee', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/84/84031.png', 'colors': ['#BD1357', '#19182A'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a Loot exclusive in 2019.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 120}], 'description': 'After the worldwide success of K/DA, Akali made it her mission to recruit accomplished and up-and-coming artists to form a new supergroup: True Damage. Never before has a crew been comprised of such diverse talent, ranging from natural lyricists to renowned producers to legendary vocalists—all ready to disrupt the music industry.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 58033
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 58033, 'contentId': '07cbefcd-9797-4ea8-9995-ff0596e77ed7', 'isBase': False, 'name': 'Dawnbringer Renekton', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Renekton/Skins/Skin33/Images/renekton_splash_centered_33.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Renekton/Skins/Skin33/Images/renekton_splash_uncentered_33.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Renekton/Skins/Skin33/Images/renekton_splash_tile_33.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Renekton/Skins/Skin33/RenektonLoadscreen_33.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/58/58033.png', 'chromas': [{'id': 58034, 'name': 'Dawnbringer Renekton', 'contentId': 'd3c0b1be-6bec-4ac0-b453-9ecdf7121ab4', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/58/58034.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2023.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 58035, 'name': 'Dawnbringer Renekton', 'contentId': 'f903172f-ff44-4a0e-94cb-a189a3a2ad4d', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/58/58035.png', 'colors': ['#DF9117', '#DF9117'], 'descriptions': [], 'rarities': []}, {'id': 58036, 'name': 'Dawnbringer Renekton', 'contentId': '7fb38ee8-5adc-4575-8a96-66fcd31e5117', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/58/58036.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': []}, {'id': 58037, 'name': 'Dawnbringer Renekton', 'contentId': '24e1c425-30f5-4d50-9955-56f02b792e2a', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/58/58037.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'TENCENT', 'rarity': 1}]}, {'id': 58038, 'name': 'Dawnbringer Renekton', 'contentId': '72f9e8ef-a8fc-4e51-b35a-78ccdb52400d', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/58/58038.png', 'colors': ['#54209B', '#54209B'], 'descriptions': [], 'rarities': []}, {'id': 58039, 'name': 'Dawnbringer Renekton', 'contentId': '5346080d-ebe2-428b-a8c5-9587fcbfdc74', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/58/58039.png', 'colors': ['#73BFBE', '#73BFBE'], 'descriptions': [], 'rarities': []}, {'id': 58040, 'name': 'Dawnbringer Renekton', 'contentId': 'cd5e61f8-b822-48c1-9504-bd05f5297736', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/58/58040.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': []}, {'id': 58041, 'name': 'Dawnbringer Renekton', 'contentId': '510167f5-9077-4c15-9ecb-169ee16f7a36', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/58/58041.png', 'colors': ['#ECF9F8', '#ECF9F8'], 'descriptions': [], 'rarities': []}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 193}], 'description': 'In the aftermath of chaos slaying order, two brothers were born: the embodiments of cause, Renekton, and effect, Nasus. As executioner for the Dawnbringers, Renekton has become more frenzied from battling his kin, but he still holds to his view that everything must have a purpose. For what function does chaos serve, if not to cause more chaos?'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 62002
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 62002, 'contentId': 'b77d48e3-db26-4c2c-8b31-b78638cbd0ac', 'isBase': False, 'name': 'General Wukong', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/MonkeyKing/Skins/Skin02/Images/monkeyking_splash_centered_2.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/MonkeyKing/Skins/Skin02/Images/monkeyking_splash_uncentered_2.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/MonkeyKing/Skins/Skin02/Images/monkeyking_splash_tile_2.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/MonkeyKing/Skins/Skin02/MonkeyKingLoadScreen_2.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 167}], 'description': 'Once a mischievous scout, Wukong led his sworn brethren on a treacherous journey to the western hinterlands, where he attained martial enlightenment. Now promoted to the rank of general, he commands a host of fearsome warriors, destined to cleanse the realm of monsters and demons alike.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 14003
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 14003, 'contentId': '277e69dc-f3b0-4dbc-af88-ec1a83df1f25', 'isBase': False, 'name': 'Lumberjack Sion', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Sion/Skins/Skin03/Images/sion_splash_centered_3.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Sion/Skins/Skin03/Images/sion_splash_uncentered_3.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Sion/Skins/Skin03/Images/sion_splash_tile_3.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Sion/Skins/Skin03/SionLoadScreen_3.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 100}], 'description': "He's a lumberjack, and he's okay."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 78005
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 78005, 'contentId': '1ef8a11d-6489-4022-ba83-452b131da19f', 'isBase': False, 'name': 'Battle Regalia Poppy', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Poppy/Skins/Skin05/Images/poppy_splash_centered_5.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Poppy/Skins/Skin05/Images/poppy_splash_uncentered_5.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Poppy/Skins/Skin05/Images/poppy_splash_tile_5.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Poppy/Skins/Skin05/PoppyLoadScreen_5.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/78/78005.png', 'chromas': [{'id': 78008, 'name': 'Battle Regalia Poppy', 'contentId': 'b662f00e-0b5b-4373-92a1-4eb0447f12dc', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/78/78008.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 78009, 'name': 'Battle Regalia Poppy', 'contentId': '9ecc088d-8c34-4a48-9725-d03ddfca6643', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/78/78009.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 78010, 'name': 'Battle Regalia Poppy', 'contentId': '2a0dc304-37c4-41a5-a8d6-63a22556935f', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/78/78010.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 78011, 'name': 'Battle Regalia Poppy', 'contentId': 'ce050793-9899-4d58-b790-acfd630e6b4f', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/78/78011.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 78012, 'name': 'Battle Regalia Poppy', 'contentId': 'cf819afa-41df-4531-94bc-6ba031113d81', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/78/78012.png', 'colors': ['#9C68D7', '#9C68D7'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 78013, 'name': 'Battle Regalia Poppy', 'contentId': '54b87974-5405-4aa1-8a2e-f2542aeb78f5', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/78/78013.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 167}], 'description': None}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 90038
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 90038, 'contentId': '0a45c80e-6c37-4cbd-bf0f-c615cac05c76', 'isBase': False, 'name': 'Three Honors Malzahar', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Malzahar/Skins/Skin38/Images/malzahar_splash_centered_38.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Malzahar/Skins/Skin38/Images/malzahar_splash_uncentered_38.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Malzahar/Skins/Skin38/Images/malzahar_splash_tile_38.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Malzahar/Skins/Skin38/MalzaharLoadscreen_38.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 176}], 'description': "As the Order of Solace's leader, Malzahar wants to extend a gentle, guiding hand to all. It was only by following the tenets of the Three Honors that he was able to escape from a pit of cynicism, and while apathy may reign supreme, Malzahar will do whatever it takes to build a better world."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 238011
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 238011, 'contentId': '7cad0716-5a59-41f6-85fa-c10cae6b76c2', 'isBase': False, 'name': 'Death Sworn Zed', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Zed/Skins/Skin11/Images/zed_splash_centered_11.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Zed/Skins/Skin11/Images/zed_splash_uncentered_11.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Zed/Skins/Skin11/Images/zed_splash_tile_11.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Zed/Skins/Skin11/ZedLoadscreen_11.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': True, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 87}], 'description': "Having lived his life in the shadows, Zed's eventual return from the dead should have come as no surprise to anyone. A master of unliving darkness, he is only too happy to spend all eternity spilling mortal blood, as one of the Death Sworn."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19001
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 19001, 'contentId': '996ec966-bfde-4151-8d1b-5b4101219876', 'isBase': False, 'name': 'Grey Warwick', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Warwick/Skins/Skin01/Images/warwick_splash_centered_1.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Warwick/Skins/Skin01/Images/warwick_splash_uncentered_1.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Warwick/Skins/Skin01/Images/warwick_splash_tile_1.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Warwick/Skins/Skin01/WarwickLoadscreen_1.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Warwick/Skins/Skin01/WarwickLoadscreen_1_LE.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': True, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/19/19001.png', 'chromas': [{'id': 19018, 'name': 'Grey Warwick', 'contentId': '861dd57b-e87a-4006-84c5-52e3e16e1ffe', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/19/19018.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 2}]}, {'id': 19023, 'name': 'Grey Warwick', 'contentId': '6f363d48-b85d-4bd0-a9fe-d2d58a1d4674', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/19/19023.png', 'colors': ['#5F432B', '#5F432B'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 2}]}, {'id': 19025, 'name': 'Grey Warwick', 'contentId': 'd8428f19-daeb-45c5-ad39-db2be9d111a1', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/19/19025.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 2}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 80}], 'description': 'Warwick was a man, once—a knight of great renown, pledged to serve at the side of Blade Queen Lissandra. In exchange for his loyalty she gifted him a shard of spectral moonlight, cursed to one day pierce his heart and sink him into the depths of depravity.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 14014
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 14014, 'contentId': '135248bd-bfd0-4864-91ea-7026697e66e3', 'isBase': False, 'name': 'Worldbreaker Sion', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Sion/Skins/Skin14/Images/sion_splash_centered_14.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Sion/Skins/Skin14/Images/sion_splash_uncentered_14.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Sion/Skins/Skin14/Images/sion_splash_tile_14.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Sion/Skins/Skin14/SionLoadscreen_14.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/14/14014.png', 'chromas': [{'id': 14015, 'name': 'Worldbreaker Sion', 'contentId': 'bbfafbe3-1a4f-464c-94b5-0aaed2e8a013', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/14/14015.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': []}, {'id': 14016, 'name': 'Worldbreaker Sion', 'contentId': '57669160-4fb0-483e-aa3a-1d465695dd9e', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/14/14016.png', 'colors': ['#ECF9F8', '#ECF9F8'], 'descriptions': [], 'rarities': []}, {'id': 14017, 'name': 'Worldbreaker Sion', 'contentId': 'cc3c0231-291d-48fb-9873-de5da2cccecf', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/14/14017.png', 'colors': ['#6ABBEE', '#6ABBEE'], 'descriptions': [], 'rarities': []}, {'id': 14018, 'name': 'Worldbreaker Sion', 'contentId': '7cb4b0ad-4eca-4e3d-93a9-56bb263d7a88', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/14/14018.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2020.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 14019, 'name': 'Worldbreaker Sion', 'contentId': 'a41cb584-e5ed-40ca-bebd-fdac82345338', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/14/14019.png', 'colors': ['#DF9117', '#DF9117'], 'descriptions': [], 'rarities': []}, {'id': 14020, 'name': 'Worldbreaker Sion', 'contentId': 'f2b9e7cd-1980-4f80-b8cb-9187f2bdffe2', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/14/14020.png', 'colors': ['#9C68D7', '#9C68D7'], 'descriptions': [], 'rarities': []}, {'id': 14021, 'name': 'Worldbreaker Sion', 'contentId': '9ce44ad6-0181-42df-9fd4-e4336019a964', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/14/14021.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': []}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 29}], 'description': 'A once towering fortress toppled during the last annihilation, the thing called Sion was given consciousness by Malzahar, and raised up to become the purest titan of death and destruction. Now, imbued with ravenous hatred, he blasts the terrain with every step, hellbent on culling any instance of life.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 37056
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 37056, 'contentId': 'd3d6f524-933b-48b1-90da-d5f42db9bf18', 'isBase': False, 'name': 'Victorious Sona', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Sona/Skins/Skin56/Images/sona_splash_centered_56.SKINS_Sona_Skin56.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Sona/Skins/Skin56/Images/sona_splash_uncentered_56.SKINS_Sona_Skin56.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Sona/Skins/Skin56/Images/sona_splash_tile_56.SKINS_Sona_Skin56.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Sona/Skins/Skin56/SonaLoadScreen_56.SKINS_Sona_Skin56.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/37/37056.png', 'chromas': [{'id': 37057, 'name': 'Victorious Sona', 'contentId': 'cc7dedc5-cc93-460c-84af-26ad26f1e95a', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/37/37057.png', 'colors': ['#764D43', '#764D43'], 'descriptions': [{'region': 'riot', 'description': "This Chroma was awarded to players who accumulated at least 1000 Split Points and reached Bronze rank in any Summoner's Rift ranked queue in Season 2024 - Split 2"}], 'rarities': [], 'skinAugments': {'borders': {'layer0': [{'contentId': '2d46b649-9330-474d-a4c4-c9c2087bfe13', 'layer': 0, 'priority': 0, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Iron.Season2024RankedRewards.png'}, {'contentId': '765c5e40-a9ab-4f51-ab6b-aa646c409135', 'layer': 0, 'priority': 1, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Bronze.Season2024RankedRewards.png'}, {'contentId': '7ddd4cce-8517-470f-b111-2ddd680d0654', 'layer': 0, 'priority': 2, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Silver.Season2024RankedRewards.png'}, {'contentId': 'd2398e32-b161-4ce7-ba79-6aee571c3c79', 'layer': 0, 'priority': 3, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Gold.Season2024RankedRewards.png'}, {'contentId': '3b990708-8848-4b7b-ac67-a05c120b85fd', 'layer': 0, 'priority': 4, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Platinum.Season2024RankedRewards.png'}, {'contentId': '1cce3040-4541-4ab5-955d-c56dafa3e63c', 'layer': 0, 'priority': 5, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Emerald.Season2024RankedRewards.png'}, {'contentId': 'ff623ab7-593d-4996-84f2-9018630f2312', 'layer': 0, 'priority': 6, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Diamond.Season2024RankedRewards.png'}, {'contentId': '570d327e-7ebd-4342-b108-3e7b9f3b932d', 'layer': 0, 'priority': 7, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Master.Season2024RankedRewards.png'}, {'contentId': 'f8ab9496-d957-4987-b0da-5f749aedab33', 'layer': 0, 'priority': 8, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Grandmaster.Season2024RankedRewards.png'}, {'contentId': '060dddac-b9f2-45c5-a056-e4bd5490f340', 'layer': 0, 'priority': 9, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Challenger.Season2024RankedRewards.png'}]}}}, {'id': 37058, 'name': 'Victorious Sona', 'contentId': '3d4bac4a-5451-4396-9543-cee2623a3ea1', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/37/37058.png', 'colors': ['#AAB5BA', '#AAB5BA'], 'descriptions': [{'region': 'riot', 'description': "This Chroma was awarded to players who accumulated at least 1000 Split Points and reached Silver rank in any Summoner's Rift ranked queue in Season 2024 - Split 2"}], 'rarities': [], 'skinAugments': {'borders': {'layer0': [{'contentId': '2d46b649-9330-474d-a4c4-c9c2087bfe13', 'layer': 0, 'priority': 0, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Iron.Season2024RankedRewards.png'}, {'contentId': '765c5e40-a9ab-4f51-ab6b-aa646c409135', 'layer': 0, 'priority': 1, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Bronze.Season2024RankedRewards.png'}, {'contentId': '7ddd4cce-8517-470f-b111-2ddd680d0654', 'layer': 0, 'priority': 2, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Silver.Season2024RankedRewards.png'}, {'contentId': 'd2398e32-b161-4ce7-ba79-6aee571c3c79', 'layer': 0, 'priority': 3, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Gold.Season2024RankedRewards.png'}, {'contentId': '3b990708-8848-4b7b-ac67-a05c120b85fd', 'layer': 0, 'priority': 4, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Platinum.Season2024RankedRewards.png'}, {'contentId': '1cce3040-4541-4ab5-955d-c56dafa3e63c', 'layer': 0, 'priority': 5, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Emerald.Season2024RankedRewards.png'}, {'contentId': 'ff623ab7-593d-4996-84f2-9018630f2312', 'layer': 0, 'priority': 6, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Diamond.Season2024RankedRewards.png'}, {'contentId': '570d327e-7ebd-4342-b108-3e7b9f3b932d', 'layer': 0, 'priority': 7, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Master.Season2024RankedRewards.png'}, {'contentId': 'f8ab9496-d957-4987-b0da-5f749aedab33', 'layer': 0, 'priority': 8, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Grandmaster.Season2024RankedRewards.png'}, {'contentId': '060dddac-b9f2-45c5-a056-e4bd5490f340', 'layer': 0, 'priority': 9, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Challenger.Season2024RankedRewards.png'}]}}}, {'id': 37059, 'name': 'Victorious Sona', 'contentId': 'c1c96856-eb6c-41d8-98eb-f57457a2a6a4', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/37/37059.png', 'colors': ['#D9C445', '#D9C445'], 'descriptions': [{'region': 'riot', 'description': "This Chroma was awarded to players who accumulated at least 80 Split Points and reached Gold rank in any Summoner's Rift ranked queue in Season 2024 - Split 2"}], 'rarities': [], 'skinAugments': {'borders': {'layer0': [{'contentId': '2d46b649-9330-474d-a4c4-c9c2087bfe13', 'layer': 0, 'priority': 0, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Iron.Season2024RankedRewards.png'}, {'contentId': '765c5e40-a9ab-4f51-ab6b-aa646c409135', 'layer': 0, 'priority': 1, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Bronze.Season2024RankedRewards.png'}, {'contentId': '7ddd4cce-8517-470f-b111-2ddd680d0654', 'layer': 0, 'priority': 2, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Silver.Season2024RankedRewards.png'}, {'contentId': 'd2398e32-b161-4ce7-ba79-6aee571c3c79', 'layer': 0, 'priority': 3, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Gold.Season2024RankedRewards.png'}, {'contentId': '3b990708-8848-4b7b-ac67-a05c120b85fd', 'layer': 0, 'priority': 4, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Platinum.Season2024RankedRewards.png'}, {'contentId': '1cce3040-4541-4ab5-955d-c56dafa3e63c', 'layer': 0, 'priority': 5, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Emerald.Season2024RankedRewards.png'}, {'contentId': 'ff623ab7-593d-4996-84f2-9018630f2312', 'layer': 0, 'priority': 6, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Diamond.Season2024RankedRewards.png'}, {'contentId': '570d327e-7ebd-4342-b108-3e7b9f3b932d', 'layer': 0, 'priority': 7, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Master.Season2024RankedRewards.png'}, {'contentId': 'f8ab9496-d957-4987-b0da-5f749aedab33', 'layer': 0, 'priority': 8, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Grandmaster.Season2024RankedRewards.png'}, {'contentId': '060dddac-b9f2-45c5-a056-e4bd5490f340', 'layer': 0, 'priority': 9, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Challenger.Season2024RankedRewards.png'}]}}}, {'id': 37060, 'name': 'Victorious Sona', 'contentId': '55200faf-2519-41e9-8521-04b21d5ccb4e', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/37/37060.png', 'colors': ['#55CDE5', '#55CDE5'], 'descriptions': [{'region': 'riot', 'description': "This Chroma was awarded to players who accumulated at least 80 Split Points and reached Platinum rank in any Summoner's Rift ranked queue in Season 2024 - Split 2"}], 'rarities': [{'region': 'riot', 'rarity': 1}], 'skinAugments': {'borders': {'layer0': [{'contentId': '2d46b649-9330-474d-a4c4-c9c2087bfe13', 'layer': 0, 'priority': 0, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Iron.Season2024RankedRewards.png'}, {'contentId': '765c5e40-a9ab-4f51-ab6b-aa646c409135', 'layer': 0, 'priority': 1, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Bronze.Season2024RankedRewards.png'}, {'contentId': '7ddd4cce-8517-470f-b111-2ddd680d0654', 'layer': 0, 'priority': 2, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Silver.Season2024RankedRewards.png'}, {'contentId': 'd2398e32-b161-4ce7-ba79-6aee571c3c79', 'layer': 0, 'priority': 3, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Gold.Season2024RankedRewards.png'}, {'contentId': '3b990708-8848-4b7b-ac67-a05c120b85fd', 'layer': 0, 'priority': 4, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Platinum.Season2024RankedRewards.png'}, {'contentId': '1cce3040-4541-4ab5-955d-c56dafa3e63c', 'layer': 0, 'priority': 5, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Emerald.Season2024RankedRewards.png'}, {'contentId': 'ff623ab7-593d-4996-84f2-9018630f2312', 'layer': 0, 'priority': 6, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Diamond.Season2024RankedRewards.png'}, {'contentId': '570d327e-7ebd-4342-b108-3e7b9f3b932d', 'layer': 0, 'priority': 7, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Master.Season2024RankedRewards.png'}, {'contentId': 'f8ab9496-d957-4987-b0da-5f749aedab33', 'layer': 0, 'priority': 8, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Grandmaster.Season2024RankedRewards.png'}, {'contentId': '060dddac-b9f2-45c5-a056-e4bd5490f340', 'layer': 0, 'priority': 9, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Challenger.Season2024RankedRewards.png'}]}}}, {'id': 37061, 'name': 'Victorious Sona', 'contentId': 'aaa6fd8f-75df-4644-8c4b-5fbfd903d6bc', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/37/37061.png', 'colors': ['#26854C', '#26854C'], 'descriptions': [{'region': 'riot', 'description': "This Chroma was awarded to players who accumulated at least 80 Split Points and reached Emerald rank in any Summoner's Rift ranked queue in Season 2024 - Split 2"}], 'rarities': [{'region': 'riot', 'rarity': 2}], 'skinAugments': {'borders': {'layer0': [{'contentId': '2d46b649-9330-474d-a4c4-c9c2087bfe13', 'layer': 0, 'priority': 0, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Iron.Season2024RankedRewards.png'}, {'contentId': '765c5e40-a9ab-4f51-ab6b-aa646c409135', 'layer': 0, 'priority': 1, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Bronze.Season2024RankedRewards.png'}, {'contentId': '7ddd4cce-8517-470f-b111-2ddd680d0654', 'layer': 0, 'priority': 2, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Silver.Season2024RankedRewards.png'}, {'contentId': 'd2398e32-b161-4ce7-ba79-6aee571c3c79', 'layer': 0, 'priority': 3, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Gold.Season2024RankedRewards.png'}, {'contentId': '3b990708-8848-4b7b-ac67-a05c120b85fd', 'layer': 0, 'priority': 4, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Platinum.Season2024RankedRewards.png'}, {'contentId': '1cce3040-4541-4ab5-955d-c56dafa3e63c', 'layer': 0, 'priority': 5, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Emerald.Season2024RankedRewards.png'}, {'contentId': 'ff623ab7-593d-4996-84f2-9018630f2312', 'layer': 0, 'priority': 6, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Diamond.Season2024RankedRewards.png'}, {'contentId': '570d327e-7ebd-4342-b108-3e7b9f3b932d', 'layer': 0, 'priority': 7, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Master.Season2024RankedRewards.png'}, {'contentId': 'f8ab9496-d957-4987-b0da-5f749aedab33', 'layer': 0, 'priority': 8, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Grandmaster.Season2024RankedRewards.png'}, {'contentId': '060dddac-b9f2-45c5-a056-e4bd5490f340', 'layer': 0, 'priority': 9, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Challenger.Season2024RankedRewards.png'}]}}}, {'id': 37062, 'name': 'Victorious Sona', 'contentId': '9a1a790b-702c-410e-a3ce-0d498fc01bc8', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/37/37062.png', 'colors': ['#65337F', '#65337F'], 'descriptions': [{'region': 'riot', 'description': "This Chroma was awarded to players who accumulated at least 80 Split Points and reached Diamond rank in any Summoner's Rift ranked queue in Season 2024 - Split 2"}], 'rarities': [{'region': 'riot', 'rarity': 3}], 'skinAugments': {'borders': {'layer0': [{'contentId': '2d46b649-9330-474d-a4c4-c9c2087bfe13', 'layer': 0, 'priority': 0, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Iron.Season2024RankedRewards.png'}, {'contentId': '765c5e40-a9ab-4f51-ab6b-aa646c409135', 'layer': 0, 'priority': 1, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Bronze.Season2024RankedRewards.png'}, {'contentId': '7ddd4cce-8517-470f-b111-2ddd680d0654', 'layer': 0, 'priority': 2, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Silver.Season2024RankedRewards.png'}, {'contentId': 'd2398e32-b161-4ce7-ba79-6aee571c3c79', 'layer': 0, 'priority': 3, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Gold.Season2024RankedRewards.png'}, {'contentId': '3b990708-8848-4b7b-ac67-a05c120b85fd', 'layer': 0, 'priority': 4, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Platinum.Season2024RankedRewards.png'}, {'contentId': '1cce3040-4541-4ab5-955d-c56dafa3e63c', 'layer': 0, 'priority': 5, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Emerald.Season2024RankedRewards.png'}, {'contentId': 'ff623ab7-593d-4996-84f2-9018630f2312', 'layer': 0, 'priority': 6, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Diamond.Season2024RankedRewards.png'}, {'contentId': '570d327e-7ebd-4342-b108-3e7b9f3b932d', 'layer': 0, 'priority': 7, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Master.Season2024RankedRewards.png'}, {'contentId': 'f8ab9496-d957-4987-b0da-5f749aedab33', 'layer': 0, 'priority': 8, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Grandmaster.Season2024RankedRewards.png'}, {'contentId': '060dddac-b9f2-45c5-a056-e4bd5490f340', 'layer': 0, 'priority': 9, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Challenger.Season2024RankedRewards.png'}]}}}, {'id': 37063, 'name': 'Victorious Sona', 'contentId': 'e2ec580a-94e4-458d-8e34-4f657fcf783a', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/37/37063.png', 'colors': ['#9D228D', '#9D228D'], 'descriptions': [{'region': 'riot', 'description': "This Chroma was awarded to players who accumulated at least 80 Split Points and reached Master rank in any Summoner's Rift ranked queue in Season 2024 - Split 2"}], 'rarities': [{'region': 'riot', 'rarity': 4}], 'skinAugments': {'borders': {'layer0': [{'contentId': '2d46b649-9330-474d-a4c4-c9c2087bfe13', 'layer': 0, 'priority': 0, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Iron.Season2024RankedRewards.png'}, {'contentId': '765c5e40-a9ab-4f51-ab6b-aa646c409135', 'layer': 0, 'priority': 1, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Bronze.Season2024RankedRewards.png'}, {'contentId': '7ddd4cce-8517-470f-b111-2ddd680d0654', 'layer': 0, 'priority': 2, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Silver.Season2024RankedRewards.png'}, {'contentId': 'd2398e32-b161-4ce7-ba79-6aee571c3c79', 'layer': 0, 'priority': 3, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Gold.Season2024RankedRewards.png'}, {'contentId': '3b990708-8848-4b7b-ac67-a05c120b85fd', 'layer': 0, 'priority': 4, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Platinum.Season2024RankedRewards.png'}, {'contentId': '1cce3040-4541-4ab5-955d-c56dafa3e63c', 'layer': 0, 'priority': 5, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Emerald.Season2024RankedRewards.png'}, {'contentId': 'ff623ab7-593d-4996-84f2-9018630f2312', 'layer': 0, 'priority': 6, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Diamond.Season2024RankedRewards.png'}, {'contentId': '570d327e-7ebd-4342-b108-3e7b9f3b932d', 'layer': 0, 'priority': 7, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Master.Season2024RankedRewards.png'}, {'contentId': 'f8ab9496-d957-4987-b0da-5f749aedab33', 'layer': 0, 'priority': 8, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Grandmaster.Season2024RankedRewards.png'}, {'contentId': '060dddac-b9f2-45c5-a056-e4bd5490f340', 'layer': 0, 'priority': 9, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Challenger.Season2024RankedRewards.png'}]}}}, {'id': 37064, 'name': 'Victorious Sona', 'contentId': '56de5202-abf8-4534-9b64-65ea684ac3b9', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/37/37064.png', 'colors': ['#B31316', '#B31316'], 'descriptions': [{'region': 'riot', 'description': "This Chroma was awarded to players who accumulated at least 80 Split Points and reached Grandmaster rank in any Summoner's Rift ranked queue in Season 2024 - Split 2"}], 'rarities': [{'region': 'riot', 'rarity': 5}], 'skinAugments': {'borders': {'layer0': [{'contentId': '2d46b649-9330-474d-a4c4-c9c2087bfe13', 'layer': 0, 'priority': 0, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Iron.Season2024RankedRewards.png'}, {'contentId': '765c5e40-a9ab-4f51-ab6b-aa646c409135', 'layer': 0, 'priority': 1, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Bronze.Season2024RankedRewards.png'}, {'contentId': '7ddd4cce-8517-470f-b111-2ddd680d0654', 'layer': 0, 'priority': 2, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Silver.Season2024RankedRewards.png'}, {'contentId': 'd2398e32-b161-4ce7-ba79-6aee571c3c79', 'layer': 0, 'priority': 3, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Gold.Season2024RankedRewards.png'}, {'contentId': '3b990708-8848-4b7b-ac67-a05c120b85fd', 'layer': 0, 'priority': 4, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Platinum.Season2024RankedRewards.png'}, {'contentId': '1cce3040-4541-4ab5-955d-c56dafa3e63c', 'layer': 0, 'priority': 5, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Emerald.Season2024RankedRewards.png'}, {'contentId': 'ff623ab7-593d-4996-84f2-9018630f2312', 'layer': 0, 'priority': 6, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Diamond.Season2024RankedRewards.png'}, {'contentId': '570d327e-7ebd-4342-b108-3e7b9f3b932d', 'layer': 0, 'priority': 7, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Master.Season2024RankedRewards.png'}, {'contentId': 'f8ab9496-d957-4987-b0da-5f749aedab33', 'layer': 0, 'priority': 8, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Grandmaster.Season2024RankedRewards.png'}, {'contentId': '060dddac-b9f2-45c5-a056-e4bd5490f340', 'layer': 0, 'priority': 9, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Challenger.Season2024RankedRewards.png'}]}}}, {'id': 37065, 'name': 'Victorious Sona', 'contentId': '47a2e908-4606-4ba5-8928-cfa9b3e448fd', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/37/37065.png', 'colors': ['#1D62C8', '#1D62C8'], 'descriptions': [{'region': 'riot', 'description': "This Chroma was awarded to players who accumulated at least 80 Split Points and reached Challenger rank in any Summoner's Rift ranked queue in Season 2024 - Split 2"}], 'rarities': [{'region': 'riot', 'rarity': 5}], 'skinAugments': {'borders': {'layer0': [{'contentId': '2d46b649-9330-474d-a4c4-c9c2087bfe13', 'layer': 0, 'priority': 0, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Iron.Season2024RankedRewards.png'}, {'contentId': '765c5e40-a9ab-4f51-ab6b-aa646c409135', 'layer': 0, 'priority': 1, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Bronze.Season2024RankedRewards.png'}, {'contentId': '7ddd4cce-8517-470f-b111-2ddd680d0654', 'layer': 0, 'priority': 2, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Silver.Season2024RankedRewards.png'}, {'contentId': 'd2398e32-b161-4ce7-ba79-6aee571c3c79', 'layer': 0, 'priority': 3, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Gold.Season2024RankedRewards.png'}, {'contentId': '3b990708-8848-4b7b-ac67-a05c120b85fd', 'layer': 0, 'priority': 4, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Platinum.Season2024RankedRewards.png'}, {'contentId': '1cce3040-4541-4ab5-955d-c56dafa3e63c', 'layer': 0, 'priority': 5, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Emerald.Season2024RankedRewards.png'}, {'contentId': 'ff623ab7-593d-4996-84f2-9018630f2312', 'layer': 0, 'priority': 6, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Diamond.Season2024RankedRewards.png'}, {'contentId': '570d327e-7ebd-4342-b108-3e7b9f3b932d', 'layer': 0, 'priority': 7, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Master.Season2024RankedRewards.png'}, {'contentId': 'f8ab9496-d957-4987-b0da-5f749aedab33', 'layer': 0, 'priority': 8, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Grandmaster.Season2024RankedRewards.png'}, {'contentId': '060dddac-b9f2-45c5-a056-e4bd5490f340', 'layer': 0, 'priority': 9, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Challenger.Season2024RankedRewards.png'}]}}}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 7}], 'skinAugments': {'borders': {'layer0': [{'contentId': '2d46b649-9330-474d-a4c4-c9c2087bfe13', 'layer': 0, 'priority': 0, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Iron.Season2024RankedRewards.png'}, {'contentId': '765c5e40-a9ab-4f51-ab6b-aa646c409135', 'layer': 0, 'priority': 1, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Bronze.Season2024RankedRewards.png'}, {'contentId': '7ddd4cce-8517-470f-b111-2ddd680d0654', 'layer': 0, 'priority': 2, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Silver.Season2024RankedRewards.png'}, {'contentId': 'd2398e32-b161-4ce7-ba79-6aee571c3c79', 'layer': 0, 'priority': 3, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Gold.Season2024RankedRewards.png'}, {'contentId': '3b990708-8848-4b7b-ac67-a05c120b85fd', 'layer': 0, 'priority': 4, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Platinum.Season2024RankedRewards.png'}, {'contentId': '1cce3040-4541-4ab5-955d-c56dafa3e63c', 'layer': 0, 'priority': 5, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Emerald.Season2024RankedRewards.png'}, {'contentId': 'ff623ab7-593d-4996-84f2-9018630f2312', 'layer': 0, 'priority': 6, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Diamond.Season2024RankedRewards.png'}, {'contentId': '570d327e-7ebd-4342-b108-3e7b9f3b932d', 'layer': 0, 'priority': 7, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Master.Season2024RankedRewards.png'}, {'contentId': 'f8ab9496-d957-4987-b0da-5f749aedab33', 'layer': 0, 'priority': 8, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Grandmaster.Season2024RankedRewards.png'}, {'contentId': '060dddac-b9f2-45c5-a056-e4bd5490f340', 'layer': 0, 'priority': 9, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Challenger.Season2024RankedRewards.png'}]}}, 'description': 'Victorious Sona was earned by players who accumulated enough split points in Ranked. Luring the unsuspecting into a false sense of ease with her melodies, Sona is as quick to pluck her allies from the brink of death as she is to send her foes to their grand finale.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 8001
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 8001, 'contentId': '3da824a4-1f14-4a3d-ac3a-bd25b4370afe', 'isBase': False, 'name': 'Count Vladimir', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Vladimir/Skins/Skin01/Images/vladimir_splash_centered_1.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Vladimir/Skins/Skin01/Images/vladimir_splash_uncentered_1.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Vladimir/Skins/Skin01/Images/vladimir_splash_tile_1.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Vladimir/Skins/Skin01/VladimirLoadScreen_1.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': True, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 71}], 'description': "Once upon a time, in the heart of a dark and gloomy castle, there lived an eternally youthful count who the people greatly feared. Every night, a terrible red mist would seep into the villages, and some poor soul would vanish without a trace—another life lost to Vladimir's growing appetites."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 37057
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Victorious Sona (Bronze) for champion Sona
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 37058
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Victorious Sona (Silver) for champion Sona
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 24003
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 24003, 'contentId': 'ba88c164-738c-40b1-b118-7a2ce93209c6', 'isBase': False, 'name': 'Angler Jax', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Jax/Skins/Skin03/Images/jax_splash_centered_3.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Jax/Skins/Skin03/Images/jax_splash_uncentered_3.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Jax/Skins/Skin03/Images/jax_splash_tile_3.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Jax/Skins/Skin03/JaxLoadscreen_3.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': True, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 100}], 'description': 'Nothing quite like the peace of the great outdoors, waiting for that perfect bite—and then violently ripping a fish out of the water before hitting it with his fishing rod.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 37059
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Victorious Sona (Gold) for champion Sona
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 126024
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 126024, 'contentId': '1c0cefc5-5d3e-408c-96ce-0b60806143ec', 'isBase': False, 'name': 'Arcane Inventor Jayce', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Jayce/Skins/Skin24/Images/jayce_splash_centered_24.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Jayce/Skins/Skin24/Images/jayce_splash_uncentered_24.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Jayce/Skins/Skin24/Images/jayce_splash_tile_24.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Jayce/Skins/Skin24/JayceLoadscreen_24.jpg', 'skinType': '', 'rarity': 'kRare', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 152}], 'description': 'Celebrating the RiotX Arcane event.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 157003
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 157003, 'contentId': 'ba7e7e39-3bd8-490e-947b-c973d86eb297', 'isBase': False, 'name': 'Blood Moon Yasuo', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Yasuo/Skins/Skin03/Images/yasuo_splash_centered_3.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Yasuo/Skins/Skin03/Images/yasuo_splash_uncentered_3.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Yasuo/Skins/Skin03/Images/yasuo_splash_tile_3.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Yasuo/Skins/Skin03/YasuoLoadScreen_3.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 12}], 'description': "Ceremonial executioner of the Blood Moon cult, Yasuo's blade is inhabited by an insidious, bloodthirsty demon whose hunger for death can never be satisfied. This suits Yasuo well, for he is possessed of an inner darkness even deeper than the creature whispering at his side."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19018
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Grey Warwick (Color: #2756CE) for champion Warwick
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29003
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 29003, 'contentId': 'fe896ef1-2a0c-49d4-a1f3-a8c8a9c14176', 'isBase': False, 'name': 'Medieval Twitch', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Twitch/Skins/Skin03/Images/twitch_splash_centered_3.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Twitch/Skins/Skin03/Images/twitch_splash_uncentered_3.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Twitch/Skins/Skin03/Images/twitch_splash_tile_3.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Twitch/Skins/Skin03/TwitchLoadScreen_3.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Twitch/Skins/Skin03/TwitchLoadScreen_3_LE.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': True, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/29/29003.png', 'chromas': [{'id': 29021, 'name': 'Medieval Twitch', 'contentId': '2717bff4-051d-4cd7-8280-c15cafb51e26', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/29/29021.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 2}]}, {'id': 29023, 'name': 'Medieval Twitch', 'contentId': '8f0562cb-7c6b-4f99-91f0-db41ccb9221b', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/29/29023.png', 'colors': ['#9C68D7', '#9C68D7'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 2}]}, {'id': 29024, 'name': 'Medieval Twitch', 'contentId': '48e5636f-6d6a-4372-b594-6d73cc56380e', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/29/29024.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 2}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 114}], 'description': 'An assassin-for-hire living beneath Dread Fortress Zaun, Twitch is a disgusting malefactor who has tipped every one of his killing implements in moist, toxic garbage. He excitedly awaits each contract, eager to kill, steal, and destroy as much as he can.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4045
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[93m!←[0m←[97m] Skin not found in database: 4045, champion_id: 4, champion_name: Twisted Fate
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4046
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[93m!←[0m←[97m] Skin not found in database: 4046, champion_id: 4, champion_name: Twisted Fate
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4047
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[93m!←[0m←[97m] Skin not found in database: 4047, champion_id: 4, champion_name: Twisted Fate
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19023
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Grey Warwick (Color: #5F432B) for champion Warwick
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4048
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[93m!←[0m←[97m] Skin not found in database: 4048, champion_id: 4, champion_name: Twisted Fate
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4049
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[93m!←[0m←[97m] Skin not found in database: 4049, champion_id: 4, champion_name: Twisted Fate
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19025
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Grey Warwick (Color: #E58BA5) for champion Warwick
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55001
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 55001, 'contentId': 'bdf82d63-e04b-4468-bec6-27c01dab8630', 'isBase': False, 'name': 'Mercenary Katarina', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin01/Images/katarina_splash_centered_1.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin01/Images/katarina_splash_uncentered_1.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin01/Images/katarina_splash_tile_1.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin01/KatarinaLoadScreen_1.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 167}], 'description': None}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 35033
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 35033, 'contentId': '64006c9a-1881-4d3f-8a31-d2ddcb06ceb9', 'isBase': False, 'name': 'Winterblessed Shaco', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Shaco/Skins/Skin33/Images/shaco_splash_centered_33.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Shaco/Skins/Skin33/Images/shaco_splash_uncentered_33.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Shaco/Skins/Skin33/Images/shaco_splash_tile_33.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Shaco/Skins/Skin33/ShacoLoadscreen_33.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Shaco/Skins/Skin33/ShacoLoadscreen_33_LE.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/35/35033.png', 'chromas': [{'id': 35034, 'name': 'Winterblessed Shaco', 'contentId': 'f834df7d-58e0-4acc-8f27-a52c5e634259', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/35/35034.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2022.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 35035, 'name': 'Winterblessed Shaco', 'contentId': '4e8caa3f-fea0-4d4f-965b-84a2ac5a2343', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/35/35035.png', 'colors': ['#73BFBE', '#73BFBE'], 'descriptions': [], 'rarities': []}, {'id': 35036, 'name': 'Winterblessed Shaco', 'contentId': '8cd738ca-6d1c-4ddf-9883-182ab913e622', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/35/35036.png', 'colors': ['#FFEE59', '#FFEE59'], 'descriptions': [], 'rarities': []}, {'id': 35037, 'name': 'Winterblessed Shaco', 'contentId': '8ef44953-3dda-4078-be6f-0dd4b9fa6e4d', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/35/35037.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': []}, {'id': 35038, 'name': 'Winterblessed Shaco', 'contentId': '1df0b265-d55c-4bbe-b2e8-4f936dfb3d6d', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/35/35038.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'TENCENT', 'rarity': 1}]}, {'id': 35039, 'name': 'Winterblessed Shaco', 'contentId': '2c96d245-f6bd-4d3b-a41b-7f4f5dbbb8c3', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/35/35039.png', 'colors': ['#9C68D7', '#9C68D7'], 'descriptions': [], 'rarities': []}, {'id': 35040, 'name': 'Winterblessed Shaco', 'contentId': 'df47ab2d-490f-43c0-8d4e-684a485a7523', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/35/35040.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': []}, {'id': 35041, 'name': 'Winterblessed Shaco', 'contentId': 'c28f5da5-025a-49df-b9ae-5cd1b12cc8db', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/35/35041.png', 'colors': ['#ECF9F8', '#ECF9F8'], 'descriptions': [], 'rarities': []}, {'id': 35042, 'name': 'Winterblessed Shaco', 'contentId': '3e6ab46c-23fb-4bb0-8790-4b5bd7e60e37', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/35/35042.png', 'colors': ['#BF2020', '#368B25'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a Loot exclusive in the Season 2023 event.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 187}], 'description': 'The Automaton Shaco felt nothing toward the quarreling leaders until Polaris snapped her fingers and ordered her “gifts” to corner the trio. “Winter is cruel,” she boomed, “and warmth is meant to be shared. Let this punishment serve as a warning.” In a flurry of snow, Polaris disappeared, leaving the Automaton and Beast to dispense her judgment.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 23004
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 23004, 'contentId': 'c1581b9d-96bf-468a-a45d-2887f670567e', 'isBase': False, 'name': 'Demonblade Tryndamere', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Tryndamere/Skins/Skin04/Images/tryndamere_splash_centered_4.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Tryndamere/Skins/Skin04/Images/tryndamere_splash_uncentered_4.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Tryndamere/Skins/Skin04/Images/tryndamere_splash_tile_4.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Tryndamere/Skins/Skin04/TryndamereLoadScreen_4.jpg', 'skinType': '', 'rarity': 'kLegendary', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 180}], 'description': "Tryndamere's cursed blade corrupted him from a once honorable barbarian into a rampaging monster. Now in a frenzy to sate his endless hunger, this demonic sword drives him to slay all. With each kill another fraction of his humanity is lost and succumbing further into the demonic sword's madness."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 103005
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 103005, 'contentId': '2076567d-d90b-46d3-8799-3ebab3e365ce', 'isBase': False, 'name': 'Challenger Ahri', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Ahri/Skins/Skin05/Images/ahri_splash_centered_5.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Ahri/Skins/Skin05/Images/ahri_splash_uncentered_5.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Ahri/Skins/Skin05/Images/ahri_splash_tile_5.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Ahri/Skins/Skin05/AhriLoadscreen_5.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Ahri/Skins/Skin05/AhriLoadscreen_5_LE.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': True, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/103/103005.png', 'chromas': [{'id': 103018, 'name': 'Challenger Ahri', 'contentId': '00319c18-ab33-41d4-aa15-00b718fd1d71', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/103/103018.png', 'colors': ['#FFC948', '#FFC948'], 'descriptions': [{'region': 'riot', 'description': 'Loot exclusive gained during the 2019 MSI Event.'}], 'rarities': [{'region': 'riot', 'rarity': 2}]}, {'id': 103056, 'name': 'Challenger Ahri', 'contentId': '562da728-3713-4dd6-b107-23bd809bb047', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/103/103056.png', 'colors': ['#2E38C4', '#C8003F'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released in the 2021 Ahri-versary event.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 9}], 'description': 'Commemorating the 2015 Season 5 start.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55005
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 55005, 'contentId': '5a260596-10b2-4417-a3dd-e420c636c25a', 'isBase': False, 'name': 'High Command Katarina', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin05/Images/katarina_splash_centered_5.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin05/Images/katarina_splash_uncentered_5.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin05/Images/katarina_splash_tile_5.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin05/KatarinaLoadScreen_5.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 177}], 'description': None}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29021
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Medieval Twitch (Color: #2756CE) for champion Twitch
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 7005
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 7005, 'contentId': 'b7faf0b7-26d0-4daa-a594-1a8a97478408', 'isBase': False, 'name': 'Elderwood LeBlanc', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Leblanc/Skins/Skin05/Images/leblanc_splash_centered_5.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Leblanc/Skins/Skin05/Images/leblanc_splash_uncentered_5.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Leblanc/Skins/Skin05/Images/leblanc_splash_tile_5.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Leblanc/Skins/Skin05/LeblancLoadScreen_5.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 58}], 'description': "When the Coven first rose to power, they craved the living enchantments of the Elderwood—a tool, they believed, for the resurrection of their dark patrons. Thus did they fall upon the great forest, and slaughter its children… until a lone sylvan stood against them, stealing the witches' lives, their magic, and even their names."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29023
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Medieval Twitch (Color: #9C68D7) for champion Twitch
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29024
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Medieval Twitch (Color: #27211C) for champion Twitch
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 92003
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 92003, 'contentId': 'fc0faa88-cb03-4db3-8231-2a5e88cf676a', 'isBase': False, 'name': 'Battle Bunny Riven', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Riven/Skins/Skin03/Images/riven_splash_centered_3.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Riven/Skins/Skin03/Images/riven_splash_uncentered_3.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Riven/Skins/Skin03/Images/riven_splash_tile_3.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Riven/Skins/Skin03/RivenLoadScreen_3.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92003.png', 'chromas': [{'id': 92008, 'name': 'Battle Bunny Riven', 'contentId': '12217994-4485-44d9-8f04-4ce542b2c369', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92008.png', 'colors': ['#FFEE59', '#FFEE59'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 92009, 'name': 'Battle Bunny Riven', 'contentId': '58e38d69-5bec-4886-bdb6-35330f5c7464', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92009.png', 'colors': ['#85827F', '#85827F'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 92010, 'name': 'Battle Bunny Riven', 'contentId': '0a1665db-29bb-4f69-81c6-fdeb07a5ec8a', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92010.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 92011, 'name': 'Battle Bunny Riven', 'contentId': 'b9398023-842d-408b-8858-f649158820cc', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92011.png', 'colors': ['#FF3CFF', '#FF3CFF'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 92012, 'name': 'Battle Bunny Riven', 'contentId': '21b5df88-e015-4bc6-a321-442d4e3f16b5', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92012.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 92013, 'name': 'Battle Bunny Riven', 'contentId': 'ffb4a5c6-38fd-4771-8ca0-6046e1834a19', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92013.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [], 'rarities': [{'region': 'VN', 'rarity': 1}, {'region': 'riot', 'rarity': 0}]}, {'id': 92014, 'name': 'Battle Bunny Riven', 'contentId': '71ea2341-e2b5-4c19-bae6-6b8f5424e8ac', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92014.png', 'colors': ['#9C68D7', '#9C68D7'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 92015, 'name': 'Battle Bunny Riven', 'contentId': '354e964e-d138-41d3-a014-8ae9c124eafc', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92015.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 100}], 'description': "Say “Hoppin' Mad” one more time. She dares you."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 236001
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 236001, 'contentId': 'e2da99c8-d4bc-400b-a385-f270aa51dac5', 'isBase': False, 'name': 'Hired Gun Lucian', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Lucian/Skins/Skin01/Images/lucian_splash_centered_1.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Lucian/Skins/Skin01/Images/lucian_splash_uncentered_1.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Lucian/Skins/Skin01/Images/lucian_splash_tile_1.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Lucian/Skins/Skin01/LucianLoadScreen_1.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 167}], 'description': None}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55012
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 55012, 'contentId': '92df77a2-5f01-470a-a261-35e793c71c12', 'isBase': False, 'name': 'Battle Academia Katarina', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin12/Images/katarina_splash_centered_12.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin12/Images/katarina_splash_uncentered_12.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin12/Images/katarina_splash_tile_12.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin12/KatarinaLoadscreen_12.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin12/KatarinaLoadscreen_12_LE.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55012.png', 'chromas': [{'id': 55013, 'name': 'Battle Academia Katarina', 'contentId': 'c2a1433a-1e17-466e-927e-e26691a3360b', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55013.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive during 2019.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 55014, 'name': 'Battle Academia Katarina', 'contentId': '4488aa1d-2a0e-4134-9c12-6b4941664feb', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55014.png', 'colors': ['#111112', '#EAF1F4'], 'descriptions': [{'region': 'riot', 'description': 'Loot exclusive gained during the 2019 Trials event.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 55015, 'name': 'Battle Academia Katarina', 'contentId': '8658343b-b56d-4ce8-bee6-6983685127c0', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55015.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': []}, {'id': 55016, 'name': 'Battle Academia Katarina', 'contentId': '9aac2169-bd6e-4f17-971a-dc624f902112', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55016.png', 'colors': ['#54209B', '#54209B'], 'descriptions': [], 'rarities': []}, {'id': 55017, 'name': 'Battle Academia Katarina', 'contentId': 'fdddf4ca-732d-49f6-82c0-765128b0e989', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55017.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': []}, {'id': 55018, 'name': 'Battle Academia Katarina', 'contentId': '6567b960-0318-4c00-a72a-2b69b2ee19f7', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55018.png', 'colors': ['#DF9117', '#DF9117'], 'descriptions': [], 'rarities': []}, {'id': 55019, 'name': 'Battle Academia Katarina', 'contentId': '20a675c4-265b-4b0f-be2d-ff1d6e470a0e', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55019.png', 'colors': ['#ECF9F8', '#ECF9F8'], 'descriptions': [], 'rarities': []}, {'id': 55020, 'name': 'Battle Academia Katarina', 'contentId': '70aaaa00-3dbc-4c17-b3f0-d20531c3c6f9', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55020.png', 'colors': ['#73BFBE', '#73BFBE'], 'descriptions': [], 'rarities': []}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 113}], 'description': 'Katarina is a rough-edged loner who stays far away from the politics of Durandal Academy. A 2nd year student with a chip on her shoulder, she is also a top member of the Assassin Club—the only club on campus allowed to kill opponents in school-sanctioned duels. Few other students ever dare approach her.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55015
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Battle Academia Katarina (Color: #2756CE) for champion Katarina
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 103014
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 103014, 'contentId': 'c4e28e05-5d3b-410b-95e0-fbd52d5f30ba', 'isBase': False, 'name': 'Star Guardian Ahri', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Ahri/Skins/Skin14/Images/ahri_splash_centered_14.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Ahri/Skins/Skin14/Images/ahri_splash_uncentered_14.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Ahri/Skins/Skin14/Images/ahri_splash_tile_14.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Ahri/Skins/Skin14/AhriLoadScreen_14.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Ahri/Skins/Skin14/AhriLoadScreen_14_LE.jpg', 'skinType': '', 'rarity': 'kLegendary', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/103/103014.png', 'chromas': [{'id': 103059, 'name': 'Star Guardian Ahri', 'contentId': '57e27d0a-de60-4e3f-87be-8bf649b6d108', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/103/103059.png', 'colors': ['#2E38C4', '#C8003F'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released in the 2021 Ahri-versary event.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 20}], 'description': "Ahri is a charismatic team captain who leads her group of Star Guardians from the outer cosmos, with the authority of a queen bee and the sly cunning of a fox. She's effortlessly popular, with an irresistible charm that disarms friends and foes alike."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 236008
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 236008, 'contentId': 'be840427-eabf-495e-a8ca-903a796b2eee', 'isBase': False, 'name': 'High Noon Lucian', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Lucian/Skins/Skin08/Images/lucian_splash_centered_8.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Lucian/Skins/Skin08/Images/lucian_splash_uncentered_8.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Lucian/Skins/Skin08/Images/lucian_splash_tile_8.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Lucian/Skins/Skin08/LucianLoadscreen_8.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Lucian/Skins/Skin08/LucianLoadScreen_8_LE.jpg', 'skinType': '', 'rarity': 'kLegendary', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/236/236008.png', 'chromas': [{'id': 236020, 'name': 'High Noon Lucian', 'contentId': '0e9aa823-0e91-4763-bbc2-629d5533206f', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/236/236020.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2020.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 236021, 'name': 'High Noon Lucian', 'contentId': '2b0df256-bff2-42c7-8c63-50698dfca573', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/236/236021.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': []}, {'id': 236022, 'name': 'High Noon Lucian', 'contentId': '5be2a7f2-52de-4491-84cf-dd5f21f64aba', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/236/236022.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': []}, {'id': 236023, 'name': 'High Noon Lucian', 'contentId': '546211ff-51ef-4d3b-ba24-cc7fc5f847d1', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/236/236023.png', 'colors': ['#ECF9F8', '#ECF9F8'], 'descriptions': [], 'rarities': []}, {'id': 236024, 'name': 'High Noon Lucian', 'contentId': 'a607fc1b-0c4f-4d28-85ee-c96b0bcea957', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/236/236024.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'TENCENT', 'rarity': 1}]}, {'id': 236050, 'name': 'High Noon Lucian', 'contentId': 'def6915c-e659-4084-88a0-087b62d5b9cd', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/236/236050.png', 'colors': ['#CFC0A7', '#59C3F2'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 4}]}, {'id': 236051, 'name': 'High Noon Lucian', 'contentId': '272608b2-6651-4cd6-8fd4-9310209d3967', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/236/236051.png', 'colors': ['#FF1502', '#E8CD6B'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 1}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 39}], 'description': "A former federal marshal and gunslinger for hire, Lucian's soul was corrupted when he made a deal with the devil in order to spare the life of his one true love. Double-crossed, and cursed with the powers of hell, he now hunts this devil across the high frontier, seeking revenge."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55019
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Battle Academia Katarina (Color: #ECF9F8) for champion Katarina
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 81005
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 81005, 'contentId': 'a0638a12-1a65-45e8-9c33-53e85e60c268', 'isBase': False, 'name': 'Pulsefire Ezreal', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Ezreal/Skins/Skin05/Images/ezreal_splash_centered_5.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Ezreal/Skins/Skin05/Images/ezreal_splash_uncentered_5.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Ezreal/Skins/Skin05/Images/ezreal_splash_tile_5.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Ezreal/Skins/Skin05/EzrealLoadscreen_5.jpg', 'skinType': 'Ultimate', 'rarity': 'kUltimate', 'isLegacy': False, 'splashVideoPath': '/lol-game-data/assets/ASSETS/Characters/Ezreal/Skins/Skin05/AnimatedSplash/Ezreal_Skin5_centered.webm', 'collectionSplashVideoPath': '/lol-game-data/assets/ASSETS/Characters/Ezreal/Skins/Skin05/AnimatedSplash/Ezreal_Skin5_uncentered.webm', 'collectionCardHoverVideoPath': '/lol-game-data/assets/ASSETS/Characters/Ezreal/Skins/Skin05/AnimatedSplash/Ezreal_Skin05_card.webm', 'featuresText': '<ul><li>All new spell effects and animations</li><li>Four stage evolving model</li><li>Pulsefire Ezreal and AI voices</li><li>Custom effects on a killing blow</li><li>Summoner icon</li><li>Summoner profile banner</li></ul>', 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 36}], 'description': 'Temporal fugitive and time-hopping explorer extraordinaire, Ezreal leaps across disparate realities searching for interesting technology to acquire. He is responsible for countless paradoxes and is currently wanted by the dystopian Remembrancers, who hunt him relentlessly.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55021
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 55021, 'contentId': 'b4b349e1-ca8c-4d24-a029-85187cbed1b4', 'isBase': False, 'name': 'Blood Moon Katarina', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin21/Images/katarina_splash_centered_21.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin21/Images/katarina_splash_uncentered_21.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin21/Images/katarina_splash_tile_21.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin21/KatarinaLoadscreen_21.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55021.png', 'chromas': [{'id': 55022, 'name': 'Blood Moon Katarina', 'contentId': '34751672-58c8-4b25-965e-64d6e2e05e67', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55022.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2020.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 55023, 'name': 'Blood Moon Katarina', 'contentId': '6e67d3bd-052c-4387-8f83-94274c79348c', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55023.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': []}, {'id': 55024, 'name': 'Blood Moon Katarina', 'contentId': '5a4f7ea0-2de0-4ccb-8341-9790d8552303', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55024.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': []}, {'id': 55025, 'name': 'Blood Moon Katarina', 'contentId': 'cc602f8f-ef4d-45f1-ad8f-ec46dd42aebb', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55025.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': []}, {'id': 55026, 'name': 'Blood Moon Katarina', 'contentId': 'e96818db-fa2f-49c1-8530-9bb18ae95d9b', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55026.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': []}, {'id': 55027, 'name': 'Blood Moon Katarina', 'contentId': '1e4bc204-4b8d-471e-92bb-b59ca432d0ae', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55027.png', 'colors': ['#DF9117', '#DF9117'], 'descriptions': [], 'rarities': []}, {'id': 55028, 'name': 'Blood Moon Katarina', 'contentId': '1c54d607-d505-4d0f-a23d-230f229b0ba6', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55028.png', 'colors': ['#ECF9F8', '#ECF9F8'], 'descriptions': [], 'rarities': []}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 12}], 'description': 'An honored priestess of the Blood Moon cult, merged with the flesh of her demon as all priestesses are fated to be. Yet the descent of the Blood Moon has changed the nature of demons and humankind, and Katarina has begun her ascent into a newer, darker form.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 39026
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 39026, 'contentId': 'af4078ba-27b4-4bde-89df-52b6add727de', 'isBase': False, 'name': 'Sentinel Irelia', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Irelia/Skins/Skin26/Images/irelia_splash_centered_26.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Irelia/Skins/Skin26/Images/irelia_splash_uncentered_26.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Irelia/Skins/Skin26/Images/irelia_splash_tile_26.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Irelia/Skins/Skin26/IreliaLoadscreen_26.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Irelia/Skins/Skin26/IreliaLoadscreen_26_LE.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/39/39026.png', 'chromas': [{'id': 39027, 'name': 'Sentinel Irelia', 'contentId': 'fb854fcf-e540-40d8-b43c-da5ebb92d9b6', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/39/39027.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2021.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 39028, 'name': 'Sentinel Irelia', 'contentId': 'c672f39d-368a-4ee5-bdfe-10fb68f72470', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/39/39028.png', 'colors': ['#DF9117', '#DF9117'], 'descriptions': [], 'rarities': []}, {'id': 39029, 'name': 'Sentinel Irelia', 'contentId': '011b89f5-74e3-4f0e-810b-79544d114d46', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/39/39029.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': []}, {'id': 39030, 'name': 'Sentinel Irelia', 'contentId': '1ed2825f-a6c5-428a-83ac-b92d1057ee07', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/39/39030.png', 'colors': ['#54209B', '#54209B'], 'descriptions': [], 'rarities': []}, {'id': 39031, 'name': 'Sentinel Irelia', 'contentId': '58daa6de-7e1e-4883-aad3-5f6519272da0', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/39/39031.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': []}, {'id': 39032, 'name': 'Sentinel Irelia', 'contentId': 'f8526a74-a451-43e9-a911-78a4bbd6a6ae', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/39/39032.png', 'colors': ['#ECF9F8', '#ECF9F8'], 'descriptions': [], 'rarities': []}, {'id': 39033, 'name': 'Sentinel Irelia', 'contentId': 'afc5b53a-ba19-45d3-8860-1f5ca7a6cfcc', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/39/39033.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'TENCENT', 'rarity': 1}]}, {'id': 39034, 'name': 'Sentinel Irelia', 'contentId': '037e7817-7d94-4406-bced-b45372ae5699', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/39/39034.png', 'colors': ['#73BFBE', '#73BFBE'], 'descriptions': [], 'rarities': []}, {'id': 39035, 'name': 'Sentinel Irelia', 'contentId': '4b5669cf-21a9-4d5d-af55-86582acc91af', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/39/39035.png', 'colors': ['#080808', '#8E0A38'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a Loot exclusive in the 2021 Sentinels event.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 147}], 'description': 'After the fall of the Ionian Grand Temple, Irelia could not stand idly by while her home was ravaged by the Black Mist. Deputized by the Sentinels, she fights the Ruination as one of their comrades: a natural-born leader who understands the threat that Ruined Karma poses, she will stop at nothing to save the Spirit of Ionia.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 166001
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 166001, 'contentId': '1f258038-4725-42ac-9cce-39a1602aa09e', 'isBase': False, 'name': 'Cyber Pop Akshan', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Akshan/Skins/Skin01/Images/akshan_splash_centered_1.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Akshan/Skins/Skin01/Images/akshan_splash_uncentered_1.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Akshan/Skins/Skin01/Images/akshan_splash_tile_1.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Akshan/Skins/Skin01/AkshanLoadscreen_1.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/166/166001.png', 'chromas': [{'id': 166002, 'name': 'Cyber Pop Akshan', 'contentId': '844787c9-cb8b-4546-af56-8c5f85de1c6b', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/166/166002.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2021.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 166003, 'name': 'Cyber Pop Akshan', 'contentId': 'a40c8c8a-a293-41fc-82c3-fde65ac3f101', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/166/166003.png', 'colors': ['#FFEE59', '#FFEE59'], 'descriptions': [], 'rarities': []}, {'id': 166004, 'name': 'Cyber Pop Akshan', 'contentId': '55367829-05ff-43e3-8d17-09ad53791734', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/166/166004.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': []}, {'id': 166005, 'name': 'Cyber Pop Akshan', 'contentId': '4554489e-5573-443a-9cc8-184e508ac05f', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/166/166005.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': []}, {'id': 166006, 'name': 'Cyber Pop Akshan', 'contentId': 'c514538c-ba6c-48dc-9ab2-fd0ea6dc164a', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/166/166006.png', 'colors': ['#54209B', '#54209B'], 'descriptions': [], 'rarities': []}, {'id': 166007, 'name': 'Cyber Pop Akshan', 'contentId': '4966a6b4-7280-4cbc-b0c9-dd178de7722f', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/166/166007.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'TENCENT', 'rarity': 1}]}, {'id': 166008, 'name': 'Cyber Pop Akshan', 'contentId': '29d8d8d0-c0b8-44e9-8c89-d0210887501a', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/166/166008.png', 'colors': ['#ECF9F8', '#ECF9F8'], 'descriptions': [], 'rarities': []}, {'id': 166009, 'name': 'Cyber Pop Akshan', 'contentId': '7aa8e385-0fd1-4fd5-92e6-740e2f4d2e2b', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/166/166009.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': []}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 148}], 'description': "The Neon Rogue of Sound City has become the hero of the underground, largely due to his bravery, his wit, and his uncanny ability to sneak into highly-secure corporate buildings without being detected. Being charming as hell doesn't hurt, either."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 22005
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 22005, 'contentId': 'e1ac28d8-57b3-47eb-b8fb-c2b82e99e973', 'isBase': False, 'name': 'Amethyst Ashe', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Ashe/Skins/Skin05/Images/ashe_splash_centered_5.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Ashe/Skins/Skin05/Images/ashe_splash_uncentered_5.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Ashe/Skins/Skin05/Images/ashe_splash_tile_5.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Ashe/Skins/Skin05/AsheLoadScreen_5.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 167}], 'description': None}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 92023
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 92023, 'contentId': '241d116b-aeb3-4370-9394-d9806b387dab', 'isBase': False, 'name': 'Spirit Blossom Riven', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Riven/Skins/Skin23/Images/riven_splash_centered_23.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Riven/Skins/Skin23/Images/riven_splash_uncentered_23.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Riven/Skins/Skin23/Images/riven_splash_tile_23.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Riven/Skins/Skin23/RivenLoadscreen_23.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Riven/Skins/Skin23/RivenLoadscreen_23_LE.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92023.png', 'chromas': [{'id': 92026, 'name': 'Spirit Blossom Riven', 'contentId': 'dad26d1e-d0d8-445c-99b9-189706a576d2', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92026.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': []}, {'id': 92027, 'name': 'Spirit Blossom Riven', 'contentId': 'd07d7603-82cd-4bf6-9191-48e69d1a7d20', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92027.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2020.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 92028, 'name': 'Spirit Blossom Riven', 'contentId': '0fab439e-8e94-4fab-bb58-7e682ab0d5b0', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92028.png', 'colors': ['#FFEE59', '#FFEE59'], 'descriptions': [], 'rarities': []}, {'id': 92029, 'name': 'Spirit Blossom Riven', 'contentId': '1dcfb737-6d78-4c33-9cfa-d1fd7690d85e', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92029.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': []}, {'id': 92030, 'name': 'Spirit Blossom Riven', 'contentId': 'ab62d4ce-0637-4b21-9f9c-5d6ca14a62a0', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92030.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': []}, {'id': 92031, 'name': 'Spirit Blossom Riven', 'contentId': '6e6a4183-caf1-4518-bd8e-671f895fce0c', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92031.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'TENCENT', 'rarity': 1}]}, {'id': 92032, 'name': 'Spirit Blossom Riven', 'contentId': '43b0de85-f1bd-434a-bcce-34d1e5fde703', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92032.png', 'colors': ['#54209B', '#54209B'], 'descriptions': [], 'rarities': []}, {'id': 92033, 'name': 'Spirit Blossom Riven', 'contentId': 'f5b2062a-5475-460b-8b63-9ca668ed9525', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92033.png', 'colors': ['#0C0C0F', '#B2D1E4'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a Loot exclusive in the 2020 Spirit Blossom event.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 171}], 'description': 'A brave warrior from an ancient land, Riven was ignobly cut down in the heat of battle thousands of years ago, her sword shattering in the process. Unable to find peace, she obsessively scours an otherworldly battlefield for pieces of her broken blade, possessed by a horrific darkness that guides her into oblivion.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 86013
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 86013, 'contentId': '029573f2-b5eb-4cc4-bf6b-eae7e0e310a9', 'isBase': False, 'name': 'God-King Garen', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Garen/Skins/Skin13/Images/garen_splash_centered_13.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Garen/Skins/Skin13/Images/garen_splash_uncentered_13.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Garen/Skins/Skin13/Images/garen_splash_tile_13.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Garen/Skins/Skin13/GarenLoadscreen_13.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Garen/Skins/Skin13/GarenLoadScreen_13_LE.jpg', 'skinType': '', 'rarity': 'kLegendary', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 78}], 'description': 'God-King Garen stands as the last scion of an ancient divinity, and the final bulwark against the end of all civilization. He rules the vast kingdom of Demacia with an iron fist, jealously protecting his people as a self-styled messiah, while rooting out and destroying all who would oppose him.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Extracted 64 skins from response
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 98051
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 98051, 'contentId': '47c09d19-79f1-4ee0-b796-473c1a8307d5', 'isBase': False, 'name': 'Three Honors Shen', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Shen/Skins/Skin51/Images/shen_splash_centered_51.SKINS_Shen_Skin51.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Shen/Skins/Skin51/Images/shen_splash_uncentered_51.SKINS_Shen_Skin51.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Shen/Skins/Skin51/Images/shen_splash_tile_51.SKINS_Shen_Skin51.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Shen/Skins/Skin51/ShenLoadScreen_51.SKINS_Shen_Skin51.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 176}], 'description': 'Shen is the last surviving founder of the Order of Solace. He shuttered every temple when the world fell into apathy and carelessness, disappearing into obscurity until Malzahar revived the Order. Now returned from the shadows, Shen serves as its most senior teacher, a stoic leader of integrity and grace that all can rely on.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 80004
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 80004, 'contentId': '2d24cf1a-ee4f-4dab-bdba-fa544fe05101', 'isBase': False, 'name': 'Full Metal Pantheon', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Pantheon/Skins/Skin04/Images/pantheon_splash_centered_4.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Pantheon/Skins/Skin04/Images/pantheon_splash_uncentered_4.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Pantheon/Skins/Skin04/Images/pantheon_splash_tile_4.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Pantheon/Skins/Skin04/PantheonLoadscreen_4.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 118}], 'description': 'A former Full Metal Robot Fighting League champion (2x reign, 14 title defenses), Pantheon was forced to retire in disgrace after a scandal involving Targon Fabrications. Reemerging from retirement to face the chosen champion, Jayce, Pantheon may be damaged, but has found that revenge adequately charges his weapon systems.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 107015
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 107015, 'contentId': '925296d6-3bc4-4b62-88c2-33ff8ee0e0de', 'isBase': False, 'name': 'Pretty Kitty Rengar', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Rengar/Skins/Skin15/Images/rengar_splash_centered_15.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Rengar/Skins/Skin15/Images/rengar_splash_uncentered_15.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Rengar/Skins/Skin15/Images/rengar_splash_tile_15.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Rengar/Skins/Skin15/RengarLoadscreen_15.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Rengar/Skins/Skin15/RengarLoadscreen_15_LE.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107015.png', 'chromas': [{'id': 107016, 'name': 'Pretty Kitty Rengar', 'contentId': 'cbc4ed44-801c-4d67-908e-74f29901c6b4', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107016.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive during 2019.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 107017, 'name': 'Pretty Kitty Rengar', 'contentId': 'c8ab9a62-1fe1-4944-879d-646f0b272c74', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107017.png', 'colors': ['#6ABBEE', '#6ABBEE'], 'descriptions': [], 'rarities': []}, {'id': 107018, 'name': 'Pretty Kitty Rengar', 'contentId': '72386172-586b-4edc-92c3-9d40a62e471f', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107018.png', 'colors': ['#DF9117', '#DF9117'], 'descriptions': [], 'rarities': []}, {'id': 107019, 'name': 'Pretty Kitty Rengar', 'contentId': '2a97d417-9ad3-48e8-a9c6-fcb520579e89', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107019.png', 'colors': ['#ECF9F8', '#ECF9F8'], 'descriptions': [], 'rarities': []}, {'id': 107020, 'name': 'Pretty Kitty Rengar', 'contentId': '8bfee237-829f-4916-b084-d664e03768f8', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107020.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [{'region': 'riot', 'description': 'Obtained through Influencer audience giveaways.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 107021, 'name': 'Pretty Kitty Rengar', 'contentId': 'cb270194-506c-4d1e-8b1e-e5e90ceb805e', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107021.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'TENCENT', 'rarity': 1}]}, {'id': 107022, 'name': 'Pretty Kitty Rengar', 'contentId': '02bb31a8-9c0d-45aa-9551-fe978799c200', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107022.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': []}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 98}], 'description': 'Once again, the prettiest kitty… is the deadliest.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 166020
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 166020, 'contentId': 'c309178c-f9e3-46c1-b073-4d5175dd62c5', 'isBase': False, 'name': 'Three Honors Akshan', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Akshan/Skins/Skin20/Images/akshan_splash_centered_20.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Akshan/Skins/Skin20/Images/akshan_splash_uncentered_20.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Akshan/Skins/Skin20/Images/akshan_splash_tile_20.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Akshan/Skins/Skin20/AkshanLoadScreen_20.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 176}], 'description': "When the Order of Solace found Akshan, he was a soulless weapon of death caught in a cycle of revenge following his mentor's murder. Even with his despair, the Order's teachings were able to fill the endless hole within Akshan and rejuvenate him. Now he stands with Malzahar as a Three Honors stalwart, ready to fight the Essents with harmony and quips."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 21001
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 21001, 'contentId': '0bcc9fd8-6b2f-4aea-bc67-c1264b2e2a3c', 'isBase': False, 'name': 'Cowgirl Miss Fortune', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/MissFortune/Skins/Skin01/Images/missfortune_splash_centered_1.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/MissFortune/Skins/Skin01/Images/missfortune_splash_uncentered_1.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/MissFortune/Skins/Skin01/Images/missfortune_splash_tile_1.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/MissFortune/Skins/Skin01/MissFortuneLoadScreen_1.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 39}], 'description': "No bounty hunter on the prairie is as skilled as Sarah Fortune, but she doesn't work for anything less than top dollar. Cause enough trouble on the high frontier and, sure enough, she'll come calling—with two gleaming pistols, and a bullet with your name on it."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 107019
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Pretty Kitty Rengar (Color: #ECF9F8) for champion Rengar
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 21002
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 21002, 'contentId': 'f67dbb57-0368-4234-8952-85101e6ef42d', 'isBase': False, 'name': 'Waterloo Miss Fortune', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/MissFortune/Skins/Skin02/Images/missfortune_splash_centered_2.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/MissFortune/Skins/Skin02/Images/missfortune_splash_uncentered_2.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/MissFortune/Skins/Skin02/Images/missfortune_splash_tile_2.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/MissFortune/Skins/Skin02/MissFortuneLoadScreen_2.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 167}], 'description': None}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 107023
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 107023, 'contentId': '13964487-2705-49fc-b937-1d792b5e8aa8', 'isBase': False, 'name': 'Guardian of the Sands Rengar', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Rengar/Skins/Skin23/Images/rengar_splash_centered_23.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Rengar/Skins/Skin23/Images/rengar_splash_uncentered_23.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Rengar/Skins/Skin23/Images/rengar_splash_tile_23.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Rengar/Skins/Skin23/RengarLoadscreen_23.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107023.png', 'chromas': [{'id': 107024, 'name': 'Guardian of the Sands Rengar', 'contentId': '1572c31e-97e4-4711-9c23-e76a43ed6243', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107024.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': []}, {'id': 107025, 'name': 'Guardian of the Sands Rengar', 'contentId': '07f46e55-dbef-468d-9bad-7bc748699099', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107025.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': []}, {'id': 107026, 'name': 'Guardian of the Sands Rengar', 'contentId': 'd518ccb0-4f2f-4ea7-aa76-92ecdcf81795', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107026.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2020.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 107027, 'name': 'Guardian of the Sands Rengar', 'contentId': 'f3e40e43-5b92-468d-bc98-3ca609773c5f', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107027.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': []}, {'id': 107028, 'name': 'Guardian of the Sands Rengar', 'contentId': '61e43e0a-cb47-418f-9c21-1f03cdb5fcfa', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107028.png', 'colors': ['#9C68D7', '#9C68D7'], 'descriptions': [], 'rarities': []}, {'id': 107029, 'name': 'Guardian of the Sands Rengar', 'contentId': '6bdfcdc4-2ed5-46f2-832a-2e0b5252a23a', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/107/107029.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'TENCENT', 'rarity': 1}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 66}], 'description': 'An assassin of a forgotten order long thought dead, Rengar prowls the tombs, retrieving ancient relics of power for purposes unknown. Far more alarming, he appears to be hunting guardians, his blades enchanted to pierce their ancient magic and defenses…'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 58008
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 58008, 'contentId': 'b5fe5d2d-6838-45da-8a4a-10222b85895b', 'isBase': False, 'name': 'SKT T1 Renekton', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Renekton/Skins/skin08/Images/renekton_splash_centered_8.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Renekton/Skins/skin08/Images/renekton_splash_uncentered_8.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Renekton/Skins/skin08/Images/renekton_splash_tile_8.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Renekton/Skins/skin08/RenektonLoadscreen_8.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': True, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 5}], 'description': "Honoring MaRin's winning performance as Renekton during the 2015 World Championship."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 234010
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 234010, 'contentId': '23253a42-ca5a-4ac5-a808-fc30739e96f0', 'isBase': False, 'name': 'Dissonance of Pentakill Viego', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Viego/Skins/Skin10/Images/viego_splash_centered_10.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Viego/Skins/Skin10/Images/viego_splash_uncentered_10.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Viego/Skins/Skin10/Images/viego_splash_tile_10.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Viego/Skins/Skin10/ViegoLoadscreen_10.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/234/234010.png', 'chromas': [{'id': 234011, 'name': 'Dissonance of Pentakill Viego', 'contentId': '13eea8ae-9c40-4ef6-8e58-4ec4ccc1b1ec', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/234/234011.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2021.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 234012, 'name': 'Dissonance of Pentakill Viego', 'contentId': '102b4641-b891-4cd7-a94e-e1eff3d492e0', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/234/234012.png', 'colors': ['#DF9117', '#DF9117'], 'descriptions': [], 'rarities': []}, {'id': 234013, 'name': 'Dissonance of Pentakill Viego', 'contentId': '4c35835c-b781-4020-bce4-ad30807f43b5', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/234/234013.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': []}, {'id': 234014, 'name': 'Dissonance of Pentakill Viego', 'contentId': 'c267570e-f239-49ae-b80c-b02c8f4ef8a1', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/234/234014.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'TENCENT', 'rarity': 1}]}, {'id': 234015, 'name': 'Dissonance of Pentakill Viego', 'contentId': '27170c3b-a583-4d3e-9b29-4abd568d3163', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/234/234015.png', 'colors': ['#DED6B0', '#DED6B0'], 'descriptions': [], 'rarities': []}, {'id': 234016, 'name': 'Dissonance of Pentakill Viego', 'contentId': '0ef5bf7d-3eae-4ed8-ad78-941985cb353f', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/234/234016.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': []}, {'id': 234017, 'name': 'Dissonance of Pentakill Viego', 'contentId': 'b767f12d-adac-49f8-92e0-0774f7d3ef81', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/234/234017.png', 'colors': ['#54209B', '#54209B'], 'descriptions': [], 'rarities': []}, {'id': 234018, 'name': 'Dissonance of Pentakill Viego', 'contentId': '575389b3-3427-4797-bcad-8491b8e6fe9a', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/234/234018.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': []}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 151}], 'description': 'Only the broken-hearted can know the true sound of grief, that yearning for perfection that goes unheard by all others. Viego exists in a world of ashes and shadow, when it ought to be filled with light, and now looks beyond these grey horizons for new sounds and visions to make it so...'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 64027
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 64027, 'contentId': '8bf804a5-8393-4825-9383-1f4d83616ecc', 'isBase': False, 'name': 'Nightbringer Lee Sin', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/LeeSin/Skins/Skin27/Images/leesin_splash_centered_27.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/LeeSin/Skins/Skin27/Images/leesin_splash_uncentered_27.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/LeeSin/Skins/Skin27/Images/leesin_splash_tile_27.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/LeeSin/Skins/Skin27/LeeSinLoadscreen_27.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/LeeSin/Skins/Skin27/LeeSinLoadscreen_27_LE.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 193}], 'description': 'Lee Sin thrives in the heat of combat. An embodiment of passion and fury, he rules over the raw, chaotic emotions of the heart. While his siblings fight for various ideals, Lee Sin fights for the thrill of battle itself, striking with power and unpredictability.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 84001
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 84001, 'contentId': '328fc0ae-9f6d-44c8-972d-833a061bb74a', 'isBase': False, 'name': 'Stinger Akali', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin01/Images/akali_splash_centered_1.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin01/Images/akali_splash_uncentered_1.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin01/Images/akali_splash_tile_1.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin01/AkaliLoadscreen_1.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': True, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': None, 'description': 'The Order of the Great Sparrow Bee follows a brutal, time-honored training regimen, teaching students to sting with the ferocity and ceaselessness of a hornet. Akali is their greatest champion, and she intends to take the art to even greater heights.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 84009
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 84009, 'contentId': 'd9fb0ced-9dda-46be-b53f-c1fbf26aabd7', 'isBase': False, 'name': 'K/DA Akali', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin09/Images/akali_splash_centered_9.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin09/Images/akali_splash_uncentered_9.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin09/Images/akali_splash_tile_9.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin09/AkaliLoadscreen_9.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 91}], 'description': "Akali's street aesthetic and hard-hitting lyrics inspired a dedicated fandom. She wields a microphone or kama as a hip-hop ninja, revolutionizing the stale scene with her raw talents."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 105002
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 105002, 'contentId': 'a147e016-76b2-418e-a42a-3dd233d98975', 'isBase': False, 'name': 'Tundra Fizz', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Fizz/Skins/Skin02/Images/fizz_splash_centered_2.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Fizz/Skins/Skin02/Images/fizz_splash_uncentered_2.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Fizz/Skins/Skin02/Images/fizz_splash_tile_2.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Fizz/Skins/Skin02/FizzLoadScreen_2.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 167}], 'description': None}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 105005
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Fizz (Color: #F9E330) for champion Fizz
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 84015
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 84015, 'contentId': '60242bab-0053-4f52-a7de-525c6e96089b', 'isBase': False, 'name': 'True Damage Akali', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin15/Images/akali_splash_centered_15.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin15/Images/akali_splash_uncentered_15.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin15/Images/akali_splash_tile_15.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin15/AkaliLoadscreen_15.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Akali/Skins/Skin15/AkaliLoadscreen_15_LE.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/84/84015.png', 'chromas': [{'id': 84025, 'name': 'True Damage Akali', 'contentId': '940f6b17-be76-423a-84e9-60eaae20ad82', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/84/84025.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2019.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 84026, 'name': 'True Damage Akali', 'contentId': 'c86cf67e-5732-4a85-9f89-ad8e4bde2790', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/84/84026.png', 'colors': ['#FFEE59', '#FFEE59'], 'descriptions': [], 'rarities': []}, {'id': 84027, 'name': 'True Damage Akali', 'contentId': '9dfb6406-fd85-4dac-a7b1-9e65fa3b1360', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/84/84027.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [{'region': 'riot', 'description': 'Obtained through Influencer audience giveaways.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 84028, 'name': 'True Damage Akali', 'contentId': 'accb7f93-8389-43cb-a3ec-796e491cf64c', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/84/84028.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': []}, {'id': 84029, 'name': 'True Damage Akali', 'contentId': '21cd0f9b-089c-4341-a11d-0cabc5b42a58', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/84/84029.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'TENCENT', 'rarity': 1}]}, {'id': 84030, 'name': 'True Damage Akali', 'contentId': 'f91bf6c0-9565-4cab-bacc-b461579a5cc0', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/84/84030.png', 'colors': ['#ECF9F8', '#ECF9F8'], 'descriptions': [], 'rarities': []}, {'id': 84031, 'name': 'True Damage Akali', 'contentId': '93226781-b880-4872-a4d2-961c77ef6bee', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/84/84031.png', 'colors': ['#BD1357', '#19182A'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a Loot exclusive in 2019.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 120}], 'description': 'After the worldwide success of K/DA, Akali made it her mission to recruit accomplished and up-and-coming artists to form a new supergroup: True Damage. Never before has a crew been comprised of such diverse talent, ranging from natural lyricists to renowned producers to legendary vocalists—all ready to disrupt the music industry.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 58033
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 58033, 'contentId': '07cbefcd-9797-4ea8-9995-ff0596e77ed7', 'isBase': False, 'name': 'Dawnbringer Renekton', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Renekton/Skins/Skin33/Images/renekton_splash_centered_33.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Renekton/Skins/Skin33/Images/renekton_splash_uncentered_33.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Renekton/Skins/Skin33/Images/renekton_splash_tile_33.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Renekton/Skins/Skin33/RenektonLoadscreen_33.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/58/58033.png', 'chromas': [{'id': 58034, 'name': 'Dawnbringer Renekton', 'contentId': 'd3c0b1be-6bec-4ac0-b453-9ecdf7121ab4', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/58/58034.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2023.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 58035, 'name': 'Dawnbringer Renekton', 'contentId': 'f903172f-ff44-4a0e-94cb-a189a3a2ad4d', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/58/58035.png', 'colors': ['#DF9117', '#DF9117'], 'descriptions': [], 'rarities': []}, {'id': 58036, 'name': 'Dawnbringer Renekton', 'contentId': '7fb38ee8-5adc-4575-8a96-66fcd31e5117', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/58/58036.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': []}, {'id': 58037, 'name': 'Dawnbringer Renekton', 'contentId': '24e1c425-30f5-4d50-9955-56f02b792e2a', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/58/58037.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'TENCENT', 'rarity': 1}]}, {'id': 58038, 'name': 'Dawnbringer Renekton', 'contentId': '72f9e8ef-a8fc-4e51-b35a-78ccdb52400d', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/58/58038.png', 'colors': ['#54209B', '#54209B'], 'descriptions': [], 'rarities': []}, {'id': 58039, 'name': 'Dawnbringer Renekton', 'contentId': '5346080d-ebe2-428b-a8c5-9587fcbfdc74', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/58/58039.png', 'colors': ['#73BFBE', '#73BFBE'], 'descriptions': [], 'rarities': []}, {'id': 58040, 'name': 'Dawnbringer Renekton', 'contentId': 'cd5e61f8-b822-48c1-9504-bd05f5297736', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/58/58040.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': []}, {'id': 58041, 'name': 'Dawnbringer Renekton', 'contentId': '510167f5-9077-4c15-9ecb-169ee16f7a36', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/58/58041.png', 'colors': ['#ECF9F8', '#ECF9F8'], 'descriptions': [], 'rarities': []}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 193}], 'description': 'In the aftermath of chaos slaying order, two brothers were born: the embodiments of cause, Renekton, and effect, Nasus. As executioner for the Dawnbringers, Renekton has become more frenzied from battling his kin, but he still holds to his view that everything must have a purpose. For what function does chaos serve, if not to cause more chaos?'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 62002
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 62002, 'contentId': 'b77d48e3-db26-4c2c-8b31-b78638cbd0ac', 'isBase': False, 'name': 'General Wukong', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/MonkeyKing/Skins/Skin02/Images/monkeyking_splash_centered_2.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/MonkeyKing/Skins/Skin02/Images/monkeyking_splash_uncentered_2.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/MonkeyKing/Skins/Skin02/Images/monkeyking_splash_tile_2.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/MonkeyKing/Skins/Skin02/MonkeyKingLoadScreen_2.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 167}], 'description': 'Once a mischievous scout, Wukong led his sworn brethren on a treacherous journey to the western hinterlands, where he attained martial enlightenment. Now promoted to the rank of general, he commands a host of fearsome warriors, destined to cleanse the realm of monsters and demons alike.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 14003
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 14003, 'contentId': '277e69dc-f3b0-4dbc-af88-ec1a83df1f25', 'isBase': False, 'name': 'Lumberjack Sion', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Sion/Skins/Skin03/Images/sion_splash_centered_3.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Sion/Skins/Skin03/Images/sion_splash_uncentered_3.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Sion/Skins/Skin03/Images/sion_splash_tile_3.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Sion/Skins/Skin03/SionLoadScreen_3.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 100}], 'description': "He's a lumberjack, and he's okay."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 78005
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 78005, 'contentId': '1ef8a11d-6489-4022-ba83-452b131da19f', 'isBase': False, 'name': 'Battle Regalia Poppy', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Poppy/Skins/Skin05/Images/poppy_splash_centered_5.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Poppy/Skins/Skin05/Images/poppy_splash_uncentered_5.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Poppy/Skins/Skin05/Images/poppy_splash_tile_5.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Poppy/Skins/Skin05/PoppyLoadScreen_5.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/78/78005.png', 'chromas': [{'id': 78008, 'name': 'Battle Regalia Poppy', 'contentId': 'b662f00e-0b5b-4373-92a1-4eb0447f12dc', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/78/78008.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 78009, 'name': 'Battle Regalia Poppy', 'contentId': '9ecc088d-8c34-4a48-9725-d03ddfca6643', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/78/78009.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 78010, 'name': 'Battle Regalia Poppy', 'contentId': '2a0dc304-37c4-41a5-a8d6-63a22556935f', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/78/78010.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 78011, 'name': 'Battle Regalia Poppy', 'contentId': 'ce050793-9899-4d58-b790-acfd630e6b4f', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/78/78011.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 78012, 'name': 'Battle Regalia Poppy', 'contentId': 'cf819afa-41df-4531-94bc-6ba031113d81', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/78/78012.png', 'colors': ['#9C68D7', '#9C68D7'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 78013, 'name': 'Battle Regalia Poppy', 'contentId': '54b87974-5405-4aa1-8a2e-f2542aeb78f5', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/78/78013.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 167}], 'description': None}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 90038
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 90038, 'contentId': '0a45c80e-6c37-4cbd-bf0f-c615cac05c76', 'isBase': False, 'name': 'Three Honors Malzahar', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Malzahar/Skins/Skin38/Images/malzahar_splash_centered_38.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Malzahar/Skins/Skin38/Images/malzahar_splash_uncentered_38.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Malzahar/Skins/Skin38/Images/malzahar_splash_tile_38.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Malzahar/Skins/Skin38/MalzaharLoadscreen_38.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 176}], 'description': "As the Order of Solace's leader, Malzahar wants to extend a gentle, guiding hand to all. It was only by following the tenets of the Three Honors that he was able to escape from a pit of cynicism, and while apathy may reign supreme, Malzahar will do whatever it takes to build a better world."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 238011
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 238011, 'contentId': '7cad0716-5a59-41f6-85fa-c10cae6b76c2', 'isBase': False, 'name': 'Death Sworn Zed', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Zed/Skins/Skin11/Images/zed_splash_centered_11.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Zed/Skins/Skin11/Images/zed_splash_uncentered_11.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Zed/Skins/Skin11/Images/zed_splash_tile_11.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Zed/Skins/Skin11/ZedLoadscreen_11.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': True, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 87}], 'description': "Having lived his life in the shadows, Zed's eventual return from the dead should have come as no surprise to anyone. A master of unliving darkness, he is only too happy to spend all eternity spilling mortal blood, as one of the Death Sworn."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19001
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 19001, 'contentId': '996ec966-bfde-4151-8d1b-5b4101219876', 'isBase': False, 'name': 'Grey Warwick', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Warwick/Skins/Skin01/Images/warwick_splash_centered_1.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Warwick/Skins/Skin01/Images/warwick_splash_uncentered_1.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Warwick/Skins/Skin01/Images/warwick_splash_tile_1.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Warwick/Skins/Skin01/WarwickLoadscreen_1.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Warwick/Skins/Skin01/WarwickLoadscreen_1_LE.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': True, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/19/19001.png', 'chromas': [{'id': 19018, 'name': 'Grey Warwick', 'contentId': '861dd57b-e87a-4006-84c5-52e3e16e1ffe', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/19/19018.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 2}]}, {'id': 19023, 'name': 'Grey Warwick', 'contentId': '6f363d48-b85d-4bd0-a9fe-d2d58a1d4674', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/19/19023.png', 'colors': ['#5F432B', '#5F432B'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 2}]}, {'id': 19025, 'name': 'Grey Warwick', 'contentId': 'd8428f19-daeb-45c5-ad39-db2be9d111a1', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/19/19025.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 2}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 80}], 'description': 'Warwick was a man, once—a knight of great renown, pledged to serve at the side of Blade Queen Lissandra. In exchange for his loyalty she gifted him a shard of spectral moonlight, cursed to one day pierce his heart and sink him into the depths of depravity.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 14014
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 14014, 'contentId': '135248bd-bfd0-4864-91ea-7026697e66e3', 'isBase': False, 'name': 'Worldbreaker Sion', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Sion/Skins/Skin14/Images/sion_splash_centered_14.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Sion/Skins/Skin14/Images/sion_splash_uncentered_14.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Sion/Skins/Skin14/Images/sion_splash_tile_14.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Sion/Skins/Skin14/SionLoadscreen_14.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/14/14014.png', 'chromas': [{'id': 14015, 'name': 'Worldbreaker Sion', 'contentId': 'bbfafbe3-1a4f-464c-94b5-0aaed2e8a013', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/14/14015.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': []}, {'id': 14016, 'name': 'Worldbreaker Sion', 'contentId': '57669160-4fb0-483e-aa3a-1d465695dd9e', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/14/14016.png', 'colors': ['#ECF9F8', '#ECF9F8'], 'descriptions': [], 'rarities': []}, {'id': 14017, 'name': 'Worldbreaker Sion', 'contentId': 'cc3c0231-291d-48fb-9873-de5da2cccecf', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/14/14017.png', 'colors': ['#6ABBEE', '#6ABBEE'], 'descriptions': [], 'rarities': []}, {'id': 14018, 'name': 'Worldbreaker Sion', 'contentId': '7cb4b0ad-4eca-4e3d-93a9-56bb263d7a88', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/14/14018.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2020.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 14019, 'name': 'Worldbreaker Sion', 'contentId': 'a41cb584-e5ed-40ca-bebd-fdac82345338', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/14/14019.png', 'colors': ['#DF9117', '#DF9117'], 'descriptions': [], 'rarities': []}, {'id': 14020, 'name': 'Worldbreaker Sion', 'contentId': 'f2b9e7cd-1980-4f80-b8cb-9187f2bdffe2', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/14/14020.png', 'colors': ['#9C68D7', '#9C68D7'], 'descriptions': [], 'rarities': []}, {'id': 14021, 'name': 'Worldbreaker Sion', 'contentId': '9ce44ad6-0181-42df-9fd4-e4336019a964', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/14/14021.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': []}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 29}], 'description': 'A once towering fortress toppled during the last annihilation, the thing called Sion was given consciousness by Malzahar, and raised up to become the purest titan of death and destruction. Now, imbued with ravenous hatred, he blasts the terrain with every step, hellbent on culling any instance of life.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 37056
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 37056, 'contentId': 'd3d6f524-933b-48b1-90da-d5f42db9bf18', 'isBase': False, 'name': 'Victorious Sona', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Sona/Skins/Skin56/Images/sona_splash_centered_56.SKINS_Sona_Skin56.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Sona/Skins/Skin56/Images/sona_splash_uncentered_56.SKINS_Sona_Skin56.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Sona/Skins/Skin56/Images/sona_splash_tile_56.SKINS_Sona_Skin56.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Sona/Skins/Skin56/SonaLoadScreen_56.SKINS_Sona_Skin56.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/37/37056.png', 'chromas': [{'id': 37057, 'name': 'Victorious Sona', 'contentId': 'cc7dedc5-cc93-460c-84af-26ad26f1e95a', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/37/37057.png', 'colors': ['#764D43', '#764D43'], 'descriptions': [{'region': 'riot', 'description': "This Chroma was awarded to players who accumulated at least 1000 Split Points and reached Bronze rank in any Summoner's Rift ranked queue in Season 2024 - Split 2"}], 'rarities': [], 'skinAugments': {'borders': {'layer0': [{'contentId': '2d46b649-9330-474d-a4c4-c9c2087bfe13', 'layer': 0, 'priority': 0, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Iron.Season2024RankedRewards.png'}, {'contentId': '765c5e40-a9ab-4f51-ab6b-aa646c409135', 'layer': 0, 'priority': 1, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Bronze.Season2024RankedRewards.png'}, {'contentId': '7ddd4cce-8517-470f-b111-2ddd680d0654', 'layer': 0, 'priority': 2, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Silver.Season2024RankedRewards.png'}, {'contentId': 'd2398e32-b161-4ce7-ba79-6aee571c3c79', 'layer': 0, 'priority': 3, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Gold.Season2024RankedRewards.png'}, {'contentId': '3b990708-8848-4b7b-ac67-a05c120b85fd', 'layer': 0, 'priority': 4, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Platinum.Season2024RankedRewards.png'}, {'contentId': '1cce3040-4541-4ab5-955d-c56dafa3e63c', 'layer': 0, 'priority': 5, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Emerald.Season2024RankedRewards.png'}, {'contentId': 'ff623ab7-593d-4996-84f2-9018630f2312', 'layer': 0, 'priority': 6, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Diamond.Season2024RankedRewards.png'}, {'contentId': '570d327e-7ebd-4342-b108-3e7b9f3b932d', 'layer': 0, 'priority': 7, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Master.Season2024RankedRewards.png'}, {'contentId': 'f8ab9496-d957-4987-b0da-5f749aedab33', 'layer': 0, 'priority': 8, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Grandmaster.Season2024RankedRewards.png'}, {'contentId': '060dddac-b9f2-45c5-a056-e4bd5490f340', 'layer': 0, 'priority': 9, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Challenger.Season2024RankedRewards.png'}]}}}, {'id': 37058, 'name': 'Victorious Sona', 'contentId': '3d4bac4a-5451-4396-9543-cee2623a3ea1', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/37/37058.png', 'colors': ['#AAB5BA', '#AAB5BA'], 'descriptions': [{'region': 'riot', 'description': "This Chroma was awarded to players who accumulated at least 1000 Split Points and reached Silver rank in any Summoner's Rift ranked queue in Season 2024 - Split 2"}], 'rarities': [], 'skinAugments': {'borders': {'layer0': [{'contentId': '2d46b649-9330-474d-a4c4-c9c2087bfe13', 'layer': 0, 'priority': 0, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Iron.Season2024RankedRewards.png'}, {'contentId': '765c5e40-a9ab-4f51-ab6b-aa646c409135', 'layer': 0, 'priority': 1, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Bronze.Season2024RankedRewards.png'}, {'contentId': '7ddd4cce-8517-470f-b111-2ddd680d0654', 'layer': 0, 'priority': 2, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Silver.Season2024RankedRewards.png'}, {'contentId': 'd2398e32-b161-4ce7-ba79-6aee571c3c79', 'layer': 0, 'priority': 3, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Gold.Season2024RankedRewards.png'}, {'contentId': '3b990708-8848-4b7b-ac67-a05c120b85fd', 'layer': 0, 'priority': 4, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Platinum.Season2024RankedRewards.png'}, {'contentId': '1cce3040-4541-4ab5-955d-c56dafa3e63c', 'layer': 0, 'priority': 5, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Emerald.Season2024RankedRewards.png'}, {'contentId': 'ff623ab7-593d-4996-84f2-9018630f2312', 'layer': 0, 'priority': 6, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Diamond.Season2024RankedRewards.png'}, {'contentId': '570d327e-7ebd-4342-b108-3e7b9f3b932d', 'layer': 0, 'priority': 7, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Master.Season2024RankedRewards.png'}, {'contentId': 'f8ab9496-d957-4987-b0da-5f749aedab33', 'layer': 0, 'priority': 8, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Grandmaster.Season2024RankedRewards.png'}, {'contentId': '060dddac-b9f2-45c5-a056-e4bd5490f340', 'layer': 0, 'priority': 9, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Challenger.Season2024RankedRewards.png'}]}}}, {'id': 37059, 'name': 'Victorious Sona', 'contentId': 'c1c96856-eb6c-41d8-98eb-f57457a2a6a4', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/37/37059.png', 'colors': ['#D9C445', '#D9C445'], 'descriptions': [{'region': 'riot', 'description': "This Chroma was awarded to players who accumulated at least 80 Split Points and reached Gold rank in any Summoner's Rift ranked queue in Season 2024 - Split 2"}], 'rarities': [], 'skinAugments': {'borders': {'layer0': [{'contentId': '2d46b649-9330-474d-a4c4-c9c2087bfe13', 'layer': 0, 'priority': 0, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Iron.Season2024RankedRewards.png'}, {'contentId': '765c5e40-a9ab-4f51-ab6b-aa646c409135', 'layer': 0, 'priority': 1, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Bronze.Season2024RankedRewards.png'}, {'contentId': '7ddd4cce-8517-470f-b111-2ddd680d0654', 'layer': 0, 'priority': 2, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Silver.Season2024RankedRewards.png'}, {'contentId': 'd2398e32-b161-4ce7-ba79-6aee571c3c79', 'layer': 0, 'priority': 3, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Gold.Season2024RankedRewards.png'}, {'contentId': '3b990708-8848-4b7b-ac67-a05c120b85fd', 'layer': 0, 'priority': 4, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Platinum.Season2024RankedRewards.png'}, {'contentId': '1cce3040-4541-4ab5-955d-c56dafa3e63c', 'layer': 0, 'priority': 5, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Emerald.Season2024RankedRewards.png'}, {'contentId': 'ff623ab7-593d-4996-84f2-9018630f2312', 'layer': 0, 'priority': 6, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Diamond.Season2024RankedRewards.png'}, {'contentId': '570d327e-7ebd-4342-b108-3e7b9f3b932d', 'layer': 0, 'priority': 7, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Master.Season2024RankedRewards.png'}, {'contentId': 'f8ab9496-d957-4987-b0da-5f749aedab33', 'layer': 0, 'priority': 8, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Grandmaster.Season2024RankedRewards.png'}, {'contentId': '060dddac-b9f2-45c5-a056-e4bd5490f340', 'layer': 0, 'priority': 9, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Challenger.Season2024RankedRewards.png'}]}}}, {'id': 37060, 'name': 'Victorious Sona', 'contentId': '55200faf-2519-41e9-8521-04b21d5ccb4e', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/37/37060.png', 'colors': ['#55CDE5', '#55CDE5'], 'descriptions': [{'region': 'riot', 'description': "This Chroma was awarded to players who accumulated at least 80 Split Points and reached Platinum rank in any Summoner's Rift ranked queue in Season 2024 - Split 2"}], 'rarities': [{'region': 'riot', 'rarity': 1}], 'skinAugments': {'borders': {'layer0': [{'contentId': '2d46b649-9330-474d-a4c4-c9c2087bfe13', 'layer': 0, 'priority': 0, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Iron.Season2024RankedRewards.png'}, {'contentId': '765c5e40-a9ab-4f51-ab6b-aa646c409135', 'layer': 0, 'priority': 1, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Bronze.Season2024RankedRewards.png'}, {'contentId': '7ddd4cce-8517-470f-b111-2ddd680d0654', 'layer': 0, 'priority': 2, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Silver.Season2024RankedRewards.png'}, {'contentId': 'd2398e32-b161-4ce7-ba79-6aee571c3c79', 'layer': 0, 'priority': 3, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Gold.Season2024RankedRewards.png'}, {'contentId': '3b990708-8848-4b7b-ac67-a05c120b85fd', 'layer': 0, 'priority': 4, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Platinum.Season2024RankedRewards.png'}, {'contentId': '1cce3040-4541-4ab5-955d-c56dafa3e63c', 'layer': 0, 'priority': 5, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Emerald.Season2024RankedRewards.png'}, {'contentId': 'ff623ab7-593d-4996-84f2-9018630f2312', 'layer': 0, 'priority': 6, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Diamond.Season2024RankedRewards.png'}, {'contentId': '570d327e-7ebd-4342-b108-3e7b9f3b932d', 'layer': 0, 'priority': 7, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Master.Season2024RankedRewards.png'}, {'contentId': 'f8ab9496-d957-4987-b0da-5f749aedab33', 'layer': 0, 'priority': 8, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Grandmaster.Season2024RankedRewards.png'}, {'contentId': '060dddac-b9f2-45c5-a056-e4bd5490f340', 'layer': 0, 'priority': 9, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Challenger.Season2024RankedRewards.png'}]}}}, {'id': 37061, 'name': 'Victorious Sona', 'contentId': 'aaa6fd8f-75df-4644-8c4b-5fbfd903d6bc', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/37/37061.png', 'colors': ['#26854C', '#26854C'], 'descriptions': [{'region': 'riot', 'description': "This Chroma was awarded to players who accumulated at least 80 Split Points and reached Emerald rank in any Summoner's Rift ranked queue in Season 2024 - Split 2"}], 'rarities': [{'region': 'riot', 'rarity': 2}], 'skinAugments': {'borders': {'layer0': [{'contentId': '2d46b649-9330-474d-a4c4-c9c2087bfe13', 'layer': 0, 'priority': 0, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Iron.Season2024RankedRewards.png'}, {'contentId': '765c5e40-a9ab-4f51-ab6b-aa646c409135', 'layer': 0, 'priority': 1, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Bronze.Season2024RankedRewards.png'}, {'contentId': '7ddd4cce-8517-470f-b111-2ddd680d0654', 'layer': 0, 'priority': 2, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Silver.Season2024RankedRewards.png'}, {'contentId': 'd2398e32-b161-4ce7-ba79-6aee571c3c79', 'layer': 0, 'priority': 3, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Gold.Season2024RankedRewards.png'}, {'contentId': '3b990708-8848-4b7b-ac67-a05c120b85fd', 'layer': 0, 'priority': 4, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Platinum.Season2024RankedRewards.png'}, {'contentId': '1cce3040-4541-4ab5-955d-c56dafa3e63c', 'layer': 0, 'priority': 5, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Emerald.Season2024RankedRewards.png'}, {'contentId': 'ff623ab7-593d-4996-84f2-9018630f2312', 'layer': 0, 'priority': 6, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Diamond.Season2024RankedRewards.png'}, {'contentId': '570d327e-7ebd-4342-b108-3e7b9f3b932d', 'layer': 0, 'priority': 7, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Master.Season2024RankedRewards.png'}, {'contentId': 'f8ab9496-d957-4987-b0da-5f749aedab33', 'layer': 0, 'priority': 8, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Grandmaster.Season2024RankedRewards.png'}, {'contentId': '060dddac-b9f2-45c5-a056-e4bd5490f340', 'layer': 0, 'priority': 9, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Challenger.Season2024RankedRewards.png'}]}}}, {'id': 37062, 'name': 'Victorious Sona', 'contentId': '9a1a790b-702c-410e-a3ce-0d498fc01bc8', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/37/37062.png', 'colors': ['#65337F', '#65337F'], 'descriptions': [{'region': 'riot', 'description': "This Chroma was awarded to players who accumulated at least 80 Split Points and reached Diamond rank in any Summoner's Rift ranked queue in Season 2024 - Split 2"}], 'rarities': [{'region': 'riot', 'rarity': 3}], 'skinAugments': {'borders': {'layer0': [{'contentId': '2d46b649-9330-474d-a4c4-c9c2087bfe13', 'layer': 0, 'priority': 0, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Iron.Season2024RankedRewards.png'}, {'contentId': '765c5e40-a9ab-4f51-ab6b-aa646c409135', 'layer': 0, 'priority': 1, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Bronze.Season2024RankedRewards.png'}, {'contentId': '7ddd4cce-8517-470f-b111-2ddd680d0654', 'layer': 0, 'priority': 2, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Silver.Season2024RankedRewards.png'}, {'contentId': 'd2398e32-b161-4ce7-ba79-6aee571c3c79', 'layer': 0, 'priority': 3, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Gold.Season2024RankedRewards.png'}, {'contentId': '3b990708-8848-4b7b-ac67-a05c120b85fd', 'layer': 0, 'priority': 4, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Platinum.Season2024RankedRewards.png'}, {'contentId': '1cce3040-4541-4ab5-955d-c56dafa3e63c', 'layer': 0, 'priority': 5, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Emerald.Season2024RankedRewards.png'}, {'contentId': 'ff623ab7-593d-4996-84f2-9018630f2312', 'layer': 0, 'priority': 6, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Diamond.Season2024RankedRewards.png'}, {'contentId': '570d327e-7ebd-4342-b108-3e7b9f3b932d', 'layer': 0, 'priority': 7, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Master.Season2024RankedRewards.png'}, {'contentId': 'f8ab9496-d957-4987-b0da-5f749aedab33', 'layer': 0, 'priority': 8, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Grandmaster.Season2024RankedRewards.png'}, {'contentId': '060dddac-b9f2-45c5-a056-e4bd5490f340', 'layer': 0, 'priority': 9, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Challenger.Season2024RankedRewards.png'}]}}}, {'id': 37063, 'name': 'Victorious Sona', 'contentId': 'e2ec580a-94e4-458d-8e34-4f657fcf783a', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/37/37063.png', 'colors': ['#9D228D', '#9D228D'], 'descriptions': [{'region': 'riot', 'description': "This Chroma was awarded to players who accumulated at least 80 Split Points and reached Master rank in any Summoner's Rift ranked queue in Season 2024 - Split 2"}], 'rarities': [{'region': 'riot', 'rarity': 4}], 'skinAugments': {'borders': {'layer0': [{'contentId': '2d46b649-9330-474d-a4c4-c9c2087bfe13', 'layer': 0, 'priority': 0, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Iron.Season2024RankedRewards.png'}, {'contentId': '765c5e40-a9ab-4f51-ab6b-aa646c409135', 'layer': 0, 'priority': 1, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Bronze.Season2024RankedRewards.png'}, {'contentId': '7ddd4cce-8517-470f-b111-2ddd680d0654', 'layer': 0, 'priority': 2, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Silver.Season2024RankedRewards.png'}, {'contentId': 'd2398e32-b161-4ce7-ba79-6aee571c3c79', 'layer': 0, 'priority': 3, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Gold.Season2024RankedRewards.png'}, {'contentId': '3b990708-8848-4b7b-ac67-a05c120b85fd', 'layer': 0, 'priority': 4, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Platinum.Season2024RankedRewards.png'}, {'contentId': '1cce3040-4541-4ab5-955d-c56dafa3e63c', 'layer': 0, 'priority': 5, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Emerald.Season2024RankedRewards.png'}, {'contentId': 'ff623ab7-593d-4996-84f2-9018630f2312', 'layer': 0, 'priority': 6, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Diamond.Season2024RankedRewards.png'}, {'contentId': '570d327e-7ebd-4342-b108-3e7b9f3b932d', 'layer': 0, 'priority': 7, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Master.Season2024RankedRewards.png'}, {'contentId': 'f8ab9496-d957-4987-b0da-5f749aedab33', 'layer': 0, 'priority': 8, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Grandmaster.Season2024RankedRewards.png'}, {'contentId': '060dddac-b9f2-45c5-a056-e4bd5490f340', 'layer': 0, 'priority': 9, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Challenger.Season2024RankedRewards.png'}]}}}, {'id': 37064, 'name': 'Victorious Sona', 'contentId': '56de5202-abf8-4534-9b64-65ea684ac3b9', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/37/37064.png', 'colors': ['#B31316', '#B31316'], 'descriptions': [{'region': 'riot', 'description': "This Chroma was awarded to players who accumulated at least 80 Split Points and reached Grandmaster rank in any Summoner's Rift ranked queue in Season 2024 - Split 2"}], 'rarities': [{'region': 'riot', 'rarity': 5}], 'skinAugments': {'borders': {'layer0': [{'contentId': '2d46b649-9330-474d-a4c4-c9c2087bfe13', 'layer': 0, 'priority': 0, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Iron.Season2024RankedRewards.png'}, {'contentId': '765c5e40-a9ab-4f51-ab6b-aa646c409135', 'layer': 0, 'priority': 1, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Bronze.Season2024RankedRewards.png'}, {'contentId': '7ddd4cce-8517-470f-b111-2ddd680d0654', 'layer': 0, 'priority': 2, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Silver.Season2024RankedRewards.png'}, {'contentId': 'd2398e32-b161-4ce7-ba79-6aee571c3c79', 'layer': 0, 'priority': 3, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Gold.Season2024RankedRewards.png'}, {'contentId': '3b990708-8848-4b7b-ac67-a05c120b85fd', 'layer': 0, 'priority': 4, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Platinum.Season2024RankedRewards.png'}, {'contentId': '1cce3040-4541-4ab5-955d-c56dafa3e63c', 'layer': 0, 'priority': 5, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Emerald.Season2024RankedRewards.png'}, {'contentId': 'ff623ab7-593d-4996-84f2-9018630f2312', 'layer': 0, 'priority': 6, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Diamond.Season2024RankedRewards.png'}, {'contentId': '570d327e-7ebd-4342-b108-3e7b9f3b932d', 'layer': 0, 'priority': 7, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Master.Season2024RankedRewards.png'}, {'contentId': 'f8ab9496-d957-4987-b0da-5f749aedab33', 'layer': 0, 'priority': 8, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Grandmaster.Season2024RankedRewards.png'}, {'contentId': '060dddac-b9f2-45c5-a056-e4bd5490f340', 'layer': 0, 'priority': 9, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Challenger.Season2024RankedRewards.png'}]}}}, {'id': 37065, 'name': 'Victorious Sona', 'contentId': '47a2e908-4606-4ba5-8928-cfa9b3e448fd', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/37/37065.png', 'colors': ['#1D62C8', '#1D62C8'], 'descriptions': [{'region': 'riot', 'description': "This Chroma was awarded to players who accumulated at least 80 Split Points and reached Challenger rank in any Summoner's Rift ranked queue in Season 2024 - Split 2"}], 'rarities': [{'region': 'riot', 'rarity': 5}], 'skinAugments': {'borders': {'layer0': [{'contentId': '2d46b649-9330-474d-a4c4-c9c2087bfe13', 'layer': 0, 'priority': 0, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Iron.Season2024RankedRewards.png'}, {'contentId': '765c5e40-a9ab-4f51-ab6b-aa646c409135', 'layer': 0, 'priority': 1, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Bronze.Season2024RankedRewards.png'}, {'contentId': '7ddd4cce-8517-470f-b111-2ddd680d0654', 'layer': 0, 'priority': 2, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Silver.Season2024RankedRewards.png'}, {'contentId': 'd2398e32-b161-4ce7-ba79-6aee571c3c79', 'layer': 0, 'priority': 3, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Gold.Season2024RankedRewards.png'}, {'contentId': '3b990708-8848-4b7b-ac67-a05c120b85fd', 'layer': 0, 'priority': 4, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Platinum.Season2024RankedRewards.png'}, {'contentId': '1cce3040-4541-4ab5-955d-c56dafa3e63c', 'layer': 0, 'priority': 5, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Emerald.Season2024RankedRewards.png'}, {'contentId': 'ff623ab7-593d-4996-84f2-9018630f2312', 'layer': 0, 'priority': 6, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Diamond.Season2024RankedRewards.png'}, {'contentId': '570d327e-7ebd-4342-b108-3e7b9f3b932d', 'layer': 0, 'priority': 7, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Master.Season2024RankedRewards.png'}, {'contentId': 'f8ab9496-d957-4987-b0da-5f749aedab33', 'layer': 0, 'priority': 8, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Grandmaster.Season2024RankedRewards.png'}, {'contentId': '060dddac-b9f2-45c5-a056-e4bd5490f340', 'layer': 0, 'priority': 9, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Challenger.Season2024RankedRewards.png'}]}}}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 7}], 'skinAugments': {'borders': {'layer0': [{'contentId': '2d46b649-9330-474d-a4c4-c9c2087bfe13', 'layer': 0, 'priority': 0, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Iron.Season2024RankedRewards.png'}, {'contentId': '765c5e40-a9ab-4f51-ab6b-aa646c409135', 'layer': 0, 'priority': 1, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Bronze.Season2024RankedRewards.png'}, {'contentId': '7ddd4cce-8517-470f-b111-2ddd680d0654', 'layer': 0, 'priority': 2, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Silver.Season2024RankedRewards.png'}, {'contentId': 'd2398e32-b161-4ce7-ba79-6aee571c3c79', 'layer': 0, 'priority': 3, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Gold.Season2024RankedRewards.png'}, {'contentId': '3b990708-8848-4b7b-ac67-a05c120b85fd', 'layer': 0, 'priority': 4, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Platinum.Season2024RankedRewards.png'}, {'contentId': '1cce3040-4541-4ab5-955d-c56dafa3e63c', 'layer': 0, 'priority': 5, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Emerald.Season2024RankedRewards.png'}, {'contentId': 'ff623ab7-593d-4996-84f2-9018630f2312', 'layer': 0, 'priority': 6, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Diamond.Season2024RankedRewards.png'}, {'contentId': '570d327e-7ebd-4342-b108-3e7b9f3b932d', 'layer': 0, 'priority': 7, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Master.Season2024RankedRewards.png'}, {'contentId': 'f8ab9496-d957-4987-b0da-5f749aedab33', 'layer': 0, 'priority': 8, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Grandmaster.Season2024RankedRewards.png'}, {'contentId': '060dddac-b9f2-45c5-a056-e4bd5490f340', 'layer': 0, 'priority': 9, 'borderPath': '/lol-game-data/assets/ASSETS/Rewards/Ranked/Season2024EOS/Season2024EOS_Victorious_Border_Challenger.Season2024RankedRewards.png'}]}}, 'description': 'Victorious Sona was earned by players who accumulated enough split points in Ranked. Luring the unsuspecting into a false sense of ease with her melodies, Sona is as quick to pluck her allies from the brink of death as she is to send her foes to their grand finale.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 8001
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 8001, 'contentId': '3da824a4-1f14-4a3d-ac3a-bd25b4370afe', 'isBase': False, 'name': 'Count Vladimir', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Vladimir/Skins/Skin01/Images/vladimir_splash_centered_1.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Vladimir/Skins/Skin01/Images/vladimir_splash_uncentered_1.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Vladimir/Skins/Skin01/Images/vladimir_splash_tile_1.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Vladimir/Skins/Skin01/VladimirLoadScreen_1.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': True, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 71}], 'description': "Once upon a time, in the heart of a dark and gloomy castle, there lived an eternally youthful count who the people greatly feared. Every night, a terrible red mist would seep into the villages, and some poor soul would vanish without a trace—another life lost to Vladimir's growing appetites."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 37057
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Victorious Sona (Bronze) for champion Sona
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 37058
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Victorious Sona (Silver) for champion Sona
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 24003
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 24003, 'contentId': 'ba88c164-738c-40b1-b118-7a2ce93209c6', 'isBase': False, 'name': 'Angler Jax', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Jax/Skins/Skin03/Images/jax_splash_centered_3.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Jax/Skins/Skin03/Images/jax_splash_uncentered_3.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Jax/Skins/Skin03/Images/jax_splash_tile_3.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Jax/Skins/Skin03/JaxLoadscreen_3.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': True, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 100}], 'description': 'Nothing quite like the peace of the great outdoors, waiting for that perfect bite—and then violently ripping a fish out of the water before hitting it with his fishing rod.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 37059
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Victorious Sona (Gold) for champion Sona
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 126024
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 126024, 'contentId': '1c0cefc5-5d3e-408c-96ce-0b60806143ec', 'isBase': False, 'name': 'Arcane Inventor Jayce', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Jayce/Skins/Skin24/Images/jayce_splash_centered_24.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Jayce/Skins/Skin24/Images/jayce_splash_uncentered_24.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Jayce/Skins/Skin24/Images/jayce_splash_tile_24.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Jayce/Skins/Skin24/JayceLoadscreen_24.jpg', 'skinType': '', 'rarity': 'kRare', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 152}], 'description': 'Celebrating the RiotX Arcane event.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 157003
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 157003, 'contentId': 'ba7e7e39-3bd8-490e-947b-c973d86eb297', 'isBase': False, 'name': 'Blood Moon Yasuo', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Yasuo/Skins/Skin03/Images/yasuo_splash_centered_3.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Yasuo/Skins/Skin03/Images/yasuo_splash_uncentered_3.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Yasuo/Skins/Skin03/Images/yasuo_splash_tile_3.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Yasuo/Skins/Skin03/YasuoLoadScreen_3.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 12}], 'description': "Ceremonial executioner of the Blood Moon cult, Yasuo's blade is inhabited by an insidious, bloodthirsty demon whose hunger for death can never be satisfied. This suits Yasuo well, for he is possessed of an inner darkness even deeper than the creature whispering at his side."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19018
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Grey Warwick (Color: #2756CE) for champion Warwick
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29003
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 29003, 'contentId': 'fe896ef1-2a0c-49d4-a1f3-a8c8a9c14176', 'isBase': False, 'name': 'Medieval Twitch', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Twitch/Skins/Skin03/Images/twitch_splash_centered_3.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Twitch/Skins/Skin03/Images/twitch_splash_uncentered_3.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Twitch/Skins/Skin03/Images/twitch_splash_tile_3.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Twitch/Skins/Skin03/TwitchLoadScreen_3.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Twitch/Skins/Skin03/TwitchLoadScreen_3_LE.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': True, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/29/29003.png', 'chromas': [{'id': 29021, 'name': 'Medieval Twitch', 'contentId': '2717bff4-051d-4cd7-8280-c15cafb51e26', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/29/29021.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 2}]}, {'id': 29023, 'name': 'Medieval Twitch', 'contentId': '8f0562cb-7c6b-4f99-91f0-db41ccb9221b', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/29/29023.png', 'colors': ['#9C68D7', '#9C68D7'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 2}]}, {'id': 29024, 'name': 'Medieval Twitch', 'contentId': '48e5636f-6d6a-4372-b594-6d73cc56380e', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/29/29024.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 2}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 114}], 'description': 'An assassin-for-hire living beneath Dread Fortress Zaun, Twitch is a disgusting malefactor who has tipped every one of his killing implements in moist, toxic garbage. He excitedly awaits each contract, eager to kill, steal, and destroy as much as he can.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4045
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[93m!←[0m←[97m] Skin not found in database: 4045, champion_id: 4, champion_name: Twisted Fate
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4046
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[93m!←[0m←[97m] Skin not found in database: 4046, champion_id: 4, champion_name: Twisted Fate
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4047
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[93m!←[0m←[97m] Skin not found in database: 4047, champion_id: 4, champion_name: Twisted Fate
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19023
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Grey Warwick (Color: #5F432B) for champion Warwick
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4048
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[93m!←[0m←[97m] Skin not found in database: 4048, champion_id: 4, champion_name: Twisted Fate
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 4049
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up champion name for ID: 4
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found champion name: Twisted Fate for ID: 4
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[93m!←[0m←[97m] Skin not found in database: 4049, champion_id: 4, champion_name: Twisted Fate
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 19025
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Grey Warwick (Color: #E58BA5) for champion Warwick
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55001
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 55001, 'contentId': 'bdf82d63-e04b-4468-bec6-27c01dab8630', 'isBase': False, 'name': 'Mercenary Katarina', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin01/Images/katarina_splash_centered_1.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin01/Images/katarina_splash_uncentered_1.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin01/Images/katarina_splash_tile_1.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin01/KatarinaLoadScreen_1.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 167}], 'description': None}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 35033
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 35033, 'contentId': '64006c9a-1881-4d3f-8a31-d2ddcb06ceb9', 'isBase': False, 'name': 'Winterblessed Shaco', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Shaco/Skins/Skin33/Images/shaco_splash_centered_33.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Shaco/Skins/Skin33/Images/shaco_splash_uncentered_33.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Shaco/Skins/Skin33/Images/shaco_splash_tile_33.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Shaco/Skins/Skin33/ShacoLoadscreen_33.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Shaco/Skins/Skin33/ShacoLoadscreen_33_LE.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/35/35033.png', 'chromas': [{'id': 35034, 'name': 'Winterblessed Shaco', 'contentId': 'f834df7d-58e0-4acc-8f27-a52c5e634259', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/35/35034.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2022.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 35035, 'name': 'Winterblessed Shaco', 'contentId': '4e8caa3f-fea0-4d4f-965b-84a2ac5a2343', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/35/35035.png', 'colors': ['#73BFBE', '#73BFBE'], 'descriptions': [], 'rarities': []}, {'id': 35036, 'name': 'Winterblessed Shaco', 'contentId': '8cd738ca-6d1c-4ddf-9883-182ab913e622', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/35/35036.png', 'colors': ['#FFEE59', '#FFEE59'], 'descriptions': [], 'rarities': []}, {'id': 35037, 'name': 'Winterblessed Shaco', 'contentId': '8ef44953-3dda-4078-be6f-0dd4b9fa6e4d', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/35/35037.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': []}, {'id': 35038, 'name': 'Winterblessed Shaco', 'contentId': '1df0b265-d55c-4bbe-b2e8-4f936dfb3d6d', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/35/35038.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'TENCENT', 'rarity': 1}]}, {'id': 35039, 'name': 'Winterblessed Shaco', 'contentId': '2c96d245-f6bd-4d3b-a41b-7f4f5dbbb8c3', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/35/35039.png', 'colors': ['#9C68D7', '#9C68D7'], 'descriptions': [], 'rarities': []}, {'id': 35040, 'name': 'Winterblessed Shaco', 'contentId': 'df47ab2d-490f-43c0-8d4e-684a485a7523', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/35/35040.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': []}, {'id': 35041, 'name': 'Winterblessed Shaco', 'contentId': 'c28f5da5-025a-49df-b9ae-5cd1b12cc8db', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/35/35041.png', 'colors': ['#ECF9F8', '#ECF9F8'], 'descriptions': [], 'rarities': []}, {'id': 35042, 'name': 'Winterblessed Shaco', 'contentId': '3e6ab46c-23fb-4bb0-8790-4b5bd7e60e37', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/35/35042.png', 'colors': ['#BF2020', '#368B25'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a Loot exclusive in the Season 2023 event.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 187}], 'description': 'The Automaton Shaco felt nothing toward the quarreling leaders until Polaris snapped her fingers and ordered her “gifts” to corner the trio. “Winter is cruel,” she boomed, “and warmth is meant to be shared. Let this punishment serve as a warning.” In a flurry of snow, Polaris disappeared, leaving the Automaton and Beast to dispense her judgment.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 23004
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 23004, 'contentId': 'c1581b9d-96bf-468a-a45d-2887f670567e', 'isBase': False, 'name': 'Demonblade Tryndamere', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Tryndamere/Skins/Skin04/Images/tryndamere_splash_centered_4.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Tryndamere/Skins/Skin04/Images/tryndamere_splash_uncentered_4.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Tryndamere/Skins/Skin04/Images/tryndamere_splash_tile_4.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Tryndamere/Skins/Skin04/TryndamereLoadScreen_4.jpg', 'skinType': '', 'rarity': 'kLegendary', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 180}], 'description': "Tryndamere's cursed blade corrupted him from a once honorable barbarian into a rampaging monster. Now in a frenzy to sate his endless hunger, this demonic sword drives him to slay all. With each kill another fraction of his humanity is lost and succumbing further into the demonic sword's madness."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 103005
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 103005, 'contentId': '2076567d-d90b-46d3-8799-3ebab3e365ce', 'isBase': False, 'name': 'Challenger Ahri', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Ahri/Skins/Skin05/Images/ahri_splash_centered_5.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Ahri/Skins/Skin05/Images/ahri_splash_uncentered_5.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Ahri/Skins/Skin05/Images/ahri_splash_tile_5.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Ahri/Skins/Skin05/AhriLoadscreen_5.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Ahri/Skins/Skin05/AhriLoadscreen_5_LE.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': True, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/103/103005.png', 'chromas': [{'id': 103018, 'name': 'Challenger Ahri', 'contentId': '00319c18-ab33-41d4-aa15-00b718fd1d71', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/103/103018.png', 'colors': ['#FFC948', '#FFC948'], 'descriptions': [{'region': 'riot', 'description': 'Loot exclusive gained during the 2019 MSI Event.'}], 'rarities': [{'region': 'riot', 'rarity': 2}]}, {'id': 103056, 'name': 'Challenger Ahri', 'contentId': '562da728-3713-4dd6-b107-23bd809bb047', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/103/103056.png', 'colors': ['#2E38C4', '#C8003F'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released in the 2021 Ahri-versary event.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 9}], 'description': 'Commemorating the 2015 Season 5 start.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55005
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 55005, 'contentId': '5a260596-10b2-4417-a3dd-e420c636c25a', 'isBase': False, 'name': 'High Command Katarina', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin05/Images/katarina_splash_centered_5.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin05/Images/katarina_splash_uncentered_5.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin05/Images/katarina_splash_tile_5.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin05/KatarinaLoadScreen_5.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 177}], 'description': None}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29021
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Medieval Twitch (Color: #2756CE) for champion Twitch
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 7005
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 7005, 'contentId': 'b7faf0b7-26d0-4daa-a594-1a8a97478408', 'isBase': False, 'name': 'Elderwood LeBlanc', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Leblanc/Skins/Skin05/Images/leblanc_splash_centered_5.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Leblanc/Skins/Skin05/Images/leblanc_splash_uncentered_5.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Leblanc/Skins/Skin05/Images/leblanc_splash_tile_5.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Leblanc/Skins/Skin05/LeblancLoadScreen_5.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 58}], 'description': "When the Coven first rose to power, they craved the living enchantments of the Elderwood—a tool, they believed, for the resurrection of their dark patrons. Thus did they fall upon the great forest, and slaughter its children… until a lone sylvan stood against them, stealing the witches' lives, their magic, and even their names."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29023
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Medieval Twitch (Color: #9C68D7) for champion Twitch
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 29024
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Medieval Twitch (Color: #27211C) for champion Twitch
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 92003
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 92003, 'contentId': 'fc0faa88-cb03-4db3-8231-2a5e88cf676a', 'isBase': False, 'name': 'Battle Bunny Riven', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Riven/Skins/Skin03/Images/riven_splash_centered_3.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Riven/Skins/Skin03/Images/riven_splash_uncentered_3.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Riven/Skins/Skin03/Images/riven_splash_tile_3.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Riven/Skins/Skin03/RivenLoadScreen_3.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92003.png', 'chromas': [{'id': 92008, 'name': 'Battle Bunny Riven', 'contentId': '12217994-4485-44d9-8f04-4ce542b2c369', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92008.png', 'colors': ['#FFEE59', '#FFEE59'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 92009, 'name': 'Battle Bunny Riven', 'contentId': '58e38d69-5bec-4886-bdb6-35330f5c7464', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92009.png', 'colors': ['#85827F', '#85827F'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 92010, 'name': 'Battle Bunny Riven', 'contentId': '0a1665db-29bb-4f69-81c6-fdeb07a5ec8a', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92010.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 92011, 'name': 'Battle Bunny Riven', 'contentId': 'b9398023-842d-408b-8858-f649158820cc', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92011.png', 'colors': ['#FF3CFF', '#FF3CFF'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 92012, 'name': 'Battle Bunny Riven', 'contentId': '21b5df88-e015-4bc6-a321-442d4e3f16b5', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92012.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 92013, 'name': 'Battle Bunny Riven', 'contentId': 'ffb4a5c6-38fd-4771-8ca0-6046e1834a19', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92013.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [], 'rarities': [{'region': 'VN', 'rarity': 1}, {'region': 'riot', 'rarity': 0}]}, {'id': 92014, 'name': 'Battle Bunny Riven', 'contentId': '71ea2341-e2b5-4c19-bae6-6b8f5424e8ac', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92014.png', 'colors': ['#9C68D7', '#9C68D7'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}, {'id': 92015, 'name': 'Battle Bunny Riven', 'contentId': '354e964e-d138-41d3-a014-8ae9c124eafc', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92015.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 0}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 100}], 'description': "Say “Hoppin' Mad” one more time. She dares you."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 236001
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 236001, 'contentId': 'e2da99c8-d4bc-400b-a385-f270aa51dac5', 'isBase': False, 'name': 'Hired Gun Lucian', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Lucian/Skins/Skin01/Images/lucian_splash_centered_1.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Lucian/Skins/Skin01/Images/lucian_splash_uncentered_1.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Lucian/Skins/Skin01/Images/lucian_splash_tile_1.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Lucian/Skins/Skin01/LucianLoadScreen_1.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 167}], 'description': None}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55012
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 55012, 'contentId': '92df77a2-5f01-470a-a261-35e793c71c12', 'isBase': False, 'name': 'Battle Academia Katarina', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin12/Images/katarina_splash_centered_12.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin12/Images/katarina_splash_uncentered_12.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin12/Images/katarina_splash_tile_12.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin12/KatarinaLoadscreen_12.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin12/KatarinaLoadscreen_12_LE.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55012.png', 'chromas': [{'id': 55013, 'name': 'Battle Academia Katarina', 'contentId': 'c2a1433a-1e17-466e-927e-e26691a3360b', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55013.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive during 2019.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 55014, 'name': 'Battle Academia Katarina', 'contentId': '4488aa1d-2a0e-4134-9c12-6b4941664feb', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55014.png', 'colors': ['#111112', '#EAF1F4'], 'descriptions': [{'region': 'riot', 'description': 'Loot exclusive gained during the 2019 Trials event.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 55015, 'name': 'Battle Academia Katarina', 'contentId': '8658343b-b56d-4ce8-bee6-6983685127c0', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55015.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': []}, {'id': 55016, 'name': 'Battle Academia Katarina', 'contentId': '9aac2169-bd6e-4f17-971a-dc624f902112', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55016.png', 'colors': ['#54209B', '#54209B'], 'descriptions': [], 'rarities': []}, {'id': 55017, 'name': 'Battle Academia Katarina', 'contentId': 'fdddf4ca-732d-49f6-82c0-765128b0e989', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55017.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': []}, {'id': 55018, 'name': 'Battle Academia Katarina', 'contentId': '6567b960-0318-4c00-a72a-2b69b2ee19f7', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55018.png', 'colors': ['#DF9117', '#DF9117'], 'descriptions': [], 'rarities': []}, {'id': 55019, 'name': 'Battle Academia Katarina', 'contentId': '20a675c4-265b-4b0f-be2d-ff1d6e470a0e', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55019.png', 'colors': ['#ECF9F8', '#ECF9F8'], 'descriptions': [], 'rarities': []}, {'id': 55020, 'name': 'Battle Academia Katarina', 'contentId': '70aaaa00-3dbc-4c17-b3f0-d20531c3c6f9', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55020.png', 'colors': ['#73BFBE', '#73BFBE'], 'descriptions': [], 'rarities': []}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 113}], 'description': 'Katarina is a rough-edged loner who stays far away from the politics of Durandal Academy. A 2nd year student with a chip on her shoulder, she is also a top member of the Assassin Club—the only club on campus allowed to kill opponents in school-sanctioned duels. Few other students ever dare approach her.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55015
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Battle Academia Katarina (Color: #2756CE) for champion Katarina
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 103014
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 103014, 'contentId': 'c4e28e05-5d3b-410b-95e0-fbd52d5f30ba', 'isBase': False, 'name': 'Star Guardian Ahri', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Ahri/Skins/Skin14/Images/ahri_splash_centered_14.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Ahri/Skins/Skin14/Images/ahri_splash_uncentered_14.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Ahri/Skins/Skin14/Images/ahri_splash_tile_14.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Ahri/Skins/Skin14/AhriLoadScreen_14.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Ahri/Skins/Skin14/AhriLoadScreen_14_LE.jpg', 'skinType': '', 'rarity': 'kLegendary', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/103/103014.png', 'chromas': [{'id': 103059, 'name': 'Star Guardian Ahri', 'contentId': '57e27d0a-de60-4e3f-87be-8bf649b6d108', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/103/103059.png', 'colors': ['#2E38C4', '#C8003F'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released in the 2021 Ahri-versary event.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 20}], 'description': "Ahri is a charismatic team captain who leads her group of Star Guardians from the outer cosmos, with the authority of a queen bee and the sly cunning of a fox. She's effortlessly popular, with an irresistible charm that disarms friends and foes alike."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 236008
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 236008, 'contentId': 'be840427-eabf-495e-a8ca-903a796b2eee', 'isBase': False, 'name': 'High Noon Lucian', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Lucian/Skins/Skin08/Images/lucian_splash_centered_8.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Lucian/Skins/Skin08/Images/lucian_splash_uncentered_8.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Lucian/Skins/Skin08/Images/lucian_splash_tile_8.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Lucian/Skins/Skin08/LucianLoadscreen_8.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Lucian/Skins/Skin08/LucianLoadScreen_8_LE.jpg', 'skinType': '', 'rarity': 'kLegendary', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/236/236008.png', 'chromas': [{'id': 236020, 'name': 'High Noon Lucian', 'contentId': '0e9aa823-0e91-4763-bbc2-629d5533206f', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/236/236020.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2020.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 236021, 'name': 'High Noon Lucian', 'contentId': '2b0df256-bff2-42c7-8c63-50698dfca573', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/236/236021.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': []}, {'id': 236022, 'name': 'High Noon Lucian', 'contentId': '5be2a7f2-52de-4491-84cf-dd5f21f64aba', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/236/236022.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': []}, {'id': 236023, 'name': 'High Noon Lucian', 'contentId': '546211ff-51ef-4d3b-ba24-cc7fc5f847d1', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/236/236023.png', 'colors': ['#ECF9F8', '#ECF9F8'], 'descriptions': [], 'rarities': []}, {'id': 236024, 'name': 'High Noon Lucian', 'contentId': 'a607fc1b-0c4f-4d28-85ee-c96b0bcea957', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/236/236024.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'TENCENT', 'rarity': 1}]}, {'id': 236050, 'name': 'High Noon Lucian', 'contentId': 'def6915c-e659-4084-88a0-087b62d5b9cd', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/236/236050.png', 'colors': ['#CFC0A7', '#59C3F2'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 4}]}, {'id': 236051, 'name': 'High Noon Lucian', 'contentId': '272608b2-6651-4cd6-8fd4-9310209d3967', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/236/236051.png', 'colors': ['#FF1502', '#E8CD6B'], 'descriptions': [], 'rarities': [{'region': 'riot', 'rarity': 1}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 39}], 'description': "A former federal marshal and gunslinger for hire, Lucian's soul was corrupted when he made a deal with the devil in order to spare the life of his one true love. Double-crossed, and cursed with the powers of hell, he now hunts this devil across the high frontier, seeking revenge."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55019
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found chroma info: Battle Academia Katarina (Color: #ECF9F8) for champion Katarina
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 81005
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 81005, 'contentId': 'a0638a12-1a65-45e8-9c33-53e85e60c268', 'isBase': False, 'name': 'Pulsefire Ezreal', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Ezreal/Skins/Skin05/Images/ezreal_splash_centered_5.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Ezreal/Skins/Skin05/Images/ezreal_splash_uncentered_5.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Ezreal/Skins/Skin05/Images/ezreal_splash_tile_5.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Ezreal/Skins/Skin05/EzrealLoadscreen_5.jpg', 'skinType': 'Ultimate', 'rarity': 'kUltimate', 'isLegacy': False, 'splashVideoPath': '/lol-game-data/assets/ASSETS/Characters/Ezreal/Skins/Skin05/AnimatedSplash/Ezreal_Skin5_centered.webm', 'collectionSplashVideoPath': '/lol-game-data/assets/ASSETS/Characters/Ezreal/Skins/Skin05/AnimatedSplash/Ezreal_Skin5_uncentered.webm', 'collectionCardHoverVideoPath': '/lol-game-data/assets/ASSETS/Characters/Ezreal/Skins/Skin05/AnimatedSplash/Ezreal_Skin05_card.webm', 'featuresText': '<ul><li>All new spell effects and animations</li><li>Four stage evolving model</li><li>Pulsefire Ezreal and AI voices</li><li>Custom effects on a killing blow</li><li>Summoner icon</li><li>Summoner profile banner</li></ul>', 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 36}], 'description': 'Temporal fugitive and time-hopping explorer extraordinaire, Ezreal leaps across disparate realities searching for interesting technology to acquire. He is responsible for countless paradoxes and is currently wanted by the dystopian Remembrancers, who hunt him relentlessly.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 55021
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 55021, 'contentId': 'b4b349e1-ca8c-4d24-a029-85187cbed1b4', 'isBase': False, 'name': 'Blood Moon Katarina', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin21/Images/katarina_splash_centered_21.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin21/Images/katarina_splash_uncentered_21.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin21/Images/katarina_splash_tile_21.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Katarina/Skins/Skin21/KatarinaLoadscreen_21.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55021.png', 'chromas': [{'id': 55022, 'name': 'Blood Moon Katarina', 'contentId': '34751672-58c8-4b25-965e-64d6e2e05e67', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55022.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2020.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 55023, 'name': 'Blood Moon Katarina', 'contentId': '6e67d3bd-052c-4387-8f83-94274c79348c', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55023.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': []}, {'id': 55024, 'name': 'Blood Moon Katarina', 'contentId': '5a4f7ea0-2de0-4ccb-8341-9790d8552303', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55024.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': []}, {'id': 55025, 'name': 'Blood Moon Katarina', 'contentId': 'cc602f8f-ef4d-45f1-ad8f-ec46dd42aebb', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55025.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': []}, {'id': 55026, 'name': 'Blood Moon Katarina', 'contentId': 'e96818db-fa2f-49c1-8530-9bb18ae95d9b', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55026.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': []}, {'id': 55027, 'name': 'Blood Moon Katarina', 'contentId': '1e4bc204-4b8d-471e-92bb-b59ca432d0ae', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55027.png', 'colors': ['#DF9117', '#DF9117'], 'descriptions': [], 'rarities': []}, {'id': 55028, 'name': 'Blood Moon Katarina', 'contentId': '1c54d607-d505-4d0f-a23d-230f229b0ba6', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/55/55028.png', 'colors': ['#ECF9F8', '#ECF9F8'], 'descriptions': [], 'rarities': []}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 12}], 'description': 'An honored priestess of the Blood Moon cult, merged with the flesh of her demon as all priestesses are fated to be. Yet the descent of the Blood Moon has changed the nature of demons and humankind, and Katarina has begun her ascent into a newer, darker form.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 39026
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 39026, 'contentId': 'af4078ba-27b4-4bde-89df-52b6add727de', 'isBase': False, 'name': 'Sentinel Irelia', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Irelia/Skins/Skin26/Images/irelia_splash_centered_26.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Irelia/Skins/Skin26/Images/irelia_splash_uncentered_26.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Irelia/Skins/Skin26/Images/irelia_splash_tile_26.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Irelia/Skins/Skin26/IreliaLoadscreen_26.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Irelia/Skins/Skin26/IreliaLoadscreen_26_LE.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/39/39026.png', 'chromas': [{'id': 39027, 'name': 'Sentinel Irelia', 'contentId': 'fb854fcf-e540-40d8-b43c-da5ebb92d9b6', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/39/39027.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2021.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 39028, 'name': 'Sentinel Irelia', 'contentId': 'c672f39d-368a-4ee5-bdfe-10fb68f72470', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/39/39028.png', 'colors': ['#DF9117', '#DF9117'], 'descriptions': [], 'rarities': []}, {'id': 39029, 'name': 'Sentinel Irelia', 'contentId': '011b89f5-74e3-4f0e-810b-79544d114d46', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/39/39029.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': []}, {'id': 39030, 'name': 'Sentinel Irelia', 'contentId': '1ed2825f-a6c5-428a-83ac-b92d1057ee07', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/39/39030.png', 'colors': ['#54209B', '#54209B'], 'descriptions': [], 'rarities': []}, {'id': 39031, 'name': 'Sentinel Irelia', 'contentId': '58daa6de-7e1e-4883-aad3-5f6519272da0', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/39/39031.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': []}, {'id': 39032, 'name': 'Sentinel Irelia', 'contentId': 'f8526a74-a451-43e9-a911-78a4bbd6a6ae', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/39/39032.png', 'colors': ['#ECF9F8', '#ECF9F8'], 'descriptions': [], 'rarities': []}, {'id': 39033, 'name': 'Sentinel Irelia', 'contentId': 'afc5b53a-ba19-45d3-8860-1f5ca7a6cfcc', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/39/39033.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'TENCENT', 'rarity': 1}]}, {'id': 39034, 'name': 'Sentinel Irelia', 'contentId': '037e7817-7d94-4406-bced-b45372ae5699', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/39/39034.png', 'colors': ['#73BFBE', '#73BFBE'], 'descriptions': [], 'rarities': []}, {'id': 39035, 'name': 'Sentinel Irelia', 'contentId': '4b5669cf-21a9-4d5d-af55-86582acc91af', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/39/39035.png', 'colors': ['#080808', '#8E0A38'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a Loot exclusive in the 2021 Sentinels event.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 147}], 'description': 'After the fall of the Ionian Grand Temple, Irelia could not stand idly by while her home was ravaged by the Black Mist. Deputized by the Sentinels, she fights the Ruination as one of their comrades: a natural-born leader who understands the threat that Ruined Karma poses, she will stop at nothing to save the Spirit of Ionia.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 166001
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 166001, 'contentId': '1f258038-4725-42ac-9cce-39a1602aa09e', 'isBase': False, 'name': 'Cyber Pop Akshan', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Akshan/Skins/Skin01/Images/akshan_splash_centered_1.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Akshan/Skins/Skin01/Images/akshan_splash_uncentered_1.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Akshan/Skins/Skin01/Images/akshan_splash_tile_1.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Akshan/Skins/Skin01/AkshanLoadscreen_1.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/166/166001.png', 'chromas': [{'id': 166002, 'name': 'Cyber Pop Akshan', 'contentId': '844787c9-cb8b-4546-af56-8c5f85de1c6b', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/166/166002.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2021.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 166003, 'name': 'Cyber Pop Akshan', 'contentId': 'a40c8c8a-a293-41fc-82c3-fde65ac3f101', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/166/166003.png', 'colors': ['#FFEE59', '#FFEE59'], 'descriptions': [], 'rarities': []}, {'id': 166004, 'name': 'Cyber Pop Akshan', 'contentId': '55367829-05ff-43e3-8d17-09ad53791734', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/166/166004.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': []}, {'id': 166005, 'name': 'Cyber Pop Akshan', 'contentId': '4554489e-5573-443a-9cc8-184e508ac05f', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/166/166005.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': []}, {'id': 166006, 'name': 'Cyber Pop Akshan', 'contentId': 'c514538c-ba6c-48dc-9ab2-fd0ea6dc164a', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/166/166006.png', 'colors': ['#54209B', '#54209B'], 'descriptions': [], 'rarities': []}, {'id': 166007, 'name': 'Cyber Pop Akshan', 'contentId': '4966a6b4-7280-4cbc-b0c9-dd178de7722f', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/166/166007.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'TENCENT', 'rarity': 1}]}, {'id': 166008, 'name': 'Cyber Pop Akshan', 'contentId': '29d8d8d0-c0b8-44e9-8c89-d0210887501a', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/166/166008.png', 'colors': ['#ECF9F8', '#ECF9F8'], 'descriptions': [], 'rarities': []}, {'id': 166009, 'name': 'Cyber Pop Akshan', 'contentId': '7aa8e385-0fd1-4fd5-92e6-740e2f4d2e2b', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/166/166009.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': []}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 148}], 'description': "The Neon Rogue of Sound City has become the hero of the underground, largely due to his bravery, his wit, and his uncanny ability to sneak into highly-secure corporate buildings without being detected. Being charming as hell doesn't hurt, either."}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 22005
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 22005, 'contentId': 'e1ac28d8-57b3-47eb-b8fb-c2b82e99e973', 'isBase': False, 'name': 'Amethyst Ashe', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Ashe/Skins/Skin05/Images/ashe_splash_centered_5.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Ashe/Skins/Skin05/Images/ashe_splash_uncentered_5.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Ashe/Skins/Skin05/Images/ashe_splash_tile_5.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Ashe/Skins/Skin05/AsheLoadScreen_5.jpg', 'skinType': '', 'rarity': 'kNoRarity', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 167}], 'description': None}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 92023
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 92023, 'contentId': '241d116b-aeb3-4370-9394-d9806b387dab', 'isBase': False, 'name': 'Spirit Blossom Riven', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Riven/Skins/Skin23/Images/riven_splash_centered_23.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Riven/Skins/Skin23/Images/riven_splash_uncentered_23.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Riven/Skins/Skin23/Images/riven_splash_tile_23.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Riven/Skins/Skin23/RivenLoadscreen_23.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Riven/Skins/Skin23/RivenLoadscreen_23_LE.jpg', 'skinType': '', 'rarity': 'kEpic', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92023.png', 'chromas': [{'id': 92026, 'name': 'Spirit Blossom Riven', 'contentId': 'dad26d1e-d0d8-445c-99b9-189706a576d2', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92026.png', 'colors': ['#2756CE', '#2756CE'], 'descriptions': [], 'rarities': []}, {'id': 92027, 'name': 'Spirit Blossom Riven', 'contentId': 'd07d7603-82cd-4bf6-9191-48e69d1a7d20', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92027.png', 'colors': ['#D33528', '#D33528'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a bundle exclusive in 2020.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}, {'id': 92028, 'name': 'Spirit Blossom Riven', 'contentId': '0fab439e-8e94-4fab-bb58-7e682ab0d5b0', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92028.png', 'colors': ['#FFEE59', '#FFEE59'], 'descriptions': [], 'rarities': []}, {'id': 92029, 'name': 'Spirit Blossom Riven', 'contentId': '1dcfb737-6d78-4c33-9cfa-d1fd7690d85e', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92029.png', 'colors': ['#2DA130', '#2DA130'], 'descriptions': [], 'rarities': []}, {'id': 92030, 'name': 'Spirit Blossom Riven', 'contentId': 'ab62d4ce-0637-4b21-9f9c-5d6ca14a62a0', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92030.png', 'colors': ['#27211C', '#27211C'], 'descriptions': [], 'rarities': []}, {'id': 92031, 'name': 'Spirit Blossom Riven', 'contentId': '6e6a4183-caf1-4518-bd8e-671f895fce0c', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92031.png', 'colors': ['#E58BA5', '#E58BA5'], 'descriptions': [], 'rarities': [{'region': 'TENCENT', 'rarity': 1}]}, {'id': 92032, 'name': 'Spirit Blossom Riven', 'contentId': '43b0de85-f1bd-434a-bcce-34d1e5fde703', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92032.png', 'colors': ['#54209B', '#54209B'], 'descriptions': [], 'rarities': []}, {'id': 92033, 'name': 'Spirit Blossom Riven', 'contentId': 'f5b2062a-5475-460b-8b63-9ca668ed9525', 'chromaPath': '/lol-game-data/assets/v1/champion-chroma-images/92/92033.png', 'colors': ['#0C0C0F', '#B2D1E4'], 'descriptions': [{'region': 'riot', 'description': 'This chroma was released as a Loot exclusive in the 2020 Spirit Blossom event.'}], 'rarities': [{'region': 'riot', 'rarity': 1}]}], 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 171}], 'description': 'A brave warrior from an ancient land, Riven was ignobly cut down in the heat of battle thousands of years ago, her sword shattering in the process. Unable to find peace, she obsessively scours an otherworldly battlefield for pieces of her broken blade, possessed by a horrific darkness that guides her into oblivion.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Looking up skin info for ID: 86013
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found skin info: {'id': 86013, 'contentId': '029573f2-b5eb-4cc4-bf6b-eae7e0e310a9', 'isBase': False, 'name': 'God-King Garen', 'splashPath': '/lol-game-data/assets/ASSETS/Characters/Garen/Skins/Skin13/Images/garen_splash_centered_13.jpg', 'uncenteredSplashPath': '/lol-game-data/assets/ASSETS/Characters/Garen/Skins/Skin13/Images/garen_splash_uncentered_13.jpg', 'tilePath': '/lol-game-data/assets/ASSETS/Characters/Garen/Skins/Skin13/Images/garen_splash_tile_13.jpg', 'loadScreenPath': '/lol-game-data/assets/ASSETS/Characters/Garen/Skins/Skin13/GarenLoadscreen_13.jpg', 'loadScreenVintagePath': '/lol-game-data/assets/ASSETS/Characters/Garen/Skins/Skin13/GarenLoadScreen_13_LE.jpg', 'skinType': '', 'rarity': 'kLegendary', 'isLegacy': False, 'splashVideoPath': None, 'collectionSplashVideoPath': None, 'collectionCardHoverVideoPath': None, 'featuresText': None, 'chromaPath': None, 'emblems': None, 'regionRarityId': 0, 'rarityGemPath': None, 'skinLines': [{'id': 78}], 'description': 'God-King Garen stands as the last scion of an ancient divinity, and the final bulwark against the end of all civilization. He rules the vast kingdom of Demacia with an iron fist, jealously protecting his people as a self-styled messiah, while rooting out and destroying all who would oppose him.'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Saving 64 skins for konjisenpaii
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Got skin data for konjisenpaii
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Checking penalties for konjisenpaii
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Checking penalties for None using URL: https://euw-red.lol.sgp.pvp.net/leaverbuster-ledge/restrictionInfo
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Penalty check response: {'puuid': '6c47ee27-a616-527f-a007-582ff470c05f', 'rankedRestrictionEntryDto': {'puuid': '6c47ee27-a616-527f-a007-582ff470c05f', 'version': 0, 'restrictedGamesRemaining': 0, 'restrictedGamesOriginal': 0, 'rankedRestrictionPenaltyId': '5c5b216d-f0f9-49e9-aa23-37d5e08903bc', 'punishmentIncurredGameId': 0, 'punishmentIncurredTimeMillis': 0, 'rankedRestrictionAckNeeded': False, 'penaltyOrigin': 'UNKNOWN'}, 'leaverBusterEntryDto': {'puuid': '6c47ee27-a616-527f-a007-582ff470c05f', 'version': 949, 'tainted': True, 'preLockoutAckNeeded': False, 'onLockoutAckNeeded': False, 'leaverScore': 0, 'leaverLevel': 0, 'punishedGamesRemaining': 0, 'currentPunishmentStep': 0, 'leaverPenalty': {'puuid': '6c47ee27-a616-527f-a007-582ff470c05f', 'hasActivePenalty': False, 'delayTime': 0, 'queueLockoutTimerExpiryUtcMillis': 0, 'punishmentTimerType': 'NO_PENALTY', 'rankRestrictedGamesRemaining': 0, 'rankRestrictedTimerExpiryUtcMillis': 0, 'rankRestricted': False}, 'warnSentMillis': 1585675593168, 'warnAckedMillis': 1585746326260, 'lastUpdatedMillis': 1749896594496, 'totalPunishedGamesPlayed': 12, 'lastBustedGameId': 7094866894, 'lastBustedTimeMillis': 1724870816608, 'lastPunishmentIncurredGameId': 7094866894, 'lastPunishmentIncurredTimeMillis': 1724870816608, 'processedGameIdHistoryString': '7417264658,7417350390,7417420378,7417479305,7417520673,7417571839,7417592277,7417612125,7417625186,7417639122,7417988841,7418034703,7418091241,7418121397,7431748234'}}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] No active penalty
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Got penalty info for konjisenpaii: 0 minutes, 0 games
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Checking rank for CornyTalonAbuser#EUW
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Using region code EUW for rank info retrieval
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[90m@←[0m←[97m] get_rank_info called with region_code: EUW, type: <class 'str'>
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[90m@←[0m←[97m] Getting rank info for CornyTalonAbuser#EUW in region EUW
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[91m-←[0m←[97m] Error decoding JWT: module 'jwt' has no attribute 'decode'
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Found country from userinfo: unknown
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[92m$←[0m←[97m] Current rank data response: {'queues': [{'queueType': 'RANKED_SOLO_5x5', 'provisionalGameThreshold': 5, 'tier': 'DIAMOND', 'rank': 'III', 'leaguePoints': 35, 'cumulativeLp': 2535, 'wins': 72, 'losses': 29, 'provisionalGamesRemaining': 0, 'highestTier': 'DIAMOND', 'highestRank': 'III', 'previousSeasonEndTier': 'PLATINUM', 'previousSeasonEndRank': 'II', 'previousSeasonHighestTier': 'PLATINUM', 'previousSeasonHighestRank': 'II', 'previousSeasonAchievedTier': 'PLATINUM', 'previousSeasonAchievedRank': 'II', 'ratedRating': 0, 'warnings': {'apexDaysUntilDecay': 4, 'daysUntilDecay': 4}, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_FLEX_SR', 'provisionalGameThreshold': 5, 'tier': 'IRON', 'rank': 'IV', 'leaguePoints': 76, 'cumulativeLp': 76, 'wins': 1, 'losses': 0, 'provisionalGamesRemaining': 4, 'highestTier': 'IRON', 'highestRank': 'IV', 'previousSeasonHighestTier': 'IRON', 'previousSeasonHighestRank': 'IV', 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_TFT', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_TFT_TURBO', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_TFT_DOUBLE_UP', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}], 'highestPreviousSeasonEndTier': 'PLATINUM', 'highestPreviousSeasonEndRank': 'II', 'highestPreviousSeasonAchievedTier': 'PLATINUM', 'highestPreviousSeasonAchievedRank': 'II', 'earnedRegaliaRewardIds': [], 'splitsProgress': {}, 'seasons': {'RANKED_TFT_TURBO': {'currentSeasonId': 29, 'currentSeasonEnd': 1753848001000, 'nextSeasonStart': 0}, 'RANKED_TFT': {'currentSeasonId': 29, 'currentSeasonEnd': 1753848001000, 'nextSeasonStart': 0}, 'RANKED_FLEX_SR': {'currentSeasonId': 19, 'currentSeasonEnd': 1756249199000, 'nextSeasonStart': 0}, 'RANKED_TFT_DOUBLE_UP': {'currentSeasonId': 29, 'currentSeasonEnd': 1753848001000, 'nextSeasonStart': 0}, 'RANKED_SOLO_5x5': {'currentSeasonId': 19, 'currentSeasonEnd': 1756249199000, 'nextSeasonStart': 0}}, 'jwt': '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'}
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[90m@←[0m←[97m] scrape_account_info called with region_code: EUW, type: <class 'str'>
←[0m←[97m[2025-07-01 21:27:34] [←[0m←[90m@←[0m←[97m] Scraping account info from: https://www.leagueofgraphs.com/summoner/euw/CornyTalonAbuser-EUW
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[90m@←[0m←[97m] Got HTML response length: 1469500
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Found 10 season tags
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Processing tag: S10 Bronze with tooltip: <itemname class='tagTitle brown'>S10 Bronze</itemname><br/><span class='tagDescription'><highlight>Ranked Solo/Duo</highlight><br/>This player reached Bronze I during Season 10. At the end of the season, this player was Bronze I.<br/><br/><highlight>Ranked Flex</highlight><br/>This player reached Iron I during Season 10. At the end of the season, this player was Iron I.</span>
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Processing tag: S11 Silver with tooltip: <itemname class='tagTitle brown'>S11 Silver</itemname><br/><span class='tagDescription'><highlight>Ranked Solo/Duo</highlight><br/>This player reached Silver II during Season 11. At the end of the season, this player was Silver II.<br/></span>
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Processing tag: S12 Silver with tooltip: <itemname class='tagTitle brown'>S12 Silver</itemname><br/><span class='tagDescription'><highlight>Ranked Solo/Duo</highlight><br/>This player reached Silver I during Season 12. At the end of the season, this player was Silver II.<br/></span>
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Processing tag: S13 (Split 2) Bronze with tooltip: <itemname class='tagTitle brown'>S13 (Split 2) Bronze</itemname><br/><span class='tagDescription'><highlight>Ranked Solo/Duo</highlight><br/>This player reached Bronze I during Season 13 (Split 2). At the end of the season, this player was Bronze I.<br/></span>
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Processing tag: S14 (Split 1) Silver with tooltip: <itemname class='tagTitle brown'>S14 (Split 1) Silver</itemname><br/><span class='tagDescription'><highlight>Ranked Solo/Duo</highlight><br/>This player reached Silver II during Season 14 (Split 1). At the end of the season, this player was Silver II.<br/></span>
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Processing tag: S14 (Split 2) Gold with tooltip: <itemname class='tagTitle brown'>S14 (Split 2) Gold</itemname><br/><span class='tagDescription'><highlight>Ranked Solo/Duo</highlight><br/>This player reached Gold IV during Season 14 (Split 2). At the end of the season, this player was Gold IV.<br/></span>
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Processing tag: Bounces back with tooltip: <itemname class='tagTitle green'>Bounces back</itemname><br/><span class='tagDescription'>This player has a higher winrate when playing just after a defeat (+<span class='highlight'>7</span>%)</span>
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Processing tag: Snowballs with tooltip: <itemname class='tagTitle green'>Snowballs</itemname><br/><span class='tagDescription'>This player has a better winrate when 2/0, 3/1... at 10 minutes than the other <span class='highlight'>Diamond</span> players: +<span class='highlight'>16</span>%</span>
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Processing tag: Blue side lover with tooltip: <itemname class='tagTitle yellow'>Blue side lover</itemname><br/><span class='tagDescription'>This player has a far higher winrate when playing on the blue side (+<span class='highlight'>6</span>%)</span>
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Processing tag: Too confident with tooltip: <itemname class='tagTitle red'>Too confident</itemname><br/><span class='tagDescription'>This player's winrate is lower when playing just after a victory (-<span class='highlight'>6</span>%)</span>
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Found previous season rank from LeagueOfGraphs: {'tier': 'GOLD', 'division': 'IV'}
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Stored complete season history with 6 entries
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Processed rank info: {'solo': {'tier': 'DIAMOND', 'division': 'III', 'lp': 35, 'wins': 72, 'losses': 29, 'previous_tier': 'GOLD', 'previous_division': 'IV'}, 'flex': {'tier': 'IRON', 'division': 'IV', 'lp': 76, 'wins': 1, 'losses': 0, 'previous_tier': 'UNRANKED', 'previous_division': ''}, 'country': 'unknown', 'creation_region': 'EUW', 'season_history': [{'season': 14, 'split': '2', 'queue_type': 'SOLO_DUO', 'peak_tier': 'GOLD', 'peak_division': 'IV', 'end_tier': 'GOLD', 'end_division': 'IV'}, {'season': 14, 'split': '1', 'queue_type': 'SOLO_DUO', 'peak_tier': 'SILVER', 'peak_division': 'II', 'end_tier': 'SILVER', 'end_division': 'II'}, {'season': 13, 'split': '2', 'queue_type': 'SOLO_DUO', 'peak_tier': 'BRONZE', 'peak_division': 'I', 'end_tier': 'BRONZE', 'end_division': 'I'}, {'season': 12, 'split': None, 'queue_type': 'SOLO_DUO', 'peak_tier': 'SILVER', 'peak_division': 'I', 'end_tier': 'SILVER', 'end_division': 'II'}, {'season': 11, 'split': None, 'queue_type': 'SOLO_DUO', 'peak_tier': 'SILVER', 'peak_division': 'II', 'end_tier': 'SILVER', 'end_division': 'II'}, {'season': 10, 'split': None, 'queue_type': 'SOLO_DUO', 'peak_tier': 'BRONZE', 'peak_division': 'I', 'end_tier': 'BRONZE', 'end_division': 'I'}]}
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Got rank info for konjisenpaii
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Including 6 season history entries for konjisenpaii
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Saving rank info for konjisenpaii: Solo=DIAMOND III, Flex=IRON IV
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Successfully rechecked account: konjisenpaii
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Loading account konjisenpaii from database
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Account details: BE=6385, RP=0, Level=167
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Account info: created_at=2020-03-24 10:08:02, creation_region=EUW, country=swe
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Game info: game_name=CornyTalonAbuser, tag_line=EUW
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Checking JWT token for region information for konjisenpaii
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] JWT region debugging for konjisenpaii:
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m]   lol field: [{'cuid': ****************, 'cpid': 'EUW1', 'uid': ****************, 'uname': 'konjisenpaii', 'ptrid': None, 'pid': 'EUW1', 'state': 'ENABLED'}]
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m]   region field: {}
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m]   original_platform_id:
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m]   lol_region: [{'active': True, 'cpid': 'EUW1', 'cuid': ****************, 'lp': False, 'pid': 'EUW1', 'uid': ****************}]
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m]   dat field: {}
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Using lol_region[].cpid to determine region for konjisenpaii: EUW
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Found region in JWT for konjisenpaii: EUW
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Loaded 6 season history entries for konjisenpaii
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Using region=EUW, region_code=EUW for account konjisenpaii
←[0m←[97m[2025-07-01 21:27:35] [←[0m←[92m$←[0m←[97m] Loaded 1 accounts from database
←[0mDEBUG: PyQtBridge.handle_account_selection called with index: 0
DEBUG: Updated PyQtBridge.selected_account_index to: 0
DEBUG: Updated main_window.selected_account_index via set_selected_account_index
DEBUG: Found Victorious parent skin: 37056 for chroma: 37057
DEBUG: Generating HTML for 2 chromas
