
C:\Users\<USER>\Desktop\sources\League Acc Checker>py main.py
←[97m[2025-07-02 08:22:51] [←[0m←[92m$←[0m←[97m] Created DatabaseManagerWrapper with path accounts.db
←[0m←[97m[2025-07-02 08:22:51] [←[0m←[92m$←[0m←[97m] Wrapped db_manager with DatabaseManagerWrapper in AccountPool
←[0m←[97m[2025-07-02 08:22:51] [←[0m←[92m$←[0m←[97m] Loaded 1941 skin entries from JSON database
←[0mDEBUG: Created CustomWebEngineView
DEBUG: Created web view with custom context menu handling
Loaded 1941 skin entries from JSON database
DEBUG: Sharing champion database with 171 entries to PyQtBridge and AccountPool
←[97m[2025-07-02 08:22:52] [←[0m←[92m$←[0m←[97m] Loading account spanglebob from database
←[0m←[97m[2025-07-02 08:22:52] [←[0m←[92m$←[0m←[97m] Account details: BE=5705, RP=0, Level=0
←[0m←[97m[2025-07-02 08:22:52] [←[0m←[92m$←[0m←[97m] Account info: created_at=None, creation_region=EUW, country=
←[0m←[97m[2025-07-02 08:22:52] [←[0m←[92m$←[0m←[97m] Game info: game_name=spanglebob, tag_line=
←[0m←[97m[2025-07-02 08:22:52] [←[0m←[92m$←[0m←[97m] Checking JWT token for region information for spanglebob
←[0m←[97m[2025-07-02 08:22:52] [←[0m←[92m$←[0m←[97m] JWT region debugging for spanglebob:
←[0m←[97m[2025-07-02 08:22:52] [←[0m←[92m$←[0m←[97m]   lol field: [{'cuid': *********, 'cpid': 'EUW1', 'uid': *********, 'uname': 'spanglebob', 'ptrid': None, 'pid': 'EUW1', 'state': 'ENABLED'}]
←[0m←[97m[2025-07-02 08:22:52] [←[0m←[92m$←[0m←[97m]   region field: {}
←[0m←[97m[2025-07-02 08:22:52] [←[0m←[92m$←[0m←[97m]   original_platform_id:
←[0m←[97m[2025-07-02 08:22:52] [←[0m←[92m$←[0m←[97m]   lol_region: [{'active': True, 'cpid': 'EUW1', 'cuid': *********, 'lp': False, 'pid': 'EUW1', 'uid': *********}]
←[0m←[97m[2025-07-02 08:22:52] [←[0m←[92m$←[0m←[97m]   dat field: {}
←[0m←[97m[2025-07-02 08:22:52] [←[0m←[92m$←[0m←[97m] Using lol_region[].cpid to determine region for spanglebob: EUW
←[0m←[97m[2025-07-02 08:22:52] [←[0m←[92m$←[0m←[97m] Found region in JWT for spanglebob: EUW
←[0m←[97m[2025-07-02 08:22:52] [←[0m←[92m$←[0m←[97m] Loaded 0 season history entries for spanglebob
←[0m←[97m[2025-07-02 08:22:52] [←[0m←[92m$←[0m←[97m] Using region=EUW, region_code=EUW for account spanglebob
←[0m←[97m[2025-07-02 08:22:52] [←[0m←[92m$←[0m←[97m] Loaded 1 accounts from database
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Starting to recheck account: spanglebob
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Checking JWT token for region during recheck for spanglebob
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Using lol_region[].cpid from JWT to determine region: EUW
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Updated region to EUW based on JWT token for account spanglebob
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Initializing authenticator for account: spanglebob
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Extracted JWT payload for spanglebob, keys: ['sub', 'country', 'player_plocale', 'amr', 'iss', 'lol', 'phone_number_verified', 'locale', 'nonce', 'account_verified', 'aud', 'acr', 'lol_region', 'player_locale', 'exp', 'iat', 'acct', 'age', 'jti', 'login_country']
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] JWT region debugging for spanglebob:
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m]   lol field: [{'cuid': *********, 'cpid': 'EUW1', 'uid': *********, 'uname': 'spanglebob', 'ptrid': None, 'pid': 'EUW1', 'state': 'ENABLED'}]
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m]   region field: {}
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m]   original_platform_id:
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m]   lol_region: [{'active': True, 'cpid': 'EUW1', 'cuid': *********, 'lp': False, 'pid': 'EUW1', 'uid': *********}]
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m]   dat field: {}
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Using lol_region[].cpid to determine region for spanglebob: EUW
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Found region in JWT for spanglebob: EUW
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Initializing authenticator for spanglebob with region EUW
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Authenticator initialized for spanglebob in region EUW
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Set existing tokens from account object for account: spanglebob
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Using refresh token for spanglebob: yes
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Authenticator initialized for spanglebob in region EUW
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Set existing tokens from account object for account: spanglebob
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Using refresh token for spanglebob: yes
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Rechecking account: spanglebob
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Starting check for account spanglebob with region EUW
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Getting userinfo for spanglebob
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Using access token for userinfo: eyJraWQiOi...06-mrSfRMw
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Access token length: 1224
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Userinfo request URL: https://auth.riotgames.com/userinfo
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Userinfo request headers: Authorization: Bearer eyJraWQiOi...06-mrSfRMw, Accept: application/json, Content-Type: application/json
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[93m!←[0m←[97m] Error getting userinfo: 401 Unauthorized - Token is likely expired or invalid. Response:
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[93m!←[0m←[97m] Full response headers: Headers({'date': 'Wed, 02 Jul 2025 06:22:55 GMT', 'content-length': '0', 'connection': 'keep-alive', 'x-rsorouterclusterid': 'ec1', 'x-rso-error-id': '40101121', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=6892lY3gr8vl6IrRGqAQrnB8GC0KmzehWLPKEA6F_jo-1751437375-*******-pxDe5uBwJnPpgEYNCz0tLPCwJuJANviLXM6oO6WwQqHKkNK9PEwke0LcEVzcBTxkXuS17SGgHp7Hw9nEdUVWD0.SJTOQ8Qqx5hDpAK3OEc0; path=/; expires=Wed, 02-Jul-25 06:52:55 GMT; domain=.riotgames.com; HttpOnly; Secure; SameSite=None', 'x-riotgames-cdn': 'Cloudflare', 'server': 'cloudflare', 'cf-ray': '958c0d2b1d8bf80d-DUS'})
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[93m!←[0m←[97m] Empty userinfo response for spanglebob, attempting re-auth
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Got account info for spanglebob: {'username': 'spanglebob', 'password': 'Wolves10', 'region': 'EUW', 'refresh_token': 'eyJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwiYWxnIjoiZGlyIn0..cz2Ku16Jvv_5tv7-3hs4FA.o6PzTxmmO3eCB8a-PeSal2DFXaeUpK5bmhRhnvI2if8M9hCbUlabY4tt7JAx71nQdZiMQIuxqYlaxpmWvPAHbUsZiZSq4DX_ZNwhECxA91hTdpoz_OccNOj49BgqzE3yR4CefeC5MuFOqQBlSlNNNReE8a5t_81GL1bKhfVn3npkgfFisLw8NlU9vqIc8x9sJEx1MTXUYv_FFQkN_lxG7i_GCBD5vuF4V_z9UQmi8POn4Q51eM0LbwKoc9V0zmz5RkUoe174-a3Q9V6AFwwqJdPc4wehMeoa9WxgGujC4s8RVkW1celRq_KMF2hRS4w4n-XMu77UqWtZwW4zeG2OamnS9H-aDliS7dcT3EjeW0aDFHZAqDJzpJJJldwtjPzhnlFWKv9cI-yi2AqMiIRft6A1howuV8ySTcqGedd3h66WWvgTgrpXDa2pLERYxYlUDam5-itKuaUoX1-8AelNwN4dY22Wi9q9dYapHfx--cIK0Tfr1PAOGaviCa9WCJbGE-AUNHDMKrkjFesTv7wK3TPA6BJrgj_Hh1t715SBYGvpJF7FoK2Vjup4-wP-NklReXk2mpS9cj6my6UK8xyYljhIc1PAr1gpVkTpN2pA80hiJOunu9UydXUxZYHE3t4LSm8SKam5vdT9l4N1563Gg9jA7CZenh-Kr2Z1jKgNfXgxo7nBglZmMLRYdOnOthQd2pARTZMZ9fthWvv2r5GbaXryfDdIQDlcG9Qs96ovxfjGomQpPGEhxCbq4IwrXhW8ButqFK2PNQlTBtyASZ_Ss7XgwTb_6QBIr6jMDxyypSGJcq_2McfgC2EvtiXiIIDp7ocpSwq16awbgdJ9tpy1WNu2YxKVnj02qMM323BaZ6TVMVZHRTj1ZIY9xtFPkx-ynIwq3wRlIZKB4NZ7TREB0G271XUNfX6WiOIHXIk5Y9jpvdh9t41uXYMNG6uDpwCBYww0hudCXPbjA8zZJtzfUA._ZVdPpSUHjEv_kWxitHqfw', 'access_token': 'eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwiYWxnIjoiUlMyNTYifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.gseG6-aBP9WxKh3GRW4QmlYbDlKupgcr-FRsrfCZqnSNCkIhk8N1kvPLit2_7AhUcBL2hCjVh7j3_LwW0TFGHoQxIwowt44e2QmVBrlERGiBtlXBJvD22iGagtDxKbLIApByfkaDyfKj67Wnbv8PfVT9dSF1jdMVJQBhhXAwtNVvgpEH3mIREmd7cohE9K0DEEXxd10enHQqmPu2pXtb_25SpF6-e-r0RD98d-Sqm-liw8NZnhzUbDqeiBqFcS1h4Tv8Ir9swZF72-zuDmsVCr0InzJZdfb2HxZGNhj3eayF1rNvclWgkx4Nxpp08rq_eLlan7ys2uWt06-mrSfRMw', 'id_token': '*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************', 'entitlements_token': None, 'userinfo': None, 'timestamp': 1289, 'puuid': None, 'account_id': None, 'game_name': 'spanglebob', 'tag_line': '', 'champion_count': 41, 'blue_essence': 5705, 'riot_points': 0, 'summoner_level': 0, 'created_at': None, 'last_checked': '2025-07-02 06:07:20', 'penalty_minutes': 0, 'ranked_games_remaining': 0, 'is_banned': 0, 'ban_info': None, 'country': '', 'creation_region': 'EUW', 'solo_tier': 'SILVER', 'solo_division': 'II', 'solo_lp': -17, 'solo_wins': 28, 'solo_losses': 7, 'solo_previous_tier': 'UNRANKED', 'solo_previous_division': '', 'flex_tier': 'UNRANKED', 'flex_division': '', 'flex_lp': 0, 'flex_wins': 0, 'flex_losses': 0, 'flex_previous_tier': 'UNRANKED', 'flex_previous_division': '', 'chat_restrictions': None, 'is_glitch_account': 0, 'region_code': 'EUW'}
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Using refresh token for spanglebob: yes
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] Attempting to use refresh token for spanglebob
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] REFRESH DEBUG - Payload: {'client_id': 'lol', 'grant_type': 'refresh_token', 'refresh_token': 'eyJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwiYWxnIjoiZGlyIn0..cz2Ku16Jvv_5tv7-3hs4FA.o6PzTxmmO3eCB8a-PeSal2DFXaeUpK5bmhRhnvI2if8M9hCbUlabY4tt7JAx71nQdZiMQIuxqYlaxpmWvPAHbUsZiZSq4DX_ZNwhECxA91hTdpoz_OccNOj49BgqzE3yR4CefeC5MuFOqQBlSlNNNReE8a5t_81GL1bKhfVn3npkgfFisLw8NlU9vqIc8x9sJEx1MTXUYv_FFQkN_lxG7i_GCBD5vuF4V_z9UQmi8POn4Q51eM0LbwKoc9V0zmz5RkUoe174-a3Q9V6AFwwqJdPc4wehMeoa9WxgGujC4s8RVkW1celRq_KMF2hRS4w4n-XMu77UqWtZwW4zeG2OamnS9H-aDliS7dcT3EjeW0aDFHZAqDJzpJJJldwtjPzhnlFWKv9cI-yi2AqMiIRft6A1howuV8ySTcqGedd3h66WWvgTgrpXDa2pLERYxYlUDam5-itKuaUoX1-8AelNwN4dY22Wi9q9dYapHfx--cIK0Tfr1PAOGaviCa9WCJbGE-AUNHDMKrkjFesTv7wK3TPA6BJrgj_Hh1t715SBYGvpJF7FoK2Vjup4-wP-NklReXk2mpS9cj6my6UK8xyYljhIc1PAr1gpVkTpN2pA80hiJOunu9UydXUxZYHE3t4LSm8SKam5vdT9l4N1563Gg9jA7CZenh-Kr2Z1jKgNfXgxo7nBglZmMLRYdOnOthQd2pARTZMZ9fthWvv2r5GbaXryfDdIQDlcG9Qs96ovxfjGomQpPGEhxCbq4IwrXhW8ButqFK2PNQlTBtyASZ_Ss7XgwTb_6QBIr6jMDxyypSGJcq_2McfgC2EvtiXiIIDp7ocpSwq16awbgdJ9tpy1WNu2YxKVnj02qMM323BaZ6TVMVZHRTj1ZIY9xtFPkx-ynIwq3wRlIZKB4NZ7TREB0G271XUNfX6WiOIHXIk5Y9jpvdh9t41uXYMNG6uDpwCBYww0hudCXPbjA8zZJtzfUA._ZVdPpSUHjEv_kWxitHqfw', 'scope': 'openid offline_access lol ban lol_region'}
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] REFRESH DEBUG - Headers: {'Content-Type': 'application/x-www-form-urlencoded', 'User-Agent': 'RiotClient/63.0.9.4909983.4789131 rso-auth (Windows;10;;Professional, x64)', 'Accept': 'application/json'}
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] REFRESH DEBUG - URL: https://auth.riotgames.com/token
←[0m←[97m[2025-07-02 08:22:54] [←[0m←[92m$←[0m←[97m] REFRESH DEBUG - Method: POST
←[0m←[97m[2025-07-02 08:22:56] [←[0m←[91m-←[0m←[97m] Error refreshing token: [SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate (_ssl.c:1028)
←[0m←[97m[2025-07-02 08:22:56] [←[0m←[91m-←[0m←[97m] Refresh token failed - no access token returned
←[0m←[97m[2025-07-02 08:22:56] [←[0m←[92m$←[0m←[97m] Falling back to password auth for spanglebob
←[0m←[97m[2025-07-02 08:22:56] [←[0m←[92m$←[0m←[97m] Solve captcha starting for spanglebob
←[0m←[97m[2025-07-02 08:22:56] [←[0m←[92m$←[0m←[97m] Initial post payload: {'clientId': 'lol', 'language': 'en_US', 'platform': 'windows', 'remember': False, 'riot_identity': {'state': 'auth'}, 'sdkVersion': '24.8.0.4145', 'type': 'auth'}
←[0m←[97m[2025-07-02 08:22:56] [←[0m←[92m$←[0m←[97m] Post response status: 200
←[0m←[97m[2025-07-02 08:22:56] [←[0m←[92m$←[0m←[97m] Got captcha challenge with sitekey: 019f1553-3845-481c-a6f5-5a60ccf6d830
←[0m←[97m[2025-07-02 08:22:56] [←[0m←[92m$←[0m←[97m] Starting captcha solver subprocess
←[0m←[97m[2025-07-02 08:23:01] [←[0m←[92m$←[0m←[97m] Captcha solver process completed
←[0m←[97m[2025-07-02 08:23:01] [←[0m←[92m$←[0m←[97m] Got captcha token: P1_eyJ0eXAiOiJKV1QiL
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Extracted login token
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Extracted authorization code
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Successfully retrieved tokens for spanglebob
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Solve captcha result: True
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Extracted region from JWT lol_region[].cpid: EUW
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Using access token for userinfo: eyJraWQiOi...8xOEZyQDNw
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Access token length: 1224
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Userinfo request URL: https://auth.riotgames.com/userinfo
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Userinfo request headers: Authorization: Bearer eyJraWQiOi...8xOEZyQDNw, Accept: application/json, Content-Type: application/json
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Raw userinfo response (first 100 chars): eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwidHlwIjoidXNlcmluZm8rand0IiwiYWxnIjoiUlMyNTYifQ.eyJjb3VudHJ5IjoiZ
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Userinfo response length: 2260
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Received JWT token for userinfo
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] JWT token has 3 parts
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Successfully decoded JWT payload with 25 keys
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[93m!←[0m←[97m] Ban info in JWT payload: {
  "restrictions": [
    {
      "type": "QUEUE_LOCKOUT",
      "reason": "QUEUE_DODGE",
      "scope": "lol",
      "dat": {
        "expirationMillis": *************,
        "gameData": {
          "productName": "lol",
          "gameLocation": "EUW1",
          "triggerGameId": "7449434636",
          "additionalGameIds": []
        },
        "gameLocation": "EUW1"
      }
    }
  ]
}
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[93m!←[0m←[97m] Found 1 ban restrictions
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[93m!←[0m←[97m] Ban info: QUEUE_LOCKOUT - QUEUE_DODGE
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Successfully decoded JWT payload with 25 keys
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Restriction QUEUE_LOCKOUT for spanglebob is temporary, expires at *************
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Dodge timer found for spanglebob: QUEUE_LOCKOUT - QUEUE_DODGE
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[93m!←[0m←[97m] Found ban info: QUEUE_LOCKOUT - QUEUE_DODGE
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Account spanglebob has temporary chat restrictions: QUEUE_LOCKOUT - QUEUE_DODGE
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Successfully authenticated spanglebob with password
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Using access token for userinfo: eyJraWQiOi...8xOEZyQDNw
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Access token length: 1224
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Userinfo request URL: https://auth.riotgames.com/userinfo
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Userinfo request headers: Authorization: Bearer eyJraWQiOi...8xOEZyQDNw, Accept: application/json, Content-Type: application/json
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Raw userinfo response (first 100 chars): eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwidHlwIjoidXNlcmluZm8rand0IiwiYWxnIjoiUlMyNTYifQ.eyJjb3VudHJ5IjoiZ
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Userinfo response length: 2260
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Received JWT token for userinfo
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] JWT token has 3 parts
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Successfully decoded JWT payload with 25 keys
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[93m!←[0m←[97m] Ban info in JWT payload: {
  "restrictions": [
    {
      "type": "QUEUE_LOCKOUT",
      "reason": "QUEUE_DODGE",
      "scope": "lol",
      "dat": {
        "expirationMillis": *************,
        "gameData": {
          "productName": "lol",
          "gameLocation": "EUW1",
          "triggerGameId": "7449434636",
          "additionalGameIds": []
        },
        "gameLocation": "EUW1"
      }
    }
  ]
}
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[93m!←[0m←[97m] Found 1 ban restrictions
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[93m!←[0m←[97m] Ban info: QUEUE_LOCKOUT - QUEUE_DODGE
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Successfully decoded JWT payload with 25 keys
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Restriction QUEUE_LOCKOUT for spanglebob is temporary, expires at *************
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Dodge timer found for spanglebob: QUEUE_LOCKOUT - QUEUE_DODGE
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[93m!←[0m←[97m] Found ban info: QUEUE_LOCKOUT - QUEUE_DODGE
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Account spanglebob has temporary chat restrictions: QUEUE_LOCKOUT - QUEUE_DODGE
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Got userinfo for spanglebob, decoding JWT
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[93m!←[0m←[97m] JWT payload had invalid UTF-8 bytes, some data may be lost
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[91m-←[0m←[97m] Error decoding JWT payload: Invalid control character at: line 1 column 1248 (char 1247)
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Getting tokens for spanglebob
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[93m!←[0m←[97m] Invalid JSON in userinfo for get_queue_token: Expecting value: line 1 column 1 (char 0)
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - URL: https://euc1-red.pp.sgp.pvp.net/login-queue/v2/login/products/lol/regions/EUW1
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Region: EUW, Region Upper: EUW1
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Player Platform Edge URL: https://euc1-red.pp.sgp.pvp.net
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Headers: {"Authorization": "Bearer eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwiYWxnIjoiUlMyNTYifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.l1DAe_KErM9vJp4uGyUDbc-OGn5HdzN2DsyaRVkSUPFfluqgKFxkmPQBpGVLrQlCJuVdZwJfeZjnY8_0Ka1LqtNmjc6KpIq_HhzZvbnusw2N26S1okcvpM_uHrhu4yW9twk6FKN_vsWu9DJziRGS6MtFLNz-_PhGhhl7waly_C7TkLwvtowB2UYYKV5OQ1W9M_xAHcsOfW1HJA6Cu0p7sscw6xgUHJ6v1n-x9aIXfyViHz-2yuCRGON2D7ySU5AyO9BIMqJGw6K_Z8cNHrUlV2W3qYdN5ZDP_dB0DkENhNS0FXCAQ2rk-USbeThiwoFX1ycVJ8Hc8MZ48xOEZyQDNw", "Accept": "application/json", "Content-Type": "application/json"}
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Body: {"clientName": "lcu", "entitlements": "eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.******************************************************************************************************************************************************************************************************************************************************.J19PxW0qc8onM5B739nEVf3N8Tmw5mB4XcZBySuaArIneuHnUxxModYGPXceOHlJ5d_WSIdMtlSsKe7vPhbBwicGmYSiog9bOm5XP3uynvpsNQF5oYHRsziNB1sHxETNhpHIM4NBn4UayBShlQKQrbVgcOjeL9riI02PqP52OX6d5kJA5zSxoI2WB1Hrchzj-ygjv9bXWRXZHiBPuaS7VAOHYw8WufewgnBJ5VSIVw23rwr1o9kZtHg8uELBrdp5FBfoggbML75CoO0idp10Onow9D9m40-gAG1VPx5Q5DGzLE2LUUNHn_C6glSVGGEkmzb8azV5ahGN02f5z0oMIg", "userinfo": "eyJraWQiOiJyc28tcHJvZC0yMDI0LTExIiwidHlwIjoidXNlcmluZm8rand0IiwiYWxnIjoiUlMyNTYifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************_ph4_ph4_ph4_ph4_ph4_ph4_ph4_ph4_ph4_ph4_ph4_ph4_ph4_ph4_ph48iLCJ0YWdfbGluZSI6IuWLh-WLh-WLh-WLh-WLhyIsImNyZWF0ZWRfYXQiOjE0ODAyOTEyMDAwMDB9LCJ1c2VybmFtZSI6InNwYW5nbGVib2IifQ.mxVjXJ-Y4ZR3OryR-7ePn_EW2_b6UCoeJRB9dSnF7fK1OzBtHU1WM-XXuT3fmlV9hlFRc0u60lP2ISR6Bjy8sDk8B6cJQRgVPLDuRBXycS4aJs7lOSQFORObhNtasUYGq9NUBVv0phTHQ4TvV7Z7qJJ0r02yB4kr3b5ca-v78lV7UO9J29x01iWugEmhTVCOWzcp4pO7CpJ-1SfMvz8ykBWGjmbJ1mZv2epM5xQV-BwAG46ZW6UKdup5PEev8Ipy2JdH6CRDw1f1cHmwAlN7vSA551naRp0teZmvlxpglqNxIJeFYXi2iW0t5P1mCUrf84mBIzo-eSAM2cB7muRWdg"}
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Status: 200
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Headers: {"date": "Wed, 02 Jul 2025 06:23:03 GMT", "content-type": "application/json;charset=utf-8", "transfer-encoding": "chunked", "connection": "keep-alive", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "expires": "0", "pragma": "no-cache", "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers, accept-encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-riot-edge-trace-id": "93c33137-c3d3-4329-a413-fbaf4b255bf2", "x-xss-protection": "0", "content-encoding": "gzip", "access-control-allow-origin": "*", "access-control-allow-methods": "GET, PUT, DELETE, POST, OPTIONS", "access-control-allow-headers": "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range", "access-control-expose-headers": "Content-Length,Content-Range", "cf-cache-status": "DYNAMIC", "set-cookie": "__cf_bm=4OrkqWAcsMgLtiB01X3LSmGfqUkRk99GpwZ8LZ8roY8-1751437383-*******-PD8t9A875z95LK24VGdwpVSy91RAzX_rVD3dM.o641QuiDOVd6XTMgkTVxDyyigBY.8FXtE7zlu1afOh0CSz7oRjeNcBcoKKHffkCjAzc2Y; path=/; expires=Wed, 02-Jul-25 06:53:03 GMT; domain=.sgp.pvp.net; HttpOnly; Secure; SameSite=None", "server": "cloudflare", "cf-ray": "958c0d5c5c00c7b4-DUS"}
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] [DEBUG] get_queue_token - Response Body: {"token": "eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.UtKrkIMptBul9WiH4KnMfGYQY0jmvYr9byLDOOqdzV3S92DACKyl0i-_mOdp0X0y8oZQlWi5rFfGAjsGEaznoJTEwUWRKKl7pQsVAZPpacdlL9R7r1wuHT7-Ciisok4e1NxUePSouAtQs-seCHzKUmlwTVGJTa0EkQuXlVx8AhpC0xu4LEF2m-1is0wzGo49W4531Jdc5g3J6QTQbkqJfFPycXJAhpN8xBgrsPQLZRus43Wf0aWOYOlOM19S0KVYRr9sKWKv6Za5gQIewzYrLn6e3pYFLRXhKa9-vnhzYOgNLXcaDDWKaixyi9EK5snj37DMzr7tfzdp1wX0CWIfJQ", "type": "LOGIN"}
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[93m!←[0m←[97m] Invalid JSON in userinfo for get_session_token: Expecting value: line 1 column 1 (char 0)
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Got JWT token from queue_token
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Parsed access token, PUUID: 0e4530dc-e6bd-533d-a26b-ae1fcfe817f8
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - URL: https://euc1-red.pp.sgp.pvp.net/session-external/v1/session/create
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Region: EUW, Region Upper: EUW1
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Player Platform Edge URL: https://euc1-red.pp.sgp.pvp.net
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Headers: {"Authorization": "Bearer eyJraWQiOiJrMSIsImFsZyI6IlJTMjU2In0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.UtKrkIMptBul9WiH4KnMfGYQY0jmvYr9byLDOOqdzV3S92DACKyl0i-_mOdp0X0y8oZQlWi5rFfGAjsGEaznoJTEwUWRKKl7pQsVAZPpacdlL9R7r1wuHT7-Ciisok4e1NxUePSouAtQs-seCHzKUmlwTVGJTa0EkQuXlVx8AhpC0xu4LEF2m-1is0wzGo49W4531Jdc5g3J6QTQbkqJfFPycXJAhpN8xBgrsPQLZRus43Wf0aWOYOlOM19S0KVYRr9sKWKv6Za5gQIewzYrLn6e3pYFLRXhKa9-vnhzYOgNLXcaDDWKaixyi9EK5snj37DMzr7tfzdp1wX0CWIfJQ", "Accept": "application/json", "Content-Type": "application/json"}
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Body: {"claims": {"cname": "lcu"}, "product": "lol", "puuid": "0e4530dc-e6bd-533d-a26b-ae1fcfe817f8", "region": "EUW1"}
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Status: 200
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Headers: {"date": "Wed, 02 Jul 2025 06:23:03 GMT", "content-type": "application/json;charset=utf-8", "transfer-encoding": "chunked", "connection": "keep-alive", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "expires": "0", "pragma": "no-cache", "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers, accept-encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-riot-edge-trace-id": "a8bae7b9-4246-45ee-97a3-b8cb6508ab78", "x-xss-protection": "0", "content-encoding": "gzip", "access-control-allow-origin": "*", "access-control-allow-methods": "GET, PUT, DELETE, POST, OPTIONS", "access-control-allow-headers": "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range", "access-control-expose-headers": "Content-Length,Content-Range", "cf-cache-status": "DYNAMIC", "server": "cloudflare", "cf-ray": "958c0d5cbc3dc7b4-DUS"}
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] [DEBUG] get_session_token - Response Text: "eyJraWQiOiJzMSIsImFsZyI6IlJTMjU2In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LoKKqhMBjAIhodb02X7tSaPqBooF0zHeqD-2gOW-XILA4SIVBeOuEzvsZn_8cJR9FwhBWuRZmq_Jn3sO5avzJ7YBrq-8bJ-jbzBI-wIntgSTrb2BWeKI51z_27IOG2m-tYpr722S1DcQk0OCVd7IAGee630i7b-gjADMpP_xI_SUoQuvz8Q_wAgSqeRu7-_zujw8p8dXFyv6-PDBYgP-HHIOOMifa5oQCTsg3ITj6MHbAbPk1cbUJH-FZeYyO4VMHdXXML5wDZ10SdW8LJXUXtn1RG4fQwDqf4o43-xA56HLRYa7Qp9lRArLlBDCqJV5CJszLamdz6CEB7yW_s722Q"
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Got all tokens for spanglebob
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Importing get_owned_champions for spanglebob
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Using existing region code for champion data retrieval: EUW for spanglebob
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Calling get_owned_champions for spanglebob with region EUW
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Getting owned champions for region: EUW
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Headers set up: Authorization and Entitlements tokens configured
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] TCP Connector created with IPv4 family and SSL disabled
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Trying URL pattern 1/4
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Using base URL: https://euw-red.lol.sgp.pvp.net
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Inventory URL: https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v2/inventoriesWithLoyalty
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Wallet URL: https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v1/walletsbalances
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Location format: lolriot.aws-euc1-prod.euw
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Inventory params: {'puuid': '', 'accountId': '', 'inventoryTypes': 'CHAMPION', 'location': 'lolriot.aws-euc1-prod.euw'}
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Attempting to resolve hostname: euw-red.lol.sgp.pvp.net
←[0m←[97m[2025-07-02 08:23:02] [←[0m←[92m$←[0m←[97m] Successfully resolved euw-red.lol.sgp.pvp.net to *************
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Successfully retrieved inventory data with pattern 1
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Found 41 owned champions
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Requesting wallet data from: https://euw-red.lol.sgp.pvp.net/lolinventoryservice-ledge/v1/walletsbalances
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Wallet params: {'puuid': '', 'location': 'lolriot.aws-euc1-prod.euw', 'accountId': '*********', 'currencyTypes': ['RP', 'lol_blue_essence', 'tft_star_fragments']}
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Successfully retrieved wallet data with pattern 1
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Successfully retrieved all data with pattern 1
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[90m@←[0m←[97m] Session closed properly
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Returned from get_owned_champions for spanglebob
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Saving comprehensive account info for spanglebob: BE=5705, RP=0, Level=0
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Saving 41 champions for spanglebob
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Getting skin data for spanglebob
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Using existing region code for skin data retrieval: EUW for spanglebob
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Getting owned skins for account
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Restriction QUEUE_LOCKOUT for spanglebob is temporary, expires at *************
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Dodge timer found for spanglebob: QUEUE_LOCKOUT - QUEUE_DODGE
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[93m!←[0m←[97m] Found ban info: QUEUE_LOCKOUT - QUEUE_DODGE
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Account spanglebob has temporary chat restrictions: QUEUE_LOCKOUT - QUEUE_DODGE
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[93m!←[0m←[97m] JWT payload had invalid UTF-8 bytes, some data may be lost
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[91m-←[0m←[97m] Error decoding JWT payload: Invalid control character at: line 1 column 1248 (char 1247)
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[91m-←[0m←[97m] Could not find account for puuid
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[93m!←[0m←[97m] No skins to save for spanglebob
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Got skin data for spanglebob
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Checking penalties for spanglebob
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Checking penalties for EUW using URL: https://euw-red.lol.sgp.pvp.net/leaverbuster-ledge/restrictionInfo
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Penalty check response: {'puuid': '0e4530dc-e6bd-533d-a26b-ae1fcfe817f8', 'rankedRestrictionEntryDto': {'puuid': '0e4530dc-e6bd-533d-a26b-ae1fcfe817f8', 'version': 7, 'restrictedGamesRemaining': 0, 'restrictedGamesOriginal': 0, 'rankedRestrictionPenaltyId': '309002a1-3017-11ef-ac90-37302662bd49', 'punishmentIncurredGameId': **********, 'punishmentIncurredTimeMillis': *************, 'rankedRestrictionAckNeeded': False, 'penaltyOrigin': 'PDS'}, 'leaverBusterEntryDto': {'puuid': '0e4530dc-e6bd-533d-a26b-ae1fcfe817f8', 'version': 88, 'tainted': True, 'preLockoutAckNeeded': False, 'onLockoutAckNeeded': False, 'leaverScore': 380, 'leaverLevel': 0, 'punishedGamesRemaining': 0, 'currentPunishmentStep': 0, 'leaverPenalty': {'puuid': '0e4530dc-e6bd-533d-a26b-ae1fcfe817f8', 'hasActivePenalty': False, 'delayTime': 0, 'queueLockoutTimerExpiryUtcMillis': 0, 'punishmentTimerType': 'NO_PENALTY', 'rankRestrictedGamesRemaining': 0, 'rankRestrictedTimerExpiryUtcMillis': 0, 'rankRestricted': False}, 'warnSentMillis': 1480470082429, 'warnAckedMillis': 1502533621262, 'lastUpdatedMillis': 1748903308373, 'totalPunishedGamesPlayed': 37, 'lastBustedGameId': 7420410111, 'lastBustedTimeMillis': 1748894191565, 'lastPunishmentIncurredGameId': 7420410111, 'lastPunishmentIncurredTimeMillis': 1748894191565, 'processedGameIdHistoryString': '7414457377,7414478966,7414496771,7414520627,7420100846,7420129683,7420171877,7420205691,7420238414,7420284292,7420410111,7420430755,7420471057,7420524493,7420570609'}}
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] No active penalty
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Got penalty info for spanglebob: 0 minutes, 0 games
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Checking rank for spanglebob#
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Using region code EUW for rank info retrieval
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[90m@←[0m←[97m] get_rank_info called with region_code: EUW, type: <class 'str'>
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[90m@←[0m←[97m] Getting rank info for spanglebob# in region EUW
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[93m!←[0m←[97m] JWT payload had invalid UTF-8 bytes, some data may be lost
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[91m-←[0m←[97m] Error decoding JWT payload: Invalid control character at: line 1 column 1248 (char 1247)
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Decoded userinfo: {}
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] No country found in userinfo
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Current rank data response: {'queues': [{'queueType': 'RANKED_SOLO_5x5', 'provisionalGameThreshold': 5, 'tier': 'SILVER', 'rank': 'II', 'leaguePoints': -17, 'cumulativeLp': 983, 'wins': 28, 'losses': 7, 'provisionalGamesRemaining': 0, 'highestTier': 'SILVER', 'highestRank': 'II', 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_FLEX_SR', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_TFT', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_TFT_TURBO', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}, {'queueType': 'RANKED_TFT_DOUBLE_UP', 'provisionalGameThreshold': 5, 'leaguePoints': 0, 'cumulativeLp': 0, 'wins': 0, 'losses': 0, 'provisionalGamesRemaining': 0, 'ratedRating': 0, 'premadeMmrRestricted': False}], 'earnedRegaliaRewardIds': [], 'splitsProgress': {}, 'seasons': {'RANKED_TFT': {'currentSeasonId': 29, 'currentSeasonEnd': 1753848001000, 'nextSeasonStart': 0}, 'RANKED_TFT_TURBO': {'currentSeasonId': 29, 'currentSeasonEnd': 1753848001000, 'nextSeasonStart': 0}, 'RANKED_FLEX_SR': {'currentSeasonId': 19, 'currentSeasonEnd': 1756249199000, 'nextSeasonStart': 0}, 'RANKED_TFT_DOUBLE_UP': {'currentSeasonId': 29, 'currentSeasonEnd': 1753848001000, 'nextSeasonStart': 0}, 'RANKED_SOLO_5x5': {'currentSeasonId': 19, 'currentSeasonEnd': 1756249199000, 'nextSeasonStart': 0}}, 'jwt': '*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'}
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Processed rank info: {'solo': {'tier': 'SILVER', 'division': 'II', 'lp': -17, 'wins': 28, 'losses': 7, 'previous_tier': 'UNRANKED', 'previous_division': ''}, 'flex': {'tier': 'UNRANKED', 'division': '', 'lp': 0, 'wins': 0, 'losses': 0, 'previous_tier': 'UNRANKED', 'previous_division': ''}, 'country': None, 'creation_region': ''}
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Got rank info for spanglebob
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Saving rank info for spanglebob: Solo=SILVER II, Flex=UNRANKED
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Successfully rechecked account: spanglebob
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Loading account spanglebob from database
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Account details: BE=5705, RP=0, Level=0
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Account info: created_at=None, creation_region=EUW, country=
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Game info: game_name=spanglebob, tag_line=
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Checking JWT token for region information for spanglebob
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] JWT region debugging for spanglebob:
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m]   lol field: [{'cuid': *********, 'cpid': 'EUW1', 'uid': *********, 'uname': 'spanglebob', 'ptrid': None, 'pid': 'EUW1', 'state': 'ENABLED'}]
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m]   region field: {}
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m]   original_platform_id:
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m]   lol_region: [{'active': True, 'cpid': 'EUW1', 'cuid': *********, 'lp': False, 'pid': 'EUW1', 'uid': *********}]
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m]   dat field: {}
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Using lol_region[].cpid to determine region for spanglebob: EUW
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Found region in JWT for spanglebob: EUW
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Loaded 0 season history entries for spanglebob
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Using region=EUW, region_code=EUW for account spanglebob
←[0m←[97m[2025-07-02 08:23:03] [←[0m←[92m$←[0m←[97m] Loaded 1 accounts from database
←[0mDEBUG: PyQtBridge.handle_account_selection called with index: 0
DEBUG: Updated PyQtBridge.selected_account_index to: 0
DEBUG: Updated main_window.selected_account_index via set_selected_account_index
