import asyncio
import traceback
from custom_logger.logger import logger

class DBWrapper:
    """Wrapper around the existing DatabaseManager to provide async methods"""
    
    def __init__(self, db_manager=None):
        """Initialize with an existing database manager or create a new one"""
        if db_manager:
            self.db_manager = db_manager
        else:
            # Import here to avoid circular imports
            from database.manager import DatabaseManager
            self.db_manager = DatabaseManager()
    
    async def execute_query(self, query, params=None):
        """Execute a query and return the results as a list of dictionaries"""
        try:
            logger.info(f"Executing query: {query}")
            
            # Ensure database is initialized
            await self.db_manager.init_db()
            
            # Execute query using the underlying database manager
            cursor = await self.db_manager.db.execute(query, params or ())
            
            # Get column names
            column_names = [description[0] for description in cursor.description] if cursor.description else []
            
            # Fetch results
            rows = await cursor.fetchall()
            
            # Convert results to list of dictionaries
            results = []
            for row in rows:
                result = {}
                for i, value in enumerate(row):
                    if i < len(column_names):
                        result[column_names[i]] = value
                results.append(result)
            
            return results
        except Exception as e:
            logger.error(f"Error executing query: {str(e)}")
            logger.error(traceback.format_exc())
            return []
    
    async def commit(self):
        """Commit changes to the database"""
        try:
            await self.db_manager.db.commit()
            return True
        except Exception as e:
            logger.error(f"Error committing changes: {str(e)}")
            return False
    
    async def close(self):
        """Close the database connection"""
        try:
            await self.db_manager.db.close()
            return True
        except Exception as e:
            logger.error(f"Error closing database: {str(e)}")
            return False 