import json
import os
from PyQt6.QtWidgets import (Q<PERSON>ialog, Q<PERSON>oxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QCheckBox, QSpinBox,
                            QGroupBox, QFormLayout, QMessageBox, Q<PERSON>abWidget, QWidget)
from PyQt6.QtCore import Qt

class SettingsDialog(QDialog):
    """Settings dialog for League Account Manager"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Settings")
        self.setModal(True)
        self.setFixedSize(500, 400)
        
        # Load current settings
        self.settings = self.load_settings()
        
        # Apply League of Legends theme
        self.apply_theme()
        
        self.init_ui()
    
    def apply_theme(self):
        """Apply League of Legends dark theme to the dialog"""
        self.setStyleSheet("""
            QDialog {
                background-color: #0A1428;
                color: #F0E6D2;
                font-family: <PERSON><PERSON>, sans-serif;
            }
            
            QTabWidget {
                background-color: #0A1428;
                border: none;
            }
            
            QTabWidget::pane {
                border: 1px solid #785A28;
                background-color: rgba(9, 20, 40, 0.9);
                border-radius: 4px;
            }
            
            QTabBar::tab {
                background-color: #1E2328;
                color: #F0E6D2;
                border: 1px solid #785A28;
                padding: 8px 16px;
                margin-right: 2px;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            
            QTabBar::tab:selected {
                background-color: #C89B3C;
                color: #0A1428;
                border-color: #C89B3C;
                font-weight: bold;
            }
            
            QTabBar::tab:hover:!selected {
                background-color: #2A2F35;
                color: #C89B3C;
            }
            
            QGroupBox {
                background-color: rgba(30, 35, 40, 0.5);
                border: 1px solid #785A28;
                border-radius: 6px;
                margin-top: 10px;
                padding-top: 10px;
                color: #F0E6D2;
                font-weight: bold;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                color: #C89B3C;
                font-weight: bold;
                font-size: 14px;
                padding: 0 5px;
            }
            
            QLabel {
                color: #F0E6D2;
                font-size: 12px;
            }
            
            QCheckBox {
                color: #F0E6D2;
                spacing: 8px;
            }
            
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 2px solid #785A28;
                border-radius: 3px;
                background-color: #1E2328;
            }
            
            QCheckBox::indicator:checked {
                background-color: #C89B3C;
                border-color: #C89B3C;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xMC42IDEuNEw0LjMgNy43TDEuNCA0LjgiIHN0cm9rZT0iIzBBMTQyOCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
            }
            
            QCheckBox::indicator:hover {
                border-color: #C89B3C;
            }
            
            QSpinBox {
                background-color: #1E2328;
                border: 1px solid #785A28;
                border-radius: 4px;
                padding: 6px;
                color: #F0E6D2;
                font-size: 12px;
                min-width: 80px;
            }
            
            QSpinBox:focus {
                border-color: #C89B3C;
                outline: none;
            }
            
            QSpinBox::up-button, QSpinBox::down-button {
                background-color: #2A2F35;
                border: 1px solid #785A28;
                border-radius: 2px;
                width: 18px;
            }
            
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background-color: #C89B3C;
                border-color: #C89B3C;
            }
            
            QSpinBox::up-arrow {
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iNiIgdmlld0JveD0iMCAwIDEwIDYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik05IDVMMSAxTDMgMyIgc3Ryb2tlPSIjRjBFNkQyIiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPg==);
            }
            
            QSpinBox::down-arrow {
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iNiIgdmlld0JveD0iMCAwIDEwIDYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMOSA1TDcgMyIgc3Ryb2tlPSIjRjBFNkQyIiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPg==);
            }
            
            QLineEdit {
                background-color: #1E2328;
                border: 1px solid #785A28;
                border-radius: 4px;
                padding: 6px;
                color: #F0E6D2;
                font-size: 12px;
                min-height: 20px;
            }
            
            QLineEdit:focus {
                border-color: #C89B3C;
                outline: none;
            }
            
            QLineEdit::placeholder {
                color: #A8A8A8;
            }
            
            QPushButton {
                background-color: #1E2328;
                color: #C89B3C;
                border: 1px solid #785A28;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
                min-width: 80px;
            }
            
            QPushButton:hover {
                background-color: #2A2F35;
                border-color: #C89B3C;
                color: #C89B3C;
            }
            
            QPushButton:pressed {
                background-color: #C89B3C;
                color: #0A1428;
                border-color: #C89B3C;
            }
            
            QPushButton:default {
                background-color: #C89B3C;
                color: #0A1428;
                border-color: #C89B3C;
            }
            
            QPushButton:default:hover {
                background-color: #D4A853;
                border-color: #D4A853;
            }
            
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            
            QScrollBar:vertical {
                background-color: rgba(9, 20, 40, 0.5);
                width: 10px;
                border-radius: 5px;
            }
            
            QScrollBar::handle:vertical {
                background-color: rgba(200, 155, 60, 0.5);
                border-radius: 5px;
                min-height: 20px;
            }
            
            QScrollBar::handle:vertical:hover {
                background-color: rgba(200, 155, 60, 0.7);
            }
            
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)
        
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout()
        
        # Create tab widget
        tab_widget = QTabWidget()
        
        # General settings tab
        general_tab = QWidget()
        general_layout = QVBoxLayout()
        
        # General settings group
        general_group = QGroupBox("General Settings")
        general_form = QFormLayout()
        
        # Auto-check on import
        self.auto_check_checkbox = QCheckBox()
        self.auto_check_checkbox.setChecked(self.settings.get('auto_check_on_import', False))
        general_form.addRow("Auto-check accounts after import:", self.auto_check_checkbox)
        
        # Save logs
        self.save_logs_checkbox = QCheckBox()
        self.save_logs_checkbox.setChecked(self.settings.get('save_logs', True))
        general_form.addRow("Save logs to file:", self.save_logs_checkbox)
        
        # Max concurrent checks
        self.max_concurrent_spinbox = QSpinBox()
        self.max_concurrent_spinbox.setRange(1, 20)
        self.max_concurrent_spinbox.setValue(self.settings.get('max_concurrent_checks', 5))
        general_form.addRow("Max concurrent account checks:", self.max_concurrent_spinbox)
        
        general_group.setLayout(general_form)
        general_layout.addWidget(general_group)
        
        # Verification settings group
        verification_group = QGroupBox("Account Verification")
        verification_form = QFormLayout()
        
        # Check champions
        self.check_champions_checkbox = QCheckBox()
        self.check_champions_checkbox.setChecked(self.settings.get('check_champions', True))
        verification_form.addRow("Check owned champions:", self.check_champions_checkbox)
        
        # Check skins
        self.check_skins_checkbox = QCheckBox()
        self.check_skins_checkbox.setChecked(self.settings.get('check_skins', True))
        verification_form.addRow("Check owned skins:", self.check_skins_checkbox)
        
        # Check rank
        self.check_rank_checkbox = QCheckBox()
        self.check_rank_checkbox.setChecked(self.settings.get('check_rank', True))
        verification_form.addRow("Check rank information:", self.check_rank_checkbox)
        
        verification_group.setLayout(verification_form)
        general_layout.addWidget(verification_group)
        
        general_layout.addStretch()
        general_tab.setLayout(general_layout)
        tab_widget.addTab(general_tab, "General")
        
        # Advanced settings tab
        advanced_tab = QWidget()
        advanced_layout = QVBoxLayout()
        
        # Network settings group
        network_group = QGroupBox("Network Settings")
        network_form = QFormLayout()
        
        # Request timeout
        self.timeout_spinbox = QSpinBox()
        self.timeout_spinbox.setRange(5, 60)
        self.timeout_spinbox.setValue(self.settings.get('request_timeout', 30))
        self.timeout_spinbox.setSuffix(" seconds")
        network_form.addRow("Request timeout:", self.timeout_spinbox)
        
        # Retry attempts
        self.retry_spinbox = QSpinBox()
        self.retry_spinbox.setRange(1, 10)
        self.retry_spinbox.setValue(self.settings.get('retry_attempts', 3))
        network_form.addRow("Retry attempts:", self.retry_spinbox)
        
        # Use proxy
        self.use_proxy_checkbox = QCheckBox()
        self.use_proxy_checkbox.setChecked(self.settings.get('use_proxy', False))
        network_form.addRow("Use proxy:", self.use_proxy_checkbox)
        
        # Proxy URL
        self.proxy_url_edit = QLineEdit()
        self.proxy_url_edit.setText(self.settings.get('proxy_url', ''))
        self.proxy_url_edit.setPlaceholderText("http://proxy:port")
        network_form.addRow("Proxy URL:", self.proxy_url_edit)
        
        network_group.setLayout(network_form)
        advanced_layout.addWidget(network_group)
        
        advanced_layout.addStretch()
        advanced_tab.setLayout(advanced_layout)
        tab_widget.addTab(advanced_tab, "Advanced")
        
        layout.addWidget(tab_widget)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        # Reset to defaults button
        reset_button = QPushButton("Reset to Defaults")
        reset_button.clicked.connect(self.reset_to_defaults)
        button_layout.addWidget(reset_button)
        
        button_layout.addStretch()
        
        # Cancel button
        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)
        
        # Save button
        save_button = QPushButton("Save")
        save_button.clicked.connect(self.save_settings_and_close)
        save_button.setDefault(True)
        button_layout.addWidget(save_button)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def load_settings(self):
        """Load settings from file"""
        try:
            if os.path.exists('settings.json'):
                with open('settings.json', 'r') as f:
                    return json.load(f)
            else:
                return self.get_default_settings()
        except Exception as e:
            print(f"Error loading settings: {e}")
            return self.get_default_settings()
    
    def get_default_settings(self):
        """Get default settings"""
        return {
            'auto_check_on_import': False,
            'save_logs': True,
            'max_concurrent_checks': 5,
            'check_champions': True,
            'check_skins': True,
            'check_rank': True,
            'request_timeout': 30,
            'retry_attempts': 3,
            'use_proxy': False,
            'proxy_url': ''
        }
    
    def reset_to_defaults(self):
        """Reset all settings to defaults"""
        reply = QMessageBox.question(
            self,
            "Reset Settings",
            "Are you sure you want to reset all settings to their default values?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            defaults = self.get_default_settings()
            
            # Update UI with defaults
            self.auto_check_checkbox.setChecked(defaults['auto_check_on_import'])
            self.save_logs_checkbox.setChecked(defaults['save_logs'])
            self.max_concurrent_spinbox.setValue(defaults['max_concurrent_checks'])
            self.check_champions_checkbox.setChecked(defaults['check_champions'])
            self.check_skins_checkbox.setChecked(defaults['check_skins'])
            self.check_rank_checkbox.setChecked(defaults['check_rank'])
            self.timeout_spinbox.setValue(defaults['request_timeout'])
            self.retry_spinbox.setValue(defaults['retry_attempts'])
            self.use_proxy_checkbox.setChecked(defaults['use_proxy'])
            self.proxy_url_edit.setText(defaults['proxy_url'])
    
    def save_settings_and_close(self):
        """Save settings and close dialog"""
        try:
            # Collect settings from UI
            settings = {
                'auto_check_on_import': self.auto_check_checkbox.isChecked(),
                'save_logs': self.save_logs_checkbox.isChecked(),
                'max_concurrent_checks': self.max_concurrent_spinbox.value(),
                'check_champions': self.check_champions_checkbox.isChecked(),
                'check_skins': self.check_skins_checkbox.isChecked(),
                'check_rank': self.check_rank_checkbox.isChecked(),
                'request_timeout': self.timeout_spinbox.value(),
                'retry_attempts': self.retry_spinbox.value(),
                'use_proxy': self.use_proxy_checkbox.isChecked(),
                'proxy_url': self.proxy_url_edit.text().strip()
            }
            
            # Save to file
            with open('settings.json', 'w') as f:
                json.dump(settings, f, indent=4)
            
            QMessageBox.information(
                self,
                "Settings Saved",
                "Settings have been saved successfully."
            )
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error Saving Settings",
                f"An error occurred while saving settings:\n{str(e)}"
            ) 