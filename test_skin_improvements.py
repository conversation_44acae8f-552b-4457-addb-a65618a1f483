#!/usr/bin/env python3
"""
Test script for the improved skin/chroma detection system.
This script tests the new categorization system with known problematic cases.
"""

import sys
import os
import json

# Add the accounts directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'accounts'))

from account_pool import Account<PERSON>ool

def test_victorious_anivia_chromas():
    """Test the Victorious Anivia chroma detection specifically"""
    print("=== Testing Victorious Anivia Chromas ===")
    
    # Create an AccountPool instance to access the skin methods
    pool = AccountPool()
    
    # Test cases: Victorious Anivia and its chromas
    test_cases = [
        34046,  # Victorious Anivia (main skin)
        34047,  # Bronze chroma
        34048,  # Silver chroma  
        34049,  # Gold chroma
        34050,  # Platinum chroma
        34051,  # Diamond chroma
        34052,  # Master chroma
        34053,  # Grandmaster chroma
        34054,  # Challenger chroma
    ]
    
    for skin_id in test_cases:
        print(f"\n--- Testing Skin ID: {skin_id} ---")
        
        # Test the new categorization system
        category = pool._categorize_skin(skin_id)
        print(f"Category: {category}")
        
        # Test the skin info retrieval
        skin_info = pool._get_skin_info(skin_id)
        print(f"Skin Info: {skin_info}")
        
        # Test chroma detection
        is_chroma = pool._is_chroma(skin_info['name'], skin_id)
        print(f"Is Chroma: {is_chroma}")
        
        print("-" * 50)

def test_other_problematic_skins():
    """Test other skins that might have similar issues"""
    print("\n=== Testing Other Problematic Skins ===")
    
    pool = AccountPool()
    
    # Test some other skins with chromas
    test_cases = [
        # Annie skins with chromas
        1013,  # Lunar Beast Annie (main)
        1014,  # Lunar Beast Annie chroma
        
        # Some random high-ID skins that might be misidentified
        98051,  # From the log file
        80004,  # From the log file
        107015, # From the log file
    ]
    
    for skin_id in test_cases:
        print(f"\n--- Testing Skin ID: {skin_id} ---")
        
        try:
            category = pool._categorize_skin(skin_id)
            print(f"Category: {category}")
            
            skin_info = pool._get_skin_info(skin_id)
            print(f"Skin Info: {skin_info}")
            
            is_chroma = pool._is_chroma(skin_info['name'], skin_id)
            print(f"Is Chroma: {is_chroma}")
        except Exception as e:
            print(f"Error testing skin {skin_id}: {e}")
        
        print("-" * 50)

def test_rank_extraction():
    """Test the rank extraction for Victorious chromas"""
    print("\n=== Testing Rank Extraction ===")
    
    pool = AccountPool()
    
    # Test the rank extraction function directly
    test_descriptions = [
        "This Chroma was awarded to players who accumulated at least 1600 Split Points and reached Bronze rank in any Summoner's Rift ranked queue in Season 2023 - Split 1",
        "This Chroma was awarded to players who accumulated at least 1600 Split Points and reached Silver rank in any Summoner's Rift ranked queue in Season 2023 - Split 1",
        "This Chroma was awarded to players who accumulated at least 80 Split Points and reached Gold rank in any Summoner's Rift ranked queue in Season 2023 - Split 1",
        "This Chroma was awarded to players who accumulated at least 80 Split Points and reached Platinum rank in any Summoner's Rift ranked queue in Season 2023 - Split 1",
        "This Chroma was awarded to players who accumulated at least 80 Split Points and reached Diamond rank in any Summoner's Rift ranked queue in Season 2023 - Split 1",
        "This Chroma was awarded to players who accumulated at least 80 Split Points and reached Master rank in any Summoner's Rift ranked queue in Season 2023 - Split 1",
        "This Chroma was awarded to players who accumulated at least 80 Split Points and reached Grandmaster rank in any Summoner's Rift ranked queue in Season 2023 - Split 1",
        "This Chroma was awarded to players who accumulated at least 80 Split Points and reached Challenger rank in any Summoner's Rift ranked queue in Season 2023 - Split 1",
    ]
    
    skin_ids = [34047, 34048, 34049, 34050, 34051, 34052, 34053, 34054]
    
    for i, (skin_id, description) in enumerate(zip(skin_ids, test_descriptions)):
        print(f"\nTesting Skin ID {skin_id}:")
        print(f"Description: {description[:80]}...")
        
        rank = pool._extract_rank_from_victorious_chroma(skin_id, description)
        print(f"Extracted Rank: {rank}")
        
        short_desc = pool._extract_short_chroma_description(description)
        print(f"Short Description: {short_desc}")

if __name__ == "__main__":
    print("Testing Improved Skin/Chroma Detection System")
    print("=" * 60)
    
    try:
        test_victorious_anivia_chromas()
        test_other_problematic_skins()
        test_rank_extraction()
        
        print("\n" + "=" * 60)
        print("Testing completed!")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
